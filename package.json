{"name": "hire_chef_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@date-io/date-fns": "^2.17.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.9.0", "@mhoc/axios-digest-auth": "^0.8.0", "@mui/icons-material": "^5.16.7", "@mui/joy": "^5.0.0-beta.48", "@mui/material": "^5.16.7", "@mui/x-charts": "^7.14.0", "@mui/x-data-grid": "^7.18.0", "@mui/x-date-pickers": "^6.20.2", "@reduxjs/toolkit": "^2.2.7", "@toolpad/core": "^0.9.0", "axios": "^1.7.7", "axios-digest": "^0.3.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "framer-motion": "^12.11.4", "http-proxy-middleware": "^3.0.3", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.3", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-icons": "^5.3.0", "react-loading-indicators": "^1.0.0", "react-query": "^3.39.3", "react-redux": "^9.1.2", "react-router-dom": "^6.26.1", "react-toastify": "^10.0.5", "react-window": "^1.8.10", "react-window-infinite-loader": "^1.0.9", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "simplebar-react": "^3.2.6", "sweetalert2": "^11.21.0", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "vite": "^5.3.4"}}