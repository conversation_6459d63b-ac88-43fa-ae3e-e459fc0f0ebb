import React, { useState, useEffect } from "react";
import "./App.css";
import ThemeProvider from "./theme";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Router from "./routes/section";
import store, { persistor } from "./app/store";
import { ThreeDot } from "react-loading-indicators";
import { Box } from "@mui/material";

function App() {
  const [isReady, setIsReady] = useState(false);

  const LoadingFallback = () => (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      height="100vh"
    >
      <ThreeDot
        variant="pulsate"
        color="#E16969"
        size="medium"
        text=""
        textColor=""
      />
    </Box>
  );

  return (
    <Provider store={store}>
      {/* <PersistGate loading={<LoadingFallback />}> */}
        <ThemeProvider>
          <Router />
          <ToastContainer position="bottom-right" stacked />
        </ThemeProvider>
      {/* </PersistGate> */}
      {/* <PersistGate 
        loading={<LoadingFallback />} 
        persistor={persistor}
        onBeforeLift={() => {
          
          setIsReady(true);
          console.log("Start")
        }}
      >
        {() => (
          isReady ? (
            <ThemeProvider>
              <Router />
              <ToastContainer position="bottom-right" stacked />
            </ThemeProvider>
          ) : (
            <LoadingFallback />
          )
        )}
      </PersistGate> */}
    </Provider>
  );
}

export default App;
