export const withdrawalRequestsData = [
  {
    id: 1,
    date: "2024-06-15T14:30:00Z",
    amount: 2500.00,
    chefName: "<PERSON>",
    requestStatus: "Pending",
    chefId: "chef_001",
    bankAccount: "****-1234",
    description: "Monthly earnings withdrawal"
  },
  {
    id: 2,
    date: "2024-06-14T09:15:00Z",
    amount: 1800.50,
    chefName: "Julia Child",
    requestStatus: "Pending",
    chefId: "chef_002",
    bankAccount: "****-5678",
    description: "Weekly commission withdrawal"
  },
  {
    id: 3,
    date: "2024-06-13T16:45:00Z",
    amount: 3200.75,
    chefName: "Anthony Bo<PERSON>dain",
    requestStatus: "Approved",
    chefId: "chef_003",
    bankAccount: "****-9012",
    description: "Bonus payment withdrawal"
  },
  {
    id: 4,
    date: "2024-06-12T11:20:00Z",
    amount: 950.25,
    chefName: "<PERSON>",
    requestStatus: "Pending",
    chefId: "chef_004",
    bankAccount: "****-3456",
    description: "Event earnings withdrawal"
  },
  {
    id: 5,
    date: "2024-06-11T13:30:00Z",
    amount: 4100.00,
    chefName: "Emeril Lagasse",
    requestStatus: "Pending",
    chefId: "chef_005",
    bankAccount: "****-7890",
    description: "Monthly subscription earnings"
  },
  {
    id: 6,
    date: "2024-06-10T10:15:00Z",
    amount: 1650.80,
    chefName: "Rachel Ray",
    requestStatus: "Approved",
    chefId: "chef_006",
    bankAccount: "****-2468",
    description: "Recipe sales commission"
  },
  {
    id: 7,
    date: "2024-06-09T15:45:00Z",
    amount: 2800.90,
    chefName: "Bobby Flay",
    requestStatus: "Pending",
    chefId: "chef_007",
    bankAccount: "****-1357",
    description: "Cooking class earnings"
  },
  {
    id: 8,
    date: "2024-06-08T12:00:00Z",
    amount: 3750.25,
    chefName: "Ina Garten",
    requestStatus: "Pending",
    chefId: "chef_008",
    bankAccount: "****-9753",
    description: "Private dining service"
  },
  {
    id: 9,
    date: "2024-06-07T08:30:00Z",
    amount: 1200.60,
    chefName: "Thomas Keller",
    requestStatus: "Approved",
    chefId: "chef_009",
    bankAccount: "****-8642",
    description: "Consultation fees"
  },
  {
    id: 10,
    date: "2024-06-06T17:20:00Z",
    amount: 5200.00,
    chefName: "Wolfgang Puck",
    requestStatus: "Pending",
    chefId: "chef_010",
    bankAccount: "****-1928",
    description: "Restaurant partnership earnings"
  }
];