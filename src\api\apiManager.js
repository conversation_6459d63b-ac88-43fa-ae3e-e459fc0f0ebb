import axios from 'axios';
import store from '../app/store';
import { setCredentials, logOut } from '../reducers/auth.reducer';

const baseURL = import.meta.env.VITE_BASE_URL;

const axiosInstance = axios.create({
    baseURL,
    headers: {
        'Content-Type': 'application/json',
    },
});

axiosInstance.interceptors.request.use(
    (config) => {
        const token = store.getState().auth.token;
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
    },
    (error) => Promise.reject(error)
);

axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;

        if (error.response.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            try {
                const refreshToken = store.getState().auth.refreshToken;
                const response = await axios.post(`${baseURL}/refresh-token`, { refreshToken });
                const { token } = response.data;

                store.dispatch(setCredentials({ token }));

                originalRequest.headers['Authorization'] = `Bearer ${token}`;
                return axiosInstance(originalRequest);
            } catch (refreshError) {
                store.dispatch(logOut());
                return Promise.reject(refreshError);
            }
        }

        return Promise.reject(error);
    }
);

export const apiPostFormData = (url, formData, config = {}) => {
    const axiosInstance = axios.create({
        baseURL,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });

    return axiosInstance.post(url, formData, config);
};

export const apiGet = (url, config = {}) => axiosInstance.get(url, config);
export const apiPost = (url, data, config = {}) => axiosInstance.post(url, data, config);
export const apiPut = (url, data, config = {}) => axiosInstance.put(url, data, config);
export const apiPatch = (url, data, config = {}) => axiosInstance.patch(url, data, config);
export const apiDelete = (url, config = {}) => axiosInstance.delete(url, config);