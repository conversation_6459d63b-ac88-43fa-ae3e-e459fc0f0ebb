import { apiGet, apiPatch, apiPost } from '../../api/apiManager';

export const liveAttendance =async(attendanceData)=>{
    try {
        const response = await apiPost('/api/attendance/live', attendanceData);
        return response.data;
    } catch (error) {
        console.error('Error saving attendance:', error);
        throw error;
    }
}

export const getAllAttendance = async ()=>{
    try {
        const response = await apiGet('/api/attendance/all');
        return response.data;
    } catch (error) {
        console.error('Error fetching attendance:', error);
        throw error;
    }
}