import { apiGet, apiPatch, apiPost } from '../../api/apiManager';
import store from '../store';
import { setCredentials } from '../../reducers/auth.reducer';

export const loginUser = async (username, password) => {
    if (!username || !password) {
        return { success: false, error: 'Username and password are required' };
    }

    try {
        const response = await apiPost('/api/user/auth/login-user', { username, password });

        if (response.data.responseCode === 1000) {
            const { user, token, refresh, admin, customer, chef } = response.data.data;

            store.dispatch(setCredentials({
                user,
                token,
                refreshToken: refresh,
                admin,
                customer,
                chef,
                userType: admin ? 'Admin' : customer ? 'Customer' : "Chef", // Determine user type
            }));

            return { success: true, message: response.data.message, userType: admin ? 'Admin' : customer ? 'Customer' : "Chef" };
        } else {
            return { success: false, error: response.data.message };
        }
    } catch (error) {
        if (error.response && error.response.data) {
            return { success: false, error: error.response.data.message };
        } else {
            return { success: false, error: '<PERSON><PERSON> failed. Please try again.' };
        }
    }
}

export const getAllUsers = async () => {
    try {
        const response = await apiGet("/api/user/get-all-users");
        return response.data;
    } catch (error) {
        console.error('Error fetching users:', error);
        throw error;
    }
}

export const getAllUserTypes = async () => {
    try {
        const response = await apiGet("/api/user/get-all-user-types");
        return response.data;
    } catch (error) {
        console.error('Error fetching user types:', error);
        throw error;
    }
}

export const saveUser = async (expense) => {
    try {
        const response = await apiPost('/api/user/add-new-user', expense);
        return response.data;
    } catch (error) {
        console.error('Error saving expense:', error);
        throw error;
    }
}
export const saveAdmin = async (admin) => {
    try {
        const response = await apiPost('/api/user/save-admin', admin);
        return response.data;
    } catch (error) {
        console.error('Error saving user:', error);
        throw error;
    }
}
export const saveBranchUser = async (branchUser) => {
    try {
        const response = await apiPost('/api/user/save-branch-user', branchUser);
        return response.data;
    } catch (error) {
        console.error('Error saving user:', error);
        throw error;
    }
}
export const saveHoUser = async (hoUser) => {
    try {
        const response = await apiPost('/api/user/save-ho-user', hoUser);
        return response.data;
    } catch (error) {
        console.error('Error saving user:', error);
        throw error;
    }
}

export const getUserById = async (userId) => {
    try {
        const response = await apiGet(`/api/user/get-user-by-id/${userId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching user by id:', error);
        throw error;
    }
}


export const updateLoginUser = async (endpoints, profileData) => {
    try {
        const response = await apiPatch(endpoints, profileData);
        return response.data;
    } catch (error) {
        console.error('Error updating user:', error);
        throw error;
    }
}

export const saveCustomer= async (customer) => {
    try {
        const response = await apiPost('/api/user/save-customer', customer);
        if (response.data.responseCode === 1000) {
            const { user, token, refresh, admin, customer, chef } = response.data.data;

            store.dispatch(setCredentials({
                user,
                token,
                refreshToken: refresh,
                admin,
                customer,
                chef,
                userType: admin ? 'Admin' : customer ? 'Customer' : "Chef", // Determine user type
            }));

            return { success: true, message: response.data.message, userType: admin ? 'Admin' : customer ? 'Customer' : "Chef" };
        } else {
            return { success: false, error: response.data.message };
        }
        
    } catch (error) {
        if (error.response && error.response.data) {
            return { success: false, error: error.response.data.message };
        } else {
            return { success: false, error: 'Saving failed. Please try again.' };
        }
    }
}
export const saveChef= async (chef) => {
    try {
        const response = await apiPost('/api/user/save-chef', chef);
        if (response.data.responseCode === 1000) {
            const { user, token, refresh, admin, customer, chef } = response.data.data;

            store.dispatch(setCredentials({
                user,
                token,
                refreshToken: refresh,
                admin,
                customer,
                chef,
                userType: admin ? 'Admin' : customer ? 'Customer' : "Chef", // Determine user type
            }));

            return { success: true, message: response.data.message, userType: admin ? 'Admin' : customer ? 'Customer' : "Chef" };
        } else {
            return { success: false, error: response.data.message };
        }
        
    } catch (error) {
        if (error.response && error.response.data) {
            return { success: false, error: error.response.data.message };
        } else {
            return { success: false, error: 'Saving failed. Please try again.' };
        }
    }
}