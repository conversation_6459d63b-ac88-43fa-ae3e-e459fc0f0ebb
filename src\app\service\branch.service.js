import { apiGet, apiPost, apiPut, apiDelete, apiPatch } from '../../api/apiManager';

export const getAllBranches = async () => {
    try {
        const response = await apiGet("/api/branch/get-all-branches");
        return response.data;
    } catch (error) {
        console.error('Error fetching branches:', error);
        throw error;
    }
}

export const saveExpense = async (expense) => {
    try {
        const response = await apiPost('/api/branch/save-expense', expense);
        return response.data;
    } catch (error) {
        console.error('Error saving expense:', error);
        throw error;
    }
}
export const saveBranch = async (branch) => {
    try {
        const response = await apiPost('/api/branch/save-branch', branch);
        return response.data;
    } catch (error) {
        console.error('Error saving branch:', error);
        throw error;
    }
}

export const getAllExpenseByBranchId = async (branchId) => {
    try {
        const response = await apiGet(`/api/branch/get-all-expenses-by-branch-id/${branchId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching expenses by id:', error);
        throw error;
    }
}

export const getAllExpenses = async () => {
    try {
        const response = await apiGet("/api/branch/get-all-expenses");
        return response.data;
    } catch (error) {
        console.error('Error fetching branches:', error);
        throw error;
    }
}


export const getAllBranchTypes = async () => {
    try {
        const response = await apiGet('/api/branch/get-all-branch-types');
        return response.data;
    } catch (error) {
        console.error('Error getting all branch types:', error);
        throw error;
    }
}

export const getBranchById = async (branchId) => {
    try {
        const response = await apiGet(`/api/branch/get-branch-by-id/${branchId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching branch by id:', error);
        throw error;
    }
}

export const updateBranch = async (branchId, data) => {
    try {
        const response = await apiPatch(`/api/branch/update-branch-details/${branchId}`, data);
        return response.data;
    } catch (error) {
        console.error('Error updating branch:', error);
        throw error;
    }
}

export const updateBranchSettings = async (branchId, data) => {
    try {
        const response = await apiPatch(`/api/branch/update-branch-settings/${branchId}`, data);
        return response.data;
    } catch (error) {
        console.error('Error updating branch settings:', error);
        throw error;
    }
}