import { apiGet, apiPost, apiPut, apiDelete, apiPatch } from '../../api/apiManager';

export const getAllChefCategories = async () => {
    try {
        const response = await apiGet("/api/chef/get-all-chef-categories");
        return response.data;
    } catch (error) {
        console.error('Error fetching branches:', error);
        throw error;
    }
}
export const getChefGeneralInfoById = async (id) => {
    try {
        const response = await apiGet(`/api/chef/get-chef-general-info/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching info:', error);
        throw error;
    }
}
export const updateChefGeneralInfo = async (id, payload) => {
    try {
        const response = await apiPatch(`/api/chef/update-chef-general-info/${id}`, payload);
        return response.data;
    } catch (error) {
        console.error('Error updating info:', error);
        throw error;
    }
}
export const updateProfessionalInfo = async (id, payload) => {
    try {
        const response = await apiPatch(`/api/chef/update-professional-info/${id}`, payload);
        return response.data;
    } catch (error) {
        console.error('Error fetching branches:', error);
        throw error;
    }
}
export const getAllChefs = async () => {
    try {
        const response = await apiGet("/api/chef/get-all-chefs");
        return response.data;
    } catch (error) {
        console.error('Error fetching chefs:', error);
        throw error;
    }
}


export const getChefProfessionalInfoById = async (id) => {
    try {
        const response = await apiGet(`/api/chef/get-chef-professional-info/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching info:', error);
        throw error;
    }
}

export const getAllVerifiedChefs = async () => {
    try {
        const response = await apiGet("/api/chef/get-all-verified-chef");
        return response.data;
    } catch (error) {
        console.error('Error fetching verified chefs:', error);
        throw error;
    }
}

export const getAllUnverifiedChefs = async () => {
    try {
        const response = await apiGet("/api/chef/get-all-unverified-chef");
        return response.data;
    } catch (error) {
        console.error('Error fetching unverified chefs:', error);
        throw error;
    }
}

export const searchChefs = async (searchParams = {}) => {
    try {
        const queryParams = new URLSearchParams();

        // Add search parameters
        if (searchParams.query) queryParams.append('q', searchParams.query);
        if (searchParams.page) queryParams.append('page', searchParams.page);
        if (searchParams.limit) queryParams.append('limit', searchParams.limit);
        if (searchParams.minRate) queryParams.append('minRate', searchParams.minRate);
        if (searchParams.maxRate) queryParams.append('maxRate', searchParams.maxRate);
        if (searchParams.location) queryParams.append('location', searchParams.location);
        if (searchParams.category) queryParams.append('category', searchParams.category);
        if (searchParams.sortBy) queryParams.append('sortBy', searchParams.sortBy);

        const response = await apiGet(`/api/chef/search?${queryParams.toString()}`);
        return response.data;
    } catch (error) {
        console.error('Error searching chefs:', error);
        throw error;
    }
}


export const chefVerifiedByAdmin = async(id)=>{
    try {
        const response = await apiPatch(`/api/chef/verify-chef/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching branches:', error);
        throw error;
    }
}

export const getChefInfoById= async(id)=>{
    try {
        const response = await apiGet(`/api/chef/chef-info/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching branches:', error);
        throw error;
    }
}


export const getChefAvailableDays = async (id) => {
    try {
        const response = await apiGet(`/api/chef/chef-availability/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching available days:', error);
        throw error;
    }
}

export const bookChef = async (bookingData) => {
    try {
        const response = await apiPost('/api/chef/book-chef', bookingData);
        return response.data;
    } catch (error) {
        console.error('Error booking chef:', error);
        throw error;
    }
}