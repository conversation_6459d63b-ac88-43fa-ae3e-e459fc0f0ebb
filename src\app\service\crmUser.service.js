import { apiGet, apiPost, apiPut, apiDelete, apiPatch } from '../../api/apiManager';


export const saveCrmUser = async (crmUserData) => {
    try {
        const response = await apiPost('/api/user/save-crm-user', crmUserData);
        return response.data;
    } catch (error) {
        console.error('Error saving user:', error);
        throw error;
    }
}

export const updateCrmUser = async (crmUserId, crmUserPayload) => {
    try {
        const response = await apiPatch(`/api/crm-user/update-crm-user-info/${crmUserId}`, crmUserPayload);
        return response.data;
    } catch (error) {
        console.error('Error updating user:', error);
        throw error;
    }
}

export const updateCrmUserSecurityDetails = async (crmUserId, crmUserPayload) => {
    try {
        const response = await apiPatch(`/api/crm-user/update-crm-user-security/${crmUserId}`, crmUserPayload);
        return response.data;
    } catch (error) {
        console.error('Error updating crm user security details:', error);
        throw error;
    }
}

export const getCrmUserById = async (crmUserId) => {
    try {
        const response = await apiGet(`/api/crm-user/get-crm-user-info/${crmUserId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching crm user by id:', error);
        throw error;
    }
}

export const getAllCrmStudents = async () => {
    try {
        const response = await apiGet('/api/crm-user/get-all-crm-students');
        return response.data;
    } catch (error) {
        console.error('Error fetching students:', error);
        throw error;
    }
};

export const saveCrmStudent = async (crmStudentData) => {
    try {
        const response = await apiPost('/api/crm-user/save-crm-student', crmStudentData);
        return response.data;
    } catch (error) {
        console.error('Error saving student:', error);
        throw error;
    }
}

export const getCrmStudentById = async (crmStudentId) => {
    try {
        const response = await apiGet(`/api/crm-user/get-crm-student-info/${crmStudentId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching student by id:', error);
        throw error;
    }
}

export const updateCrmStudent = async (crmStudentId, crmStudentPayload) => {
    try {
        const response = await apiPatch(`/api/crm-user/update-crm-student/${crmStudentId}`, crmStudentPayload);
        return response.data;
    } catch (error) {
        console.error('Error updating student:', error);
        throw error;
    }
}

export const getAllCrmUsers = async () => {
    try {
        const response = await apiGet('/api/crm-user/get-all-crm-users');
        return response.data;
    } catch (error) {
        console.error('Error fetching users:', error);
        throw error;
    }
};

export const getAllCrmStudentsByCrmUserId = async (crmUserId) => {
    try {
        const response = await apiGet(`/api/crm-user/get-all-crm-students-by-user-id/${crmUserId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching users:', error);
        throw error;
    }
};