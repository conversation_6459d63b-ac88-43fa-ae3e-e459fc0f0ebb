import { apiGet } from "../../api/apiManager";


export const getDashboardData = async () => {
    try {
        const response = await apiGet('/api/dashboard/cards');
        return response.data;
    } catch (error) {
        console.error('Error getting dashboard data:', error);
        throw error;
    }
}

export const getBarchartData = async () => {
    try {
        const response = await apiGet('/api/dashboard/bar-chart');
        return response.data;
    } catch (error) {
        console.error('Error getting barchart data:', error);
        throw error;
    }
}

export const getPiechartData = async () => {
    try {
        const response = await apiGet('/api/dashboard/pie-chart');
        return response.data;
    } catch (error) {
        console.error('Error getting piechart data:', error);
        throw error;
    }
}