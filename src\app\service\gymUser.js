import { apiGet, apiPost } from '../../api/apiManager';

export const getAllGymUsers = async () => {
    try {
        const response = await apiGet('/api/gym/all-gym-users');
        return response.data;
    } catch (error) {
        console.error('Error getting all users:', error);
        throw error;
    }
}

export const saveGymUser = async (userData)=>{
    try {
        const response = await apiPost('/api/user/save-gym-user', userData);
        return response.data;
    } catch (error) {
        console.error('Error saving user:', error);
        throw error;
    }
}

export const deleteGymUser = async (gymUserId)=> {
    try {
        const response = await apiPost('/api/gym/delete', gymUserId);
        return response.data;
    } catch (error) {
        console.error('Error deleting user:', error);
        throw error;
    }
}