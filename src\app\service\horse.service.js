import { apiGet, apiPost, apiPut, apiDelete, apiPatch } from '../../api/apiManager';

export const saveHorse = async (horse) => {
    try {
        const response = await apiPost('/api/horse/save-horse', horse);
        return response.data;
    } catch (error) {
        console.error('Error saving horse:', error);
        throw error;
    }
}

export const getAllHorse = async () => {
    try {
        const response = await apiGet('/api/horse/get-all-horse');
        return response.data;
    } catch (error) {
        console.error('Error getting all horses:', error);
        throw error;
    }
}

export const getHorseById = async (horseId) => {
    try {
        const response = await apiGet(`/api/horse/get-horse-by-id/${horseId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching horse by id:', error);
        throw error;
    }
}