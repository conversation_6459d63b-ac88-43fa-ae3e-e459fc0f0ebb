import { apiGet, apiPost } from "../../api/apiManager";

export const saveLoan = async (loan) => {
    try {
        const response = await apiPost('/api/loan/save-loan', loan);
        return response.data;
    } catch (error) {
        console.error('Error saving loan:', error);
        throw error;
    }
}

export const getAllLoansByBranch = async () => {
    try {
        const response = await apiGet("/api/loan/get-all-loans-by-branch");
        return response.data;
    } catch (error) {
        console.error('Error fetching branches:', error);
        throw error;
    }
}
export const getAllLoans = async () => {
    try {
        const response = await apiGet("/api/loan/get-all-loans");
        return response.data;
    } catch (error) {
        console.error('Error fetching loans:', error);
        throw error;
    }
}

export const getAllLoansByBranchId = async (branchId) => {
    try {
        const response = await apiGet(`/api/loan/get-all-loans-by-branch-id/${branchId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching loans by id:', error);
        throw error;
    }
}

export const getAllLoanDeductionByLoanId = async (loanId)=>{
    try{
        const response = await apiGet(`/api/loan/get-all-loan-deduction-by-loan/${loanId}`);
        return response.data;
    }catch (error) {
        console.error('Error fetching loan deductions by id:', error);
        throw error;
    }
}