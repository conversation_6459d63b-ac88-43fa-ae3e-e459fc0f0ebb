import { apiGet, apiPatch, apiPost } from '../../api/apiManager';

export const checkBarcodeAvalibility = async (barcode) => {
    try {
        const response = await apiPost('/api/member/check-barcode-availability', barcode);
        return response.data;
    } catch (error) {
        console.error('Error checking barcode:', error);
        throw error;
    }
}

export const getAllActiveMembershipPlans = async () => {
    try {
        const response = await apiGet('/api/member/membership-plan/all');
        return response.data;
    } catch (error) {
        console.error('Error getting active membership plans:', error);
        throw error;
    }
}

export const getAllMembers = async () => {
    try {
        const response = await apiGet('/api/member/all-members');
        return response.data;
    } catch (error) {
        console.error('Error getting all members:', error);
        throw error;
    }
}

export const saveMember = async (memberData)=>{
    try {
        const response = await apiPost('/api/member/add-new-member', memberData);
        return response.data;
    } catch (error) {
        console.error('Error saving member:', error);
        throw error;
    }
}

export const saveMembershipPlan = async (planData)=>{
    try {
        const response = await apiPost('/api/member/membership-plan/new', planData);
        return response.data;
    } catch (error) {
        console.error('Error saving membership plan:', error);
        throw error;
    }
}

export const getActiveMembershipByBarcode = async (barcode) => {
    try {
        const response = await apiGet(`/api/member/membership/detail/${barcode}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching member by barcode:', error);
        throw error;
    }
}

export const assignMembershipPlan = async (memberId, membershipPlanId)=>{
    try {
        const response = await apiPost('/api/member/membership-plan/assign', {memberId, membershipPlanId});
        return response.data;
    } catch (error) {
        console.error('Error saving membership plan:', error);
        throw error;
    }
}

export const deleteMemberShipPlans = async (membershipPlanId)=> {
    try {
        const response = await apiPost('/api/member/membership-plan/delete', membershipPlanId);
        return response.data;
    } catch (error) {
        console.error('Error deleting membership plan:', error);
        throw error;
    }
}

export const deleteMember = async (memberId)=> {
    try {
        const response = await apiPost('/api/member/delete', memberId);
        return response.data;
    } catch (error) {
        console.error('Error deleting member:', error);
        throw error;
    }
}

export const getMembershipPlanById = async (membershipPlanId) => {
    try {
        const response = await apiGet(`/api/member/membership-plan/retrieve/${membershipPlanId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching membership plan by ID:', error);
        throw error;
    }
}