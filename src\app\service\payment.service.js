import { apiGet, apiPost } from "../../api/apiManager";

export const fetchPaymentTypesForMember = async (barcode) => {
  try {
    const response = await apiPost("/api/payment/payment-types/member", {
      barcode,
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching payment types:", error);
    throw error;
  }
};

export const fetchPaymentTypePriceDetails = async (memberId, paymentTypeId) => {
  try {
    const response = await apiPost(
      "/api/payment/payment-types/member/price-details",
      {
        memberId,
        paymentTypeId,
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching payment types price details:", error);
    throw error;
  }
};

export const saveAdmissionPrice = async (data) => {
  try {
    const response = await apiPost(
      "/api/payment/add-new-admission-price",
      data
    );
    return response.data;
  } catch (error) {
    console.error("Error saving admission price:", error);
    throw error;
  }
};

export const getAllPaymentMethod = async () => {
  try {
    const response = await apiGet("/api/payment/payment-method/all");
    return response.data;
  } catch (error) {
    console.error("Error getting payment methods:", error);
    throw error;
  }
};

export const makePayment = async (body) => {
  try {
    const response = await apiPost("/api/payment/make-payment", body);
    return response.data;
  } catch (error) {
    console.error("Error saving payment", error);
    throw error;
  }
};

export const getAllPayment = async () => {
  try {
    const response = await apiGet("/api/payment/all-payments");
    return response.data;
  } catch (error) {
    console.error("Error getting all payments:", error);
    throw error;
  }
};

export const getAllSubcriptionPayment = async () => {
  try {
    const response = await apiGet("/api/payment/get-all-customer-sub-payments");
    return response.data;
  } catch (error) {
    console.error("Error getting all subscription payments:", error);
    throw error;
  }
};

export const getAllBookingPayment = async () => {
  try {
    const response = await apiGet("/api/payment/get-all-customer-sub-payments");
    return response.data;
  } catch (error) {
    console.error("Error getting all subscription payments:", error);
    throw error;
  }
};