import { apiGet, apiPost, apiPut, apiDelete, apiPatch } from '../../api/apiManager';


export const saveRace = async (race) => {
    try {
        const response = await apiPost('/api/race/save-race', race);
        return response.data;
    } catch (error) {
        console.error('Error saving race:', error);
        throw error;
    }
}

export const getAllTodayScheduleRaces = async (raceType,time) => {
    try {
        const response = await apiGet(`/api/race/get-today-scehdule-races/${raceType}/?time=${time}`);
        return response.data;
    } catch (error) {
        console.error('Error getting today schedule races:', error);
        throw error;
    }
}


export const getHorseByRace = async (raceId) => {
    try {
        const response = await apiGet(`/api/race/get-horses-by-race/${raceId}`);
        return response.data;
    } catch (error) {
        console.error('Error getting horse by race:', error);
        throw error;
    }
}


export const getAllRaceSchedules = async () => {
    try {
        const response = await apiGet('/api/race/get-all-race-schedules');
        return response.data;
    } catch (error) {
        console.error('Error getting all race schedules:', error);
        throw error;
    }
}

export const getRaceScheduleDetailsById = async (raceId) => {
    try {
        const response = await apiGet(`/api/race/get-race-schedule-details-by-id/${raceId}`);
        return response.data;
    } catch (error) {
        console.error('Error getting race schedule details:', error);
        throw error;
    }
}


export const updateRaceSchedule = async (raceId, body) => {
    try {
        const response = await apiPatch(`/api/race/update-race-schedule/${raceId}`, body);
        return response.data;
    } catch (error) {
        console.error('Error updating race schedule:', error);
        throw error;
    }
}