import { apiGet, apiPost, apiPut, apiDelete, apiPatch } from '../../api/apiManager';

export const saveRaceCenter = async (raceCenter) => {
    try {
        const response = await apiPost('/api/race-center/save-race-center', raceCenter);
        return response.data;
    } catch (error) {
        console.error('Error saving race center:', error);
        throw error;
    }
}

export const getAllRaceCenters = async () => {
    try {
        const response = await apiGet('/api/race-center/get-all-race-centers');
        return response.data;
    } catch (error) {
        console.error('Error getting all race centers:', error);
        throw error;
    }
}

export const getRaceCenterById = async (raceCenterId) => {
    try {
        const response = await apiGet(`/api/race-center/get-race-center-by-id/${raceCenterId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching race center by id:', error);
        throw error;
    }
}