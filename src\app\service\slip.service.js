import { apiGet, apiPost, apiPut, apiDelete, apiPatch } from '../../api/apiManager';

export const saveSlip = async (slip) => {
    try {
        const response = await apiPost('/api/slip/save-slip', slip);
        return response.data;
    } catch (error) {
        console.error('Error saving slip:', error);
        throw error;
    }
}


export const getAllPendingSlip = async () => {
    try {
        const response = await apiGet('/api/slip/get-all-pending-slip');
        return response.data;
    } catch (error) {
        console.error('Error getting all pending slip:', error);
        throw error;
    }
}

export const getPendingSlipById = async (id) => {
    try {
        const response = await apiGet(`/api/slip/get-pending-slip-by-id/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching pending slip by id:', error);
        throw error;
    }
}


export const updatePendingSlip = async (id, data) => {
    try {
        const response = await apiPatch(`/api/slip/update-pending-slip/${id}`, data);
        return response.data;
    } catch (error) {
        console.error('Error updating pending slip:', error);
        throw error;
    }
}

export const getSlipDetailsById = async (id) => {
    try {
        const response = await apiGet(`/api/slip/get-slip-detail-by-id/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching slip details by id:', error);
        throw error;
    }
}


export const getSlipByBranchAndDate = async (selectDate, branchId) => {
    try {
        const response = await apiGet(`/api/slip/get-result-slips-by-branch/${branchId}/?selectDate=${selectDate}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching slip by branch and date:', error);
        throw error;
    }
}

export const getAllSlips = async () => {
    try {
        const response = await apiGet('/api/slip/get-all-slips');
        return response.data;
    } catch (error) {
        console.error('Error getting all slips:', error);
        throw error;
    }
}


export const getSlipBetDetailsById = async (id)=>{
    try {
        const response = await apiGet(`/api/slip/get-slip-bet_details_by_id/${id}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching slip bet details by id:', error);
        throw error;
    }
}

export const updateSlipBetDetails = async(id,data)=>{
    try {
        const response = await apiPatch(`/api/slip/update-slip-bet-details/${id}`, data); 
        return response.data;
    } catch (error) {
        console.error('Error updating slip bet details:', error);
        throw error;
    }
}
export const removeSlipBetDetail = async(id)=>{
    try {
        const response = await apiPatch(`/api/slip/remove-slip-bet-details/${id}`); 
        return response.data;
    } catch (error) {
        console.error('Error updating slip bet details:', error);
        throw error;
    }
}

export const getAllTodaySubmittedSlip = async () => {
    try {
        const response = await apiGet('/api/slip/get-all-today-submitted-slip');
        return response.data;
    } catch (error) {
        console.error('Error getting slips:', error);
        throw error;
    }
}

export const addCashPayout = async (data)=>{
    try {
        const response = await apiPost('/api/slip/add-cash-pay-out', data);
        return response.data;
    } catch (error) {
        console.error('Error adding cash payout:', error);
        throw error;
    }
}