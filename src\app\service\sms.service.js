import { apiGet, apiPatch, apiPost } from "../../api/apiManager";

export const saveSMS = async (payload) => {
    try {
        const response = await apiPost('/api/sms/save-sms', payload);
        return response.data;
    } catch (error) {
        console.error('Error saving sms:', error);
        throw error;
    }
}

export const fetchSMS = async ()=>{
    try {
        const response = await apiGet('/api/sms/fetch-sms');
        return response.data;
    } catch (error) {
        console.error('Error fetching sms:', error);
        throw error;
    }
}

export const updateSMS = async (id, payload) => {
    try {
        const response = await apiPatch(`/api/sms/update-sms/${id}`, payload);
        return response.data;
    } catch (error) {
        console.error('Error updating sms:', error);
        throw error;
    }
}