import { apiGet, apiPatch, apiPost } from "../../api/apiManager";

export const saveSubscription = async (payload) => {
    try {
        const response = await apiPost('/api/subscription/add-subscription', payload);
        return response.data;
    } catch (error) {
        console.error('Error saving sms:', error);
        throw error;
    }
}

export const getAllSubscriptions = async () => {
    try {
        const response = await apiGet('/api/subscription/get-all-subscriptions');
        return response.data;
    } catch (error) {
        console.error('Error saving sms:', error);
        throw error;
    }
}