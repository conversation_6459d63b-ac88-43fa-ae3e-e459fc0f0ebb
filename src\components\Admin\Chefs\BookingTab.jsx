import React from "react";
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Chip,
  useMediaQuery,
  useTheme,
} from "@mui/material";

const BookingTab = ({ bookings = [], onBack }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    };
  
    const getStatusColor = (status) => {
      switch (status.toLowerCase()) {
        case 'confirmed':
          return { backgroundColor: '#E8F5E8', color: '#2E7D32' };
        case 'pending':
          return { backgroundColor: '#FFF3E0', color: '#F57C00' };
        case 'cancelled':
          return { backgroundColor: '#FFEBEE', color: '#C62828' };
        default:
          return { backgroundColor: '#F5F5F5', color: '#666' };
      }
    };
  
    if (isMobile) {
      return (
        <Box sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: '600', color: '#333', mb: 3 }}>
            Bookings
          </Typography>
          {bookings?.map((booking, index) => (
            <Card key={index} sx={{ mb: 2, border: '1px solid #E0E0E0' }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: '500' }}>
                    {booking.clientName}
                  </Typography>
                  <Chip
                    label={booking.bookingStatus}
                    size="small"
                    sx={{
                      ...getStatusColor(booking.bookingStatus),
                      fontWeight: '500',
                    }}
                  />
                </Box>
                <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                  <strong>Date:</strong> {formatDate(booking.bookingDate)}
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                  <strong>Time:</strong> {booking.bookingTime}
                </Typography>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  <strong>Phone:</strong> {booking.clientPhone}
                </Typography>
              </CardContent>
            </Card>
          ))}
          {!bookings?.length && (
            <Typography variant="body1" color="textSecondary" sx={{ textAlign: 'center', py: 4 }}>
              No bookings found
            </Typography>
          )}
        </Box>
      );
    }
  
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: '600', color: '#333', mb: 3 }}>
          Bookings
        </Typography>
        <TableContainer component={Paper} sx={{ border: '1px solid #E0E0E0' }}>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#F8F9FA' }}>
                <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Booking Date</TableCell>
                <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Time</TableCell>
                <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Client Name</TableCell>
                <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Phone Number</TableCell>
                <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {bookings?.map((booking, index) => (
                <TableRow key={index} hover>
                  <TableCell>{formatDate(booking.bookingDate)}</TableCell>
                  <TableCell>{booking.bookingTime}</TableCell>
                  <TableCell sx={{ fontWeight: '500' }}>{booking.clientName}</TableCell>
                  <TableCell>{booking.clientPhone}</TableCell>
                  <TableCell>
                    <Chip
                      label={booking.bookingStatus}
                      size="small"
                      sx={{
                        ...getStatusColor(booking.bookingStatus),
                        fontWeight: '500',
                      }}
                    />
                  </TableCell>
                </TableRow>
              ))}
              {!bookings?.length && (
                <TableRow>
                  <TableCell colSpan={5} sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body1" color="textSecondary">
                      No bookings found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    );
};

export default BookingTab;