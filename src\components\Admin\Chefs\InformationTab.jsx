import React, { useState } from "react";
import {
  Box,
  Paper,
  Typography,
  Container,
  Button,
  Grid,
  TextField,
} from "@mui/material";
import { ArrowBack } from "@mui/icons-material";

const InformationTab = ({ chef: initialChef, onUpdate, onBack }) => {
  const [editableChef, setEditableChef] = useState(initialChef || {
    name: "",
    title: "",
    email: "",
    phoneNumber: "",
    hourlyRate: "",
    location: "",
    englishLevel: "",
    freelancerType: "",
    languages: [],
    skills: [],
    about: ""
  });
  const [isEditing, setIsEditing] = useState(false);

  const handleChange = (field, value) => {
    setEditableChef(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    if (onUpdate) {
      onUpdate(editableChef);
    }
    setIsEditing(false);
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 2 }}>
      <Paper sx={{ border: '1px solid #E0E0E0', borderRadius: 2 }}>
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: '600', color: '#333' }}>
              Basic Information
            </Typography>
            <Button
              variant={isEditing ? "contained" : "outlined"}
              onClick={isEditing ? handleSave : () => setIsEditing(true)}
              sx={{ 
                minWidth: 100,
                ...(isEditing && {
                  backgroundColor: '#6C5CE7',
                  '&:hover': { backgroundColor: '#5B4BD6' }
                })
              }}
            >
              {isEditing ? "Save" : "Edit"}
            </Button>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Full Name"
                value={editableChef.name}
                onChange={(e) => handleChange('name', e.target.value)}
                disabled={!isEditing}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Title"
                value={editableChef.title}
                onChange={(e) => handleChange('title', e.target.value)}
                disabled={!isEditing}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                value={editableChef.email}
                onChange={(e) => handleChange('email', e.target.value)}
                disabled={!isEditing}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={editableChef.phoneNumber}
                onChange={(e) => handleChange('phoneNumber', e.target.value)}
                disabled={!isEditing}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Hourly Rate"
                value={editableChef.hourlyRate}
                onChange={(e) => handleChange('hourlyRate', e.target.value)}
                disabled={!isEditing}
                variant="outlined"
                sx={{ mb: 2 }}
                InputProps={{
                  startAdornment: <Typography sx={{ mr: 1 }}>$</Typography>,
                  endAdornment: <Typography sx={{ ml: 1 }}>/hr</Typography>,
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Location"
                value={editableChef.location}
                onChange={(e) => handleChange('location', e.target.value)}
                disabled={!isEditing}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="English Level"
                value={editableChef.englishLevel}
                onChange={(e) => handleChange('englishLevel', e.target.value)}
                disabled={!isEditing}
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Languages"
                value={editableChef.languages?.join(', ') || ''}
                onChange={(e) => handleChange('languages', e.target.value.split(', '))}
                disabled={!isEditing}
                variant="outlined"
                sx={{ mb: 2 }}
                helperText="Separate multiple languages with commas"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Skills"
                value={editableChef.skills?.join(', ') || ''}
                onChange={(e) => handleChange('skills', e.target.value.split(', '))}
                disabled={!isEditing}
                variant="outlined"
                sx={{ mb: 2 }}
                helperText="Separate multiple skills with commas"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="About"
                value={editableChef.about || ''}
                onChange={(e) => handleChange('about', e.target.value)}
                disabled={!isEditing}
                variant="outlined"
                multiline
                rows={4}
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Container>
  );
};

export default InformationTab;