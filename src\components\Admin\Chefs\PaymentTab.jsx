import React from "react";
import {
  Box,
  Paper,
  Typography,
  Container,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { TrendingUp, AccountBalance, Receipt } from "@mui/icons-material";

const PaymentsPage = ({ payments = [] }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };


  const totalEarnings = payments.reduce((sum, payment) => sum + payment.amount, 0);
  const thisMonthPayments = payments.filter(payment => {
    const paymentDate = new Date(payment.paymentDate);
    const now = new Date();
    return paymentDate.getMonth() === now.getMonth() && paymentDate.getFullYear() === now.getFullYear();
  });
  const thisMonthEarnings = thisMonthPayments.reduce((sum, payment) => sum + payment.amount, 0);

  const MobilePaymentsView = () => (
    <Box>
      {payments.map((payment, index) => (
        <Card key={index} sx={{ mb: 2, border: '1px solid #E0E0E0' }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: '500' }}>
                {payment.clientName}
              </Typography>
              <Typography variant="h6" sx={{ color: '#4CAF50', fontWeight: '600' }}>
                ${payment.amount}
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
              <strong>Date:</strong> {formatDate(payment.paymentDate)}
            </Typography>
            <Typography variant="body2" sx={{ color: '#666' }}>
              <strong>Booking ID:</strong> {payment.bookingId}
            </Typography>
          </CardContent>
        </Card>
      ))}
    </Box>
  );

  const DesktopPaymentsView = () => (
    <TableContainer component={Paper} sx={{ border: '1px solid #E0E0E0' }}>
      <Table>
        <TableHead>
          <TableRow sx={{ backgroundColor: '#F8F9FA' }}>
            <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Payment Date</TableCell>
            <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Amount</TableCell>
            <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Booking ID</TableCell>
            <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Client Name</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {payments.map((payment, index) => (
            <TableRow key={index} hover>
              <TableCell>{formatDate(payment.paymentDate)}</TableCell>
              <TableCell sx={{ color: '#4CAF50', fontWeight: '600' }}>
                ${payment.amount}
              </TableCell>
              <TableCell sx={{ fontWeight: '500' }}>{payment.bookingId}</TableCell>
              <TableCell>{payment.clientName}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const EmptyState = () => (
    <Paper sx={{ border: '1px solid #E0E0E0', p: 6, textAlign: 'center' }}>
      <Receipt sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
      <Typography variant="h6" color="textSecondary" sx={{ mb: 2 }}>
        No payments received yet
      </Typography>
      <Typography variant="body2" color="textSecondary">
        When clients make payments, they will appear here.
      </Typography>
    </Paper>
  );

  return (
    <Container maxWidth="lg" sx={{ py: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: '600', color: '#333' }}>
          Payments & Earnings
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <Paper sx={{ p: 3, minWidth: 250, border: '1px solid #E0E0E0', borderRadius: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ 
              p: 1, 
              borderRadius: 2, 
              backgroundColor: '#E8F5E8', 
              mr: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <AccountBalance sx={{ color: '#4CAF50' }} />
            </Box>
            <Typography variant="body2" color="textSecondary">Total Earnings</Typography>
          </Box>
          <Typography variant="h4" sx={{ fontWeight: '600', color: '#4CAF50' }}>
            ${totalEarnings.toLocaleString()}
          </Typography>
        </Paper>

        <Paper sx={{ p: 3, minWidth: 250, border: '1px solid #E0E0E0', borderRadius: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ 
              p: 1, 
              borderRadius: 2, 
              backgroundColor: '#E3F2FD', 
              mr: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <TrendingUp sx={{ color: '#2196F3' }} />
            </Box>
            <Typography variant="body2" color="textSecondary">This Month</Typography>
          </Box>
          <Typography variant="h4" sx={{ fontWeight: '600', color: '#2196F3' }}>
            ${thisMonthEarnings.toLocaleString()}
          </Typography>
        </Paper>

        <Paper sx={{ p: 3, minWidth: 250, border: '1px solid #E0E0E0', borderRadius: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ 
              p: 1, 
              borderRadius: 2, 
              backgroundColor: '#FFF3E0', 
              mr: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Receipt sx={{ color: '#FF9800' }} />
            </Box>
            <Typography variant="body2" color="textSecondary">Total Payments</Typography>
          </Box>
          <Typography variant="h4" sx={{ fontWeight: '600', color: '#FF9800' }}>
            {payments.length}
          </Typography>
        </Paper>
      </Box>

      {/* Payments Content */}
      <Paper sx={{ border: '1px solid #E0E0E0', borderRadius: 2 }}>
        <Box sx={{ p: 3, borderBottom: '1px solid #E0E0E0' }}>
          <Typography variant="h6" sx={{ fontWeight: '600', color: '#333' }}>
            Payment History
          </Typography>
        </Box>
        <Box sx={{ p: 3 }}>
          {payments.length === 0 ? (
            <EmptyState />
          ) : (
            <>
              {isMobile ? <MobilePaymentsView /> : <DesktopPaymentsView />}
            </>
          )}
        </Box>
      </Paper>
    </Container>
  );
};

export default PaymentsPage;