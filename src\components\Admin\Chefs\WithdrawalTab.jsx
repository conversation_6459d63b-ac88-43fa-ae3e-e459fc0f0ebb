import React, { useState } from "react";
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Chip,
  useMediaQuery,
  useTheme,
} from "@mui/material";

const WithdrawalTab = ({ withdrawals = [] }) => {
     const theme = useTheme();
      const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    
      const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      };
    
      const getStatusColor = (status) => {
        switch (status.toLowerCase()) {
          case 'completed':
            return { backgroundColor: '#E8F5E8', color: '#2E7D32' };
          case 'pending':
            return { backgroundColor: '#FFF3E0', color: '#F57C00' };
          case 'failed':
            return { backgroundColor: '#FFEBEE', color: '#C62828' };
          default:
            return { backgroundColor: '#F5F5F5', color: '#666' };
        }
      };
    
      if (isMobile) {
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: '600', color: '#333', mb: 3 }}>
              Withdrawals
            </Typography>
            {withdrawals?.map((withdrawal, index) => (
              <Card key={index} sx={{ mb: 2, border: '1px solid #E0E0E0' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6" sx={{ color: '#FF5722', fontWeight: '600' }}>
                      ${withdrawal.amount}
                    </Typography>
                    <Chip
                      label={withdrawal.withdrawStatus}
                      size="small"
                      sx={{
                        ...getStatusColor(withdrawal.withdrawStatus),
                        fontWeight: '500',
                      }}
                    />
                  </Box>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    <strong>Date:</strong> {formatDate(withdrawal.withdrawalDate)}
                  </Typography>
                </CardContent>
              </Card>
            ))}
            {!withdrawals?.length && (
              <Typography variant="body1" color="textSecondary" sx={{ textAlign: 'center', py: 4 }}>
                No withdrawals found
              </Typography>
            )}
          </Box>
        );
      }
    
      return (
        <Box sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: '600', color: '#333', mb: 3 }}>
            Withdrawals
          </Typography>
          <TableContainer component={Paper} sx={{ border: '1px solid #E0E0E0' }}>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#F8F9FA' }}>
                  <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Withdrawal Date</TableCell>
                  <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Amount</TableCell>
                  <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {withdrawals?.map((withdrawal, index) => (
                  <TableRow key={index} hover>
                    <TableCell>{formatDate(withdrawal.withdrawalDate)}</TableCell>
                    <TableCell sx={{ color: '#FF5722', fontWeight: '600' }}>
                      ${withdrawal.amount}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={withdrawal.withdrawStatus}
                        size="small"
                        sx={{
                          ...getStatusColor(withdrawal.withdrawStatus),
                          fontWeight: '500',
                        }}
                      />
                    </TableCell>
                  </TableRow>
                ))}
                {!withdrawals?.length && (
                  <TableRow>
                    <TableCell colSpan={3} sx={{ textAlign: 'center', py: 4 }}>
                      <Typography variant="body1" color="textSecondary">
                        No withdrawals found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      );
}

export default WithdrawalTab;