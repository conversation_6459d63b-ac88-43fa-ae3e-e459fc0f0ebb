import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  Paper,
  Avatar,
  useTheme,
  useMediaQuery,
  Stack,
  Divider
} from '@mui/material';
import { Edit, Save, Cancel, Person, Email, Phone, CalendarToday } from '@mui/icons-material';

const InformationTab = ({ customer, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedCustomer, setEditedCustomer] = useState(customer);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleEdit = () => {
    setIsEditing(true);
    setEditedCustomer(customer);
  };

  const handleSave = () => {
    onUpdate(editedCustomer);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedCustomer(customer);
    setIsEditing(false);
  };

  const handleInputChange = (field, value) => {
    setEditedCustomer(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getInitials = (name) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || '';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getMembershipColor = (plan) => {
    switch (plan) {
      case "1 Month":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
        };
      case "3 Month":
        return {
          backgroundColor: "#F3E5F5",
          color: "#7B1FA2",
        };
      case "6 Month":
        return {
          backgroundColor: "#E0F2F1",
          color: "#00796B",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
        };
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        mb: 3,
        flexDirection: isMobile ? 'column' : 'row',
        gap: isMobile ? 2 : 0
      }}>
        <Typography variant="h6" sx={{ fontWeight: '600', color: '#333' }}>
          Customer Information
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          {!isEditing ? (
            <Button
              startIcon={<Edit />}
              onClick={handleEdit}
              variant="outlined"
              size="small"
              sx={{
                borderColor: '#6C5CE7',
                color: '#6C5CE7',
                '&:hover': {
                  backgroundColor: '#F8F7FF',
                  borderColor: '#6C5CE7'
                }
              }}
            >
              Edit
            </Button>
          ) : (
            <>
              <Button
                startIcon={<Save />}
                onClick={handleSave}
                variant="contained"
                size="small"
                sx={{
                  backgroundColor: '#4CAF50',
                  '&:hover': { backgroundColor: '#45A049' }
                }}
              >
                Save
              </Button>
              <Button
                startIcon={<Cancel />}
                onClick={handleCancel}
                variant="outlined"
                size="small"
                sx={{
                  borderColor: '#f44336',
                  color: '#f44336',
                  '&:hover': {
                    backgroundColor: '#ffebee',
                    borderColor: '#f44336'
                  }
                }}
              >
                Cancel
              </Button>
            </>
          )}
        </Box>
      </Box>

      <Grid container spacing={3}>

        {/* Customer Details */}
        <Grid item xs={12} md={12}>
          <Paper sx={{ 
            p: 3, 
            border: '1px solid #E0E0E0', 
            borderRadius: 2 
          }}>
            <Typography variant="h6" sx={{ fontWeight: '600', mb: 3, color: '#333' }}>
              Personal Details
            </Typography>

            <Stack spacing={3}>
              {/* Name */}
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Person sx={{ fontSize: 20, color: '#666', mr: 1 }} />
                  <Typography variant="body2" sx={{ fontWeight: '600', color: '#666' }}>
                    Full Name
                  </Typography>
                </Box>
                {isEditing ? (
                  <TextField
                    fullWidth
                    value={editedCustomer.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    variant="outlined"
                    size="small"
                  />
                ) : (
                  <Typography variant="body1" sx={{ pl: 3.5 }}>
                    {customer.name}
                  </Typography>
                )}
              </Box>

              <Divider />

              {/* Email */}
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Email sx={{ fontSize: 20, color: '#666', mr: 1 }} />
                  <Typography variant="body2" sx={{ fontWeight: '600', color: '#666' }}>
                    Email Address
                  </Typography>
                </Box>
                {isEditing ? (
                  <TextField
                    fullWidth
                    value={editedCustomer.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    variant="outlined"
                    size="small"
                    type="email"
                  />
                ) : (
                  <Typography variant="body1" sx={{ pl: 3.5, wordBreak: 'break-all' }}>
                    {customer.email}
                  </Typography>
                )}
              </Box>

              <Divider />

              {/* Phone */}
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Phone sx={{ fontSize: 20, color: '#666', mr: 1 }} />
                  <Typography variant="body2" sx={{ fontWeight: '600', color: '#666' }}>
                    Phone Number
                  </Typography>
                </Box>
                {isEditing ? (
                  <TextField
                    fullWidth
                    value={editedCustomer.phone || ''}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    variant="outlined"
                    size="small"
                  />
                ) : (
                  <Typography variant="body1" sx={{ pl: 3.5 }}>
                    {customer.phone}
                  </Typography>
                )}
              </Box>

              <Divider />

              {/* Username */}
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Person sx={{ fontSize: 20, color: '#666', mr: 1 }} />
                  <Typography variant="body2" sx={{ fontWeight: '600', color: '#666' }}>
                    Username
                  </Typography>
                </Box>
                {isEditing ? (
                  <TextField
                    fullWidth
                    value={editedCustomer.username || ''}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    variant="outlined"
                    size="small"
                  />
                ) : (
                  <Typography variant="body1" sx={{ pl: 3.5 }}>
                    @{customer.username}
                  </Typography>
                )}
              </Box>

              <Divider />

              {/* Join Date */}
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CalendarToday sx={{ fontSize: 20, color: '#666', mr: 1 }} />
                  <Typography variant="body2" sx={{ fontWeight: '600', color: '#666' }}>
                    Member Since
                  </Typography>
                </Box>
                <Typography variant="body1" sx={{ pl: 3.5 }}>
                  {formatDate(customer.joinDate)}
                </Typography>
              </Box>

              <Divider />

              {/* Membership Plan */}
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CalendarToday sx={{ fontSize: 20, color: '#666', mr: 1 }} />
                  <Typography variant="body2" sx={{ fontWeight: '600', color: '#666' }}>
                    Current Plan
                  </Typography>
                </Box>
                <Box sx={{ pl: 3.5 }}>
                  <Box
                    sx={{
                      ...getMembershipColor(customer.membershipPlan),
                      px: 2,
                      py: 1,
                      borderRadius: 1,
                      display: 'inline-block',
                      fontWeight: '500',
                      fontSize: '0.875rem',
                      border: '1px solid',
                      borderColor: getMembershipColor(customer.membershipPlan).color + '40'
                    }}
                  >
                    {customer.membershipPlan}
                  </Box>
                </Box>
              </Box>
            </Stack>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default InformationTab;