import React from "react";
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  useMediaQuery,
  useTheme,
} from "@mui/material";

const SubscriptionTab = ({ subscription = [], onBack }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const formatDuration = (duration) => {
    return duration === 1 ? `${duration} Month` : `${duration} Months`;
  };

  const formatPrice = (amount) => {
    return `$${amount.toFixed(2)}`;
  };

  if (isMobile) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: '600', color: '#333', mb: 3 }}>
          Customer Subscriptions
        </Typography>
        {subscription?.map((subscription, index) => (
          <Card key={index} sx={{ mb: 2, border: '1px solid #E0E0E0' }}>
            <CardContent>
              <Typography variant="subtitle1" sx={{ fontWeight: '500', mb: 2 }}>
                {subscription.planName}
              </Typography>
              <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                <strong>Duration:</strong> {formatDuration(subscription.duration)}
              </Typography>
              <Typography variant="body2" sx={{ color: '#666' }}>
                <strong>Price:</strong> {formatPrice(subscription.amount)}
              </Typography>
            </CardContent>
          </Card>
        ))}
        {!subscription?.length && (
          <Typography variant="body1" color="textSecondary" sx={{ textAlign: 'center', py: 4 }}>
            No subscriptions found
          </Typography>
        )}
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h6" sx={{ fontWeight: '600', color: '#333', mb: 3 }}>
        Subscriptions
      </Typography>
      <TableContainer component={Paper} sx={{ border: '1px solid #E0E0E0' }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#F8F9FA' }}>
              <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>
                Plan Name
              </TableCell>
              <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>
                Duration
              </TableCell>
              <TableCell sx={{ fontWeight: '600', color: '#666', fontSize: '12px', textTransform: 'uppercase' }}>
                Price
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {subscription?.map((subscription, index) => (
              <TableRow key={index} hover>
                <TableCell sx={{ fontWeight: '500' }}>
                  {subscription.planName}
                </TableCell>
                <TableCell>
                  {formatDuration(subscription.duration)}
                </TableCell>
                <TableCell sx={{ fontWeight: '500', color: '#2E7D32' }}>
                  {formatPrice(subscription.amount)}
                </TableCell>
              </TableRow>
            ))}
            {!subscription?.length && (
              <TableRow>
                <TableCell colSpan={3} sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="textSecondary">
                    No subscriptions found
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default SubscriptionTab;