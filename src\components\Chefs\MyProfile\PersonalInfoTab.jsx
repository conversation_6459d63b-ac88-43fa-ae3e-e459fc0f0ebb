import React, { useState, useRef } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  Paper,
  Avatar,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Alert,
  Snackbar,
  IconButton,
} from "@mui/material";
import {
  PhotoCamera,
  Save,
  Cancel,
  Edit,
  Person,
  Email,
  Phone,
  LocationOn,
  Description,
  Work,
} from "@mui/icons-material";
import { apiPostFormData } from "../../../api/apiManager";
import { useDispatch, useSelector } from "react-redux";
import {
  selectUserType,
  updateProfileImage,
} from "../../../reducers/auth.reducer";

// Country codes data
const countryCodes = [
  { code: "+1", country: "US/CA", label: "+1 (US/Canada)" },
  { code: "+44", country: "UK", label: "+44 (UK)" },
  { code: "+33", country: "FR", label: "+33 (France)" },
  { code: "+49", country: "DE", label: "+49 (Germany)" },
  { code: "+81", country: "JP", label: "+81 (Japan)" },
  { code: "+86", country: "CN", label: "+86 (China)" },
  { code: "+91", country: "IN", label: "+91 (India)" },
  { code: "+61", country: "AU", label: "+61 (Australia)" },
  { code: "+55", country: "BR", label: "+55 (Brazil)" },
  { code: "+34", country: "ES", label: "+34 (Spain)" },
  { code: "+39", country: "IT", label: "+39 (Italy)" },
  { code: "+31", country: "NL", label: "+31 (Netherlands)" },
  { code: "+46", country: "SE", label: "+46 (Sweden)" },
  { code: "+47", country: "NO", label: "+47 (Norway)" },
  { code: "+45", country: "DK", label: "+45 (Denmark)" },
  { code: "+94", country: "LK", label: "+94 (Sri Lanka)" },
];

// Countries data
const countries = [
  "United States",
  "Canada",
  "United Kingdom",
  "France",
  "Germany",
  "Japan",
  "China",
  "India",
  "Australia",
  "Brazil",
  "Spain",
  "Italy",
  "Netherlands",
  "Sweden",
  "Norway",
  "Denmark",
  "Switzerland",
  "Austria",
  "Belgium",
  "Ireland",
  "New Zealand",
  "South Korea",
  "Singapore",
  "Mexico",
  "Argentina",
  "Sri Lanka",
];

const PersonalInfoTab = ({ chef, onUpdate }) => {
  console.log("Chef", chef);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const fileInputRef = useRef(null);
  const dispatch = useDispatch();
  const userType = useSelector(selectUserType);
  // Split the chef name into first and last name
  const nameParts = chef?.name?.split(" ") || ["", ""];
  const firstName = nameParts[0] || "";
  const lastName = nameParts.slice(1).join(" ") || "";

  // Extract phone number and country code
  const phoneMatch = chef?.phoneNumber?.match(/^(\+\d+)(.*)$/);
  const initialCountryCode = phoneMatch ? phoneMatch[1] : "+1";
  const initialPhoneNumber = phoneMatch ? phoneMatch[2].replace(/\D/g, "") : "";
  const [imageChanged, setImageChanged] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setSaving] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const [formData, setFormData] = useState({
    firstName: firstName,
    lastName: lastName,
    email: chef?.email || "",
    countryCode: chef?.countryCode,
    phoneNumber: chef?.phoneNumber,
    country: chef.country || "", // Remove country code from location
    address: chef?.address || "",
    city: chef?.city || "",
    postalCode: chef?.postalCode || "",
    chefTitle: chef?.title || "",
    description: chef?.description || "",
    imagePath: chef?.imagePath || "",
    image: chef?.image || "",
    imageFile: null,
  });

  const handleInputChange = (field) => (event) => {
    setFormData({
      ...formData,
      [field]: event.target.value,
    });
  };
  const resizeImage = (
    file,
    maxWidth = 800,
    maxHeight = 600,
    quality = 0.8
  ) => {
    return new Promise((resolve) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        ctx.drawImage(img, 0, 0, width, height);
        canvas.toBlob(resolve, "image/jpeg", quality);
      };

      img.src = URL.createObjectURL(file);
    });
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        setSnackbar({
          open: true,
          message: "Image size should be less than 5MB",
          severity: "error",
        });
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setFormData({
          ...formData,
          image: e.target.result,
          imageFile: file, // Add this line to store the actual file
        });
        setImageChanged(true);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Validate required fields
      if (!formData.firstName.trim() || !formData.lastName.trim()) {
        setSnackbar({
          open: true,
          message: "First name and last name are required",
          severity: "error",
        });
        setSaving(false);
        return;
      }

      if (!formData.email.trim()) {
        setSnackbar({
          open: true,
          message: "Email is required",
          severity: "error",
        });
        setSaving(false);
        return;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        setSnackbar({
          open: true,
          message: "Please enter a valid email address",
          severity: "error",
        });
        setSaving(false);
        return;
      }
      let updatedFormData = { ...formData };
      let updatedImage = null;
      // Handle image upload if changed
      if (imageChanged && formData.imageFile) {
        const optimizedImage = await resizeImage(formData.imageFile);
        const imageFormData = new FormData();
        imageFormData.append("file", optimizedImage);

        try {
          const imageResponse = await apiPostFormData(
            "/api/chef/save-chef-image",
            imageFormData
          );
          const imageName = imageResponse.data.data.filenames[0];
          updatedImage = imageName;
          updatedFormData.image = imageName;
        } catch (imageError) {
          console.error("Image upload failed:", imageError);
          setSnackbar({
            open: true,
            message: "Failed to upload image. Please try again.",
            severity: "error",
          });
          setSaving(false);
          return;
        }
      }else{
        updatedFormData.image = formData.imagePath
      }

      // Call the parent update function with form data
      const result = await onUpdate(updatedFormData);

      if (result.success) {
        if (imageChanged) {
          dispatch(
            updateProfileImage({
              profileImage: updatedImage,
              userType,
            })
          );
        }
        setIsEditing(false);
        setSnackbar({
          open: true,
          message: result.message,
          severity: "success",
        });
      } else {
        setSnackbar({
          open: true,
          message: result.message,
          severity: "error",
        });
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: "Failed to update profile. Please try again.",
        severity: "error",
      });
    }
    setSaving(false);
  };

  const handleCancel = () => {
    // Reset form data to original values
    setFormData({
      firstName: firstName,
      lastName: lastName,
      email: chef?.email || "",
      countryCode: chef.countryCode,
      phoneNumber: chef.phoneNumber,
      country: chef.country,
      address: chef?.address || "",
      city: chef?.city || "",
      postalCode: chef?.postalCode || "",
      chefTitle: chef?.title || "",
      description: chef?.description || "",
      imagePath: chef?.imagePath || "",
      image: chef?.image || "",
    });
    setIsEditing(false);
  };
  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ""}${lastName?.charAt(0) || ""}`.toUpperCase();
  };

  return (
    <Box>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
          flexDirection: { xs: "column", sm: "row" },
          gap: { xs: 1, sm: 0 },
        }}
      >
        <Typography
          variant="h5"
          sx={{
            fontWeight: "600",
            fontSize: { xs: "1.25rem", sm: "1.5rem" },
            color: "#333",
          }}
        >
          Personal Information
        </Typography>

        {!isEditing ? (
          <Button
            variant="contained"
            startIcon={<Edit />}
            onClick={() => setIsEditing(true)}
            sx={{
              backgroundColor: "#6C5CE7",
              "&:hover": { backgroundColor: "#5B4FD1" },
              textTransform: "none",
              px: 3,
              fontSize: { xs: "0.875rem", sm: "1rem" },
            }}
          >
            Edit Profile
          </Button>
        ) : (
          <Box sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<Cancel />}
              onClick={handleCancel}
              disabled={loading}
              sx={{
                color: "#666",
                borderColor: "#ddd",
                "&:hover": { borderColor: "#999", backgroundColor: "#f5f5f5" },
                textTransform: "none",
                fontSize: { xs: "0.875rem", sm: "1rem" },
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<Save />}
              onClick={handleSave}
              disabled={loading}
              sx={{
                backgroundColor: "#6C5CE7",
                "&:hover": { backgroundColor: "#5B4FD1" },
                textTransform: "none",
                fontSize: { xs: "0.875rem", sm: "1rem" },
              }}
            >
              {loading ? "Saving..." : "Save Changes"}
            </Button>
          </Box>
        )}
      </Box>

      {/* Profile Image Section */}
      <Paper sx={{ p: 3, mb: 3, border: "1px solid #E0E0E0", borderRadius: 2 }}>
        <Typography
          variant="h6"
          sx={{
            fontWeight: "600",
            mb: 2,
            fontSize: { xs: "1.1rem", sm: "1.25rem" },
            color: "#333",
          }}
        >
          Profile Picture
        </Typography>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 3,
            flexDirection: { xs: "column", sm: "row" },
          }}
        >
          <Avatar
            src={formData.image}
            sx={{
              width: { xs: 100, sm: 100 },
              height: { xs: 100, sm: 100 },
              fontSize: "2rem",
              backgroundColor: "#1e3a8a",
              color: "white",
            }}
          >
            {getInitials(formData.firstName, formData.lastName)}
          </Avatar>

          {isEditing && (
            <Box>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleImageUpload}
                accept="image/*"
                style={{ display: "none" }}
              />
              <Button
                variant="outlined"
                startIcon={<PhotoCamera />}
                onClick={() => fileInputRef.current?.click()}
                sx={{
                  textTransform: "none",
                  color: "#6C5CE7",
                  borderColor: "#6C5CE7",
                  "&:hover": {
                    borderColor: "#5B4FD1",
                    backgroundColor: "#F8F7FF",
                  },
                }}
              >
                Upload Photo
              </Button>
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{ mt: 1, fontSize: "0.875rem" }}
              >
                Max size: 5MB. Supported: JPG, PNG, GIF
              </Typography>
            </Box>
          )}
        </Box>
      </Paper>

      {/* Personal Details Form */}
      <Paper sx={{ p: 3, border: "1px solid #E0E0E0", borderRadius: 2 }}>
        <Typography
          variant="h6"
          sx={{
            fontWeight: "600",
            mb: 3,
            fontSize: { xs: "1.1rem", sm: "1.25rem" },
            color: "#333",
          }}
        >
          Personal Details
        </Typography>

        <Grid container spacing={3}>
          {/* First Name & Last Name */}
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="First Name"
              value={formData.firstName}
              onChange={handleInputChange("firstName")}
              disabled={!isEditing}
              required
              InputProps={{
                startAdornment: <Person sx={{ color: "#6C5CE7", mr: 1 }} />,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": {
                    borderColor: "#6C5CE7",
                  },
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#6C5CE7",
                },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Last Name"
              value={formData.lastName}
              onChange={handleInputChange("lastName")}
              disabled={!isEditing}
              required
              InputProps={{
                startAdornment: <Person sx={{ color: "#6C5CE7", mr: 1 }} />,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": {
                    borderColor: "#6C5CE7",
                  },
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#6C5CE7",
                },
              }}
            />
          </Grid>

          {/* Email */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Email Address"
              type="email"
              value={formData.email}
              onChange={handleInputChange("email")}
              disabled={!isEditing}
              required
              InputProps={{
                startAdornment: <Email sx={{ color: "#6C5CE7", mr: 1 }} />,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": {
                    borderColor: "#6C5CE7",
                  },
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#6C5CE7",
                },
              }}
            />
          </Grid>

          {/* Phone Number */}
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth disabled={!isEditing}>
              <InputLabel>Country Code</InputLabel>
              <Select
                value={formData.countryCode}
                onChange={handleInputChange("countryCode")}
                label="Country Code"
                sx={{
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#6C5CE7",
                  },
                }}
              >
                {countryCodes.map((item) => (
                  <MenuItem key={item.code} value={item.code}>
                    {item.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              label="Phone Number"
              value={formData.phoneNumber}
              onChange={handleInputChange("phoneNumber")}
              disabled={!isEditing}
              InputProps={{
                startAdornment: <Phone sx={{ color: "#6C5CE7", mr: 1 }} />,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": {
                    borderColor: "#6C5CE7",
                  },
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#6C5CE7",
                },
              }}
            />
          </Grid>

          {/* Country */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth disabled={!isEditing}>
              <InputLabel>Country</InputLabel>
              <Select
                value={formData.country}
                onChange={handleInputChange("country")}
                label="Country"
                sx={{
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#6C5CE7",
                  },
                }}
              >
                {countries.map((country) => (
                  <MenuItem key={country} value={country}>
                    {country}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* City */}
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="City"
              value={formData.city}
              onChange={handleInputChange("city")}
              disabled={!isEditing}
              InputProps={{
                startAdornment: <LocationOn sx={{ color: "#6C5CE7", mr: 1 }} />,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": {
                    borderColor: "#6C5CE7",
                  },
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#6C5CE7",
                },
              }}
            />
          </Grid>

          {/* Address */}
          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              label="Address"
              value={formData.address}
              onChange={handleInputChange("address")}
              disabled={!isEditing}
              InputProps={{
                startAdornment: <LocationOn sx={{ color: "#6C5CE7", mr: 1 }} />,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": {
                    borderColor: "#6C5CE7",
                  },
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#6C5CE7",
                },
              }}
            />
          </Grid>

          {/* Postal Code */}
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Postal Code"
              value={formData.postalCode}
              onChange={handleInputChange("postalCode")}
              disabled={!isEditing}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": {
                    borderColor: "#6C5CE7",
                  },
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#6C5CE7",
                },
              }}
            />
          </Grid>

          {/* Chef Title */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Chef Title"
              value={formData.chefTitle}
              onChange={handleInputChange("chefTitle")}
              disabled={!isEditing}
              placeholder="e.g., Asian Fusion Specialist & Pastry Chef"
              InputProps={{
                startAdornment: <Work sx={{ color: "#6C5CE7", mr: 1 }} />,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": {
                    borderColor: "#6C5CE7",
                  },
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#6C5CE7",
                },
              }}
            />
          </Grid>

          {/* Description */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              value={formData.description}
              onChange={handleInputChange("description")}
              disabled={!isEditing}
              multiline
              rows={4}
              placeholder="Tell us about yourself, your cooking style, experience, and what makes you unique as a chef..."
              InputProps={{
                startAdornment: (
                  <Description
                    sx={{
                      color: "#6C5CE7",
                      mr: 1,
                      alignSelf: "flex-start",
                      mt: 1,
                    }}
                  />
                ),
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": {
                    borderColor: "#6C5CE7",
                  },
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#6C5CE7",
                },
              }}
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PersonalInfoTab;
