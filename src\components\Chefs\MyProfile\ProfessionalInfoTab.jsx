import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Card,
  CardContent,
  Divider,
  FormControlLabel,
  Checkbox,
  Alert,
  Snackbar,
  useTheme,
  useMediaQuery,
  Stack,
  Autocomplete,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  School as SchoolIcon,
  Work as WorkIcon,
  Schedule as ScheduleIcon,
  EventBusy as EventBusyIcon,
  AttachMoney as MoneyIcon,
  Category as CategoryIcon,
} from "@mui/icons-material";
import {
  getAllChefCategories,
  updateProfessionalInfo,
  getChefProfessionalInfoById,
} from "../../../app/service/chef.service";

const ProfessionalInfoTab = ({ chef, onUpdate }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));

  // States
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [availableCategories, setAvailableCategories] = useState([]);
  // Add new state for menu items
  const [menuItemsByMenu, setMenuItemsByMenu] = useState({});

  // Add new dialog state for menu items
  const [menuItemDialog, setMenuItemDialog] = useState({
    open: false,
    data: null,
    menuId: null,
    index: -1,
  });
  // Professional Data States
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [education, setEducation] = useState([]);
  const [experience, setExperience] = useState([]);
  const [availableDays, setAvailableDays] = useState([]);
  const [holidays, setHolidays] = useState([]);
  const [rates, setRates] = useState([]);
  const [dailyRates, setDailyRates] = useState([]);
  const [fixedPricesEnabled, setFixedPricesEnabled] = useState(false);
  const [menusByCategory, setMenusByCategory] = useState({});
  const [pricesByMenu, setPricesByMenu] = useState({});

  // Dialog States
  const [educationDialog, setEducationDialog] = useState({
    open: false,
    data: null,
    index: -1,
  });
  const [experienceDialog, setExperienceDialog] = useState({
    open: false,
    data: null,
    index: -1,
  });
  const [dayDialog, setDayDialog] = useState({
    open: false,
    data: null,
    index: -1,
  });
  const [holidayDialog, setHolidayDialog] = useState({
    open: false,
    selectedDates: [],
  });
  const [rateDialog, setRateDialog] = useState({
    open: false,
    data: null,
    index: -1,
  });

  const [dailyRateDialog, setDailyRateDialog] = useState({
    open: false,
    data: null,
    index: -1,
  });
  const [menuDialog, setMenuDialog] = useState({
    open: false,
    data: null,
    categoryId: null,
    index: -1,
  });
  const [fixedPriceDialog, setFixedPriceDialog] = useState({
    open: false,
    data: null,
    menuId: null,
    index: -1,
  });

  const daysOfWeek = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];

  // Load Categories
  const loadCategories = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getAllChefCategories();
      if (response?.data?.categories) {
        setAvailableCategories(response.data.categories);
      }
    } catch (error) {
      console.error("Error loading categories:", error);
      setSnackbar({
        open: true,
        message: "Failed to load categories",
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // Initialize data from chef prop
  useEffect(() => {
    const loadProfessionalInfo = async () => {
      if (chef?.id) {
        let professionalData = null;
        try {
          setLoading(true);
          const response = await getChefProfessionalInfoById(chef.id);

          if (response?.data) {
            professionalData = response.data;

            // Initialize with API data
            setSelectedCategories(
              (professionalData.categories || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setEducation(
              (professionalData.education || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setExperience(
              (professionalData.experience || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setAvailableDays(
              (professionalData.availableDays || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setHolidays(
              (professionalData.holidays || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setRates(
              (professionalData.rates || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );

            setDailyRates(
              (professionalData.dailyRates || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setFixedPricesEnabled(professionalData.fixedPricesEnabled || false);
            setMenusByCategory(
              Object.fromEntries(
                Object.entries(professionalData.menusByCategory || {}).map(
                  ([categoryId, menus]) => [
                    categoryId,
                    menus.map((menu) => ({
                      ...menu,
                      isNew: false,
                      isUpdated: false,
                      isRemoved: false,
                    })),
                  ]
                )
              )
            );
            setPricesByMenu(
              Object.fromEntries(
                Object.entries(professionalData.pricesByMenu || {}).map(
                  ([menuId, prices]) => [
                    menuId,
                    prices.map((price) => ({
                      ...price,
                      isNew: false,
                      isUpdated: false,
                      isRemoved: false,
                    })),
                  ]
                )
              )
            );

            setMenuItemsByMenu(
              Object.fromEntries(
                Object.entries(professionalData.menuItemsByMenu || {}).map(
                  ([menuId, items]) => [
                    menuId,
                    items.map((item) => ({
                      ...item,
                      isNew: false,
                      isUpdated: false,
                      isRemoved: false,
                    })),
                  ]
                )
              )
            );
          }
        } catch (error) {
          console.error("Error loading professional info:", error);
          setSnackbar({
            open: true,
            message: "Failed to load professional information",
            severity: "error",
          });
          if (professionalData) {
            // Fallback to chef prop data if API fails
            setSelectedCategories(
              (professionalData.categories || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setEducation(
              (professionalData.education || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setExperience(
              (professionalData.experience || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setAvailableDays(
              (professionalData.availableDays || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setHolidays(
              (professionalData.holidays || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setRates(
              (professionalData.rates || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );

            setDailyRates(
              (professionalData.dailyRates || []).map((item) => ({
                ...item,
                isNew: false,
                isUpdated: false,
                isRemoved: false,
              }))
            );
            setFixedPricesEnabled(professionalData.fixedPricesEnabled || false);
            setMenusByCategory(
              Object.fromEntries(
                Object.entries(professionalData.menusByCategory || {}).map(
                  ([categoryId, menus]) => [
                    categoryId,
                    menus.map((menu) => ({
                      ...menu,
                      isNew: false,
                      isUpdated: false,
                      isRemoved: false,
                    })),
                  ]
                )
              )
            );
            setPricesByMenu(
              Object.fromEntries(
                Object.entries(professionalData.pricesByMenu || {}).map(
                  ([menuId, prices]) => [
                    menuId,
                    prices.map((price) => ({
                      ...price,
                      isNew: false,
                      isUpdated: false,
                      isRemoved: false,
                    })),
                  ]
                )
              )
            );

            setMenuItemsByMenu(
              Object.fromEntries(
                Object.entries(professionalData.menuItemsByMenu || {}).map(
                  ([menuId, items]) => [
                    menuId,
                    items.map((item) => ({
                      ...item,
                      isNew: false,
                      isUpdated: false,
                      isRemoved: false,
                    })),
                  ]
                )
              )
            );
          } else {
            setSelectedCategories([]);
            setEducation([]);
            setExperience([]);
            setAvailableDays([]);
            setHolidays([]);
            setRates([]);
            setDailyRates([]);
            setFixedPricesEnabled(false);
            setMenusByCategory({});
            setPricesByMenu({});
            setMenuItemsByMenu({});
          }
          // ... continue with other fallback data
        } finally {
          setLoading(false);
        }
      }
      loadCategories();
    };

    loadProfessionalInfo();
  }, [chef, loadCategories]);

  // Category Management
  const handleCategoryChange = (event, newValue) => {
    if (
      newValue &&
      !selectedCategories.some(
        (cat) => cat.category_name === newValue.category_name && !cat.isRemoved
      )
    ) {
      setSelectedCategories([
        ...selectedCategories,
        {
          ...newValue,
          isNew: true,
          isUpdated: false,
          isRemoved: false,
        },
      ]);

      // Initialize empty menu array for new category if fixed prices enabled
      if (fixedPricesEnabled) {
        setMenusByCategory((prev) => ({
          ...prev,
          [newValue.id]: [],
        }));
      }
    }
  };

  const removeCategory = (categoryToRemove) => {
    setSelectedCategories((prev) =>
      prev.map((cat) =>
        cat.category_name === categoryToRemove.category_name
          ? {
              ...cat,
              isRemoved: true,
              isNew: false,
              isUpdated: false,
            }
          : cat
      )
    );

    // Remove menus for this category if fixed prices enabled
    if (fixedPricesEnabled && menusByCategory[categoryToRemove.id]) {
      setMenusByCategory((prev) => ({
        ...prev,
        [categoryToRemove.id]: prev[categoryToRemove.id].map((menu) => ({
          ...menu,
          isRemoved: true,
          isNew: false,
          isUpdated: false,
        })),
      }));
    }
  };

  // Education Management
  const handleEducationSave = (educationData) => {
    if (educationDialog.index >= 0) {
      const updatedEducation = [...education];
      updatedEducation[educationDialog.index] = {
        ...updatedEducation[educationDialog.index],
        ...educationData,
        isUpdated: !updatedEducation[educationDialog.index].isNew,
        isNew: updatedEducation[educationDialog.index].isNew,
        isRemoved: false,
      };
      setEducation(updatedEducation);
    } else {
      setEducation([
        ...education,
        {
          ...educationData,
          id: Date.now(),
          isNew: true,
          isUpdated: false,
          isRemoved: false,
        },
      ]);
    }
    setEducationDialog({ open: false, data: null, index: -1 });
  };

  const removeEducation = (index) => {
    const updatedEducation = [...education];
    if (updatedEducation[index].isNew) {
      updatedEducation.splice(index, 1);
    } else {
      updatedEducation[index] = {
        ...updatedEducation[index],
        isRemoved: true,
        isNew: false,
        isUpdated: false,
      };
    }
    setEducation(updatedEducation);
  };

  // Experience Management
  const handleExperienceSave = (experienceData) => {
    if (experienceDialog.index >= 0) {
      const updatedExperience = [...experience];
      updatedExperience[experienceDialog.index] = {
        ...updatedExperience[experienceDialog.index],
        ...experienceData,
        isUpdated: !updatedExperience[experienceDialog.index].isNew,
        isNew: updatedExperience[experienceDialog.index].isNew,
        isRemoved: false,
      };
      setExperience(updatedExperience);
    } else {
      setExperience([
        ...experience,
        {
          ...experienceData,
          id: Date.now(),
          isNew: true,
          isUpdated: false,
          isRemoved: false,
        },
      ]);
    }
    setExperienceDialog({ open: false, data: null, index: -1 });
  };

  const removeExperience = (index) => {
    const updatedExperience = [...experience];
    if (updatedExperience[index].isNew) {
      updatedExperience.splice(index, 1);
    } else {
      updatedExperience[index] = {
        ...updatedExperience[index],
        isRemoved: true,
        isNew: false,
        isUpdated: false,
      };
    }
    setExperience(updatedExperience);
  };

  // Available Days Management
  const handleDaySave = (dayData) => {
    if (dayDialog.index >= 0) {
      const updatedDays = [...availableDays];
      updatedDays[dayDialog.index] = {
        ...updatedDays[dayDialog.index],
        ...dayData,
        isUpdated: !updatedDays[dayDialog.index].isNew,
        isNew: updatedDays[dayDialog.index].isNew,
        isRemoved: false,
      };
      setAvailableDays(updatedDays);
    } else {
      setAvailableDays([
        ...availableDays,
        {
          ...dayData,
          id: Date.now(),
          isNew: true,
          isUpdated: false,
          isRemoved: false,
        },
      ]);
    }
    setDayDialog({ open: false, data: null, index: -1 });
  };

  const removeDay = (index) => {
    const updatedDays = [...availableDays];
    if (updatedDays[index].isNew) {
      updatedDays.splice(index, 1);
    } else {
      updatedDays[index] = {
        ...updatedDays[index],
        isRemoved: true,
        isNew: false,
        isUpdated: false,
      };
    }
    setAvailableDays(updatedDays);
  };

  // Holiday Management
  const handleHolidayAdd = (selectedDates) => {
    const newHolidays = selectedDates.map((date) => ({
      id: Date.now() + Math.random(),
      holiday_date: date,
      isNew: true,
      isUpdated: false,
      isRemoved: false,
    }));
    setHolidays([...holidays, ...newHolidays]);
    setHolidayDialog({ open: false, selectedDates: [] });
  };

  const removeHoliday = (index) => {
    const updatedHolidays = [...holidays];
    if (updatedHolidays[index].isNew) {
      updatedHolidays.splice(index, 1);
    } else {
      updatedHolidays[index] = {
        ...updatedHolidays[index],
        isRemoved: true,
        isNew: false,
        isUpdated: false,
      };
    }
    setHolidays(updatedHolidays);
  };

  // Rate Management
  const handleRateSave = (rateData) => {
    if (rateDialog.index >= 0) {
      const updatedRates = [...rates];
      updatedRates[rateDialog.index] = {
        ...updatedRates[rateDialog.index],
        ...rateData,
        isUpdated: !updatedRates[rateDialog.index].isNew,
        isNew: updatedRates[rateDialog.index].isNew,
        isRemoved: false,
      };
      setRates(updatedRates);
    } else {
      setRates([
        ...rates,
        {
          ...rateData,
          id: Date.now(),
          isNew: true,
          isUpdated: false,
          isRemoved: false,
        },
      ]);
    }
    setRateDialog({ open: false, data: null, index: -1 });
  };

  const removeRate = (index) => {
    const updatedRates = [...rates];
    if (updatedRates[index].isNew) {
      updatedRates.splice(index, 1);
    } else {
      updatedRates[index] = {
        ...updatedRates[index],
        isRemoved: true,
        isNew: false,
        isUpdated: false,
      };
    }
    setRates(updatedRates);
  };

  const handleDailyRateSave = (rateData) => {
    if (dailyRateDialog.index >= 0) {
      const updatedRates = [...dailyRates];
      updatedRates[dailyRateDialog.index] = {
        ...updatedRates[dailyRateDialog.index],
        ...rateData,
        isUpdated: !updatedRates[dailyRateDialog.index].isNew,
        isNew: updatedRates[dailyRateDialog.index].isNew,
        isRemoved: false,
      };
      setDailyRates(updatedRates);
    } else {
      setDailyRates([
        ...dailyRates,
        {
          ...rateData,
          id: Date.now(),
          isNew: true,
          isUpdated: false,
          isRemoved: false,
        },
      ]);
    }
    setDailyRateDialog({ open: false, data: null, index: -1 });
  };

  const removeDailyRate = (index) => {
    const updatedRates = [...dailyRates];
    if (updatedRates[index].isNew) {
      updatedRates.splice(index, 1);
    } else {
      updatedRates[index] = {
        ...updatedRates[index],
        isRemoved: true,
        isNew: false,
        isUpdated: false,
      };
    }
    setDailyRates(updatedRates);
  };

  // Fixed Prices Toggle
  const handleFixedPricesToggle = (enabled) => {
    setFixedPricesEnabled(enabled);
    if (enabled) {
      // Initialize menu arrays for selected categories
      const initialMenus = {};
      selectedCategories
        .filter((cat) => !cat.isRemoved)
        .forEach((cat) => {
          initialMenus[cat.id] = [];
        });
      setMenusByCategory(initialMenus);
    } else {
      // Mark all menus and prices as removed
      setMenusByCategory((prev) =>
        Object.fromEntries(
          Object.entries(prev).map(([categoryId, menus]) => [
            categoryId,
            menus.map((menu) => ({
              ...menu,
              isRemoved: true,
              isNew: false,
              isUpdated: false,
            })),
          ])
        )
      );

      setPricesByMenu((prev) =>
        Object.fromEntries(
          Object.entries(prev).map(([menuId, prices]) => [
            menuId,
            prices.map((price) => ({
              ...price,
              isRemoved: true,
              isNew: false,
              isUpdated: false,
            })),
          ])
        )
      );
      // ADD: Mark all menu items as removed
      setMenuItemsByMenu((prev) =>
        Object.fromEntries(
          Object.entries(prev).map(([menuId, items]) => [
            menuId,
            items.map((item) => ({
              ...item,
              isRemoved: true,
              isNew: false,
              isUpdated: false,
            })),
          ])
        )
      );
    }
  };

  // Menu Management
  const handleMenuSave = (menuData) => {
    const { categoryId } = menuDialog;
    console.log("Menu Data:", menuData);
    console.log("Category ID:", categoryId);
    if (menuDialog.index >= 0) {
      const updatedMenus = [...(menusByCategory[categoryId] || [])];
      updatedMenus[menuDialog.index] = {
        ...updatedMenus[menuDialog.index],
        ...menuData,
        isUpdated: !updatedMenus[menuDialog.index].isNew,
        isNew: updatedMenus[menuDialog.index].isNew,
        isRemoved: false,
      };
      setMenusByCategory((prev) => ({
        ...prev,
        [categoryId]: updatedMenus,
      }));
    } else {
      const newMenu = {
        ...menuData,
        id: Date.now(),
        categoryId,
        isNew: true,
        isUpdated: false,
        isRemoved: false,
      };
      setMenusByCategory((prev) => ({
        ...prev,
        [categoryId]: [...(prev[categoryId] || []), newMenu],
      }));
      // Initialize empty prices array for new menu
      setPricesByMenu((prev) => ({
        ...prev,
        [newMenu.id]: [],
      }));
      setMenuItemsByMenu((prev) => ({
        ...prev,
        [newMenu.id]: [],
      }));
    }
    setMenuDialog({ open: false, data: null, categoryId: null, index: -1 });
  };

  const removeMenu = (categoryId, index) => {
    const updatedMenus = [...(menusByCategory[categoryId] || [])];
    const menuToRemove = updatedMenus[index];

    if (menuToRemove.isNew) {
      updatedMenus.splice(index, 1);
      // Remove prices for this menu
      setPricesByMenu((prev) => {
        const { [menuToRemove.id]: removed, ...rest } = prev;
        return rest;
      });
      setMenuItemsByMenu((prev) => {
        const { [menuToRemove.id]: removed, ...rest } = prev;
        return rest;
      });
    } else {
      updatedMenus[index] = {
        ...updatedMenus[index],
        isRemoved: true,
        isNew: false,
        isUpdated: false,
      };
      // Mark prices as removed
      if (pricesByMenu[menuToRemove.id]) {
        setPricesByMenu((prev) => ({
          ...prev,
          [menuToRemove.id]: prev[menuToRemove.id].map((price) => ({
            ...price,
            isRemoved: true,
            isNew: false,
            isUpdated: false,
          })),
        }));
      }
      if (menuItemsByMenu[menuToRemove.id]) {
        setMenuItemsByMenu((prev) => ({
          ...prev,
          [menuToRemove.id]: prev[menuToRemove.id].map((item) => ({
            ...item,
            isRemoved: true,
            isNew: false,
            isUpdated: false,
          })),
        }));
      }
    }
    setMenusByCategory((prev) => ({
      ...prev,
      [categoryId]: updatedMenus,
    }));
  };

  // Fixed Price Management
  const handleFixedPriceSave = (priceData) => {
    const { menuId } = fixedPriceDialog;
    if (fixedPriceDialog.index >= 0) {
      const updatedPrices = [...(pricesByMenu[menuId] || [])];
      updatedPrices[fixedPriceDialog.index] = {
        ...updatedPrices[fixedPriceDialog.index],
        ...priceData,
        isUpdated: !updatedPrices[fixedPriceDialog.index].isNew,
        isNew: updatedPrices[fixedPriceDialog.index].isNew,
        isRemoved: false,
      };
      setPricesByMenu((prev) => ({
        ...prev,
        [menuId]: updatedPrices,
      }));
    } else {
      setPricesByMenu((prev) => ({
        ...prev,
        [menuId]: [
          ...(prev[menuId] || []),
          {
            ...priceData,
            id: Date.now(),
            menuId,
            isNew: true,
            isUpdated: false,
            isRemoved: false,
          },
        ],
      }));
    }
    setFixedPriceDialog({ open: false, data: null, menuId: null, index: -1 });
  };

  const removeFixedPrice = (menuId, index) => {
    const updatedPrices = [...(pricesByMenu[menuId] || [])];
    if (updatedPrices[index].isNew) {
      updatedPrices.splice(index, 1);
    } else {
      updatedPrices[index] = {
        ...updatedPrices[index],
        isRemoved: true,
        isNew: false,
        isUpdated: false,
      };
    }
    setPricesByMenu((prev) => ({
      ...prev,
      [menuId]: updatedPrices,
    }));
  };

  const handleMenuItemSave = (menuItemData) => {
    const { menuId } = menuItemDialog;
    if (menuItemDialog.index >= 0) {
      const updatedMenuItems = [...(menuItemsByMenu[menuId] || [])];
      updatedMenuItems[menuItemDialog.index] = {
        ...updatedMenuItems[menuItemDialog.index],
        ...menuItemData,
        isUpdated: !updatedMenuItems[menuItemDialog.index].isNew,
        isNew: updatedMenuItems[menuItemDialog.index].isNew,
        isRemoved: false,
      };
      setMenuItemsByMenu((prev) => ({
        ...prev,
        [menuId]: updatedMenuItems,
      }));
    } else {
      setMenuItemsByMenu((prev) => ({
        ...prev,
        [menuId]: [
          ...(prev[menuId] || []),
          {
            ...menuItemData,
            id: Date.now(),
            menuId,
            isNew: true,
            isUpdated: false,
            isRemoved: false,
          },
        ],
      }));
    }
    setMenuItemDialog({ open: false, data: null, menuId: null, index: -1 });
  };

  const removeMenuItem = (menuId, index) => {
    const updatedMenuItems = [...(menuItemsByMenu[menuId] || [])];
    if (updatedMenuItems[index].isNew) {
      updatedMenuItems.splice(index, 1);
    } else {
      updatedMenuItems[index] = {
        ...updatedMenuItems[index],
        isRemoved: true,
        isNew: false,
        isUpdated: false,
      };
    }
    setMenuItemsByMenu((prev) => ({
      ...prev,
      [menuId]: updatedMenuItems,
    }));
  };

  // Save All Changes
  const handleSaveAll = useCallback(async () => {
    try {
      setSaving(true);
      const payload = {
        categories: selectedCategories,
        education: education,
        experience: experience,
        availableDays: availableDays,
        holidays: holidays,
        rates: rates,
        dailyRates: dailyRates, // Add this
        fixedPricesEnabled: fixedPricesEnabled, // Add this
        menusByCategory: menusByCategory, // Add this
        pricesByMenu: pricesByMenu, // Add this
        menuItemsByMenu: menuItemsByMenu,
      };

      const response = await updateProfessionalInfo(chef.id, payload);
      if (parseInt(response.responseCode) === 1000) {
        //success
        setSnackbar({
          open: true,
          message: "Professional information updated successfully!",
          severity: "success",
        });

        if (onUpdate) {
          onUpdate({ ...chef, ...payload });
        }
      } else {
        setSnackbar({
          open: true,
          message:
            response.message || "Failed to update professional information",
          severity: "error",
        });
        console.error("Update failed:", response);
      }
    } catch (error) {
      console.error("Error updating professional info:", error);
      setSnackbar({
        open: true,
        message: "Failed to update professional information",
        severity: "error",
      });
    } finally {
      setSaving(false);
    }
  }, [
    chef,
    selectedCategories,
    education,
    experience,
    availableDays,
    holidays,
    rates,
    dailyRates, // Add this
    fixedPricesEnabled, // Add this
    menusByCategory, // Add this
    pricesByMenu, // Add this
    menuItemsByMenu,
    onUpdate,
  ]);

  return (
    <Box>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
          flexDirection: { xs: "column", sm: "row" },
          gap: { xs: 2, sm: 0 },
        }}
      >
        <Typography
          variant={isSmallScreen ? "h6" : "h5"}
          sx={{ fontWeight: "600", color: "#333" }}
        >
          Professional Information
        </Typography>
        <Button
          variant="contained"
          onClick={handleSaveAll}
          disabled={saving}
          startIcon={<SaveIcon />}
          sx={{
            backgroundColor: "#6C5CE7",
            "&:hover": { backgroundColor: "#5A4ED4" },
            px: { xs: 2, sm: 3 },
          }}
        >
          {saving ? "Saving..." : "Save All Changes"}
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Categories Section */}
        <Grid item xs={12}>
          <Paper
            sx={{
              p: { xs: 2, sm: 3 },
              border: "1px solid #E0E0E0",
              borderRadius: 2,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <CategoryIcon sx={{ color: "#6C5CE7", mr: 1 }} />
              <Typography variant="h6" sx={{ fontWeight: "600" }}>
                Specialization Categories
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Autocomplete
                options={availableCategories.filter(
                  (cat) =>
                    !selectedCategories.some(
                      (selected) =>
                        selected.category_name === cat.category_name &&
                        !selected.isRemoved
                    )
                )}
                getOptionLabel={(option) => option.category_name}
                onChange={handleCategoryChange}
                value={null}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Add Category"
                    placeholder="Select a category to add"
                    fullWidth
                  />
                )}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
                  },
                  "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
                }}
              />
            </Box>

            <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
              {selectedCategories
                .filter((category) => !category.isRemoved)
                .map((category, index) => (
                  <Chip
                    key={category.category_name}
                    label={category.category_name}
                    onDelete={() => removeCategory(category)}
                    sx={{
                      backgroundColor: "#6C5CE7",
                      color: "white",
                      "& .MuiChip-deleteIcon": { color: "white" },
                    }}
                  />
                ))}
            </Box>
          </Paper>
        </Grid>

        {/* Education Section */}
        <Grid item xs={12}>
          <Paper
            sx={{
              p: { xs: 2, sm: 3 },
              border: "1px solid #E0E0E0",
              borderRadius: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 1, sm: 0 },
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <SchoolIcon sx={{ color: "#6C5CE7", mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: "600" }}>
                  Education
                </Typography>
              </Box>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() =>
                  setEducationDialog({ open: true, data: null, index: -1 })
                }
                sx={{
                  borderColor: "#6C5CE7",
                  color: "#6C5CE7",
                  "&:hover": {
                    borderColor: "#5A4ED4",
                    backgroundColor: "#F8F7FF",
                  },
                }}
              >
                Add Education
              </Button>
            </Box>

            <Stack spacing={2}>
              {education
                .filter((edu) => !edu.isRemoved)
                .map((edu, index) => (
                  <Card
                    key={edu.id || index}
                    sx={{ border: "1px solid #E0E0E0" }}
                  >
                    <CardContent>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                          flexDirection: { xs: "column", sm: "row" },
                          gap: { xs: 2, sm: 0 },
                        }}
                      >
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="subtitle1"
                            sx={{ fontWeight: "600", mb: 1 }}
                          >
                            {edu.education}
                          </Typography>
                          <Typography
                            variant="body2"
                            color="textSecondary"
                            sx={{ mb: 0.5 }}
                          >
                            {edu.education_institution}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {edu.education_period}
                          </Typography>
                        </Box>
                        <Box sx={{ display: "flex", gap: 1 }}>
                          <IconButton
                            size="small"
                            onClick={() =>
                              setEducationDialog({
                                open: true,
                                data: edu,
                                index: education.findIndex(
                                  (e) => e.id === edu.id
                                ),
                              })
                            }
                            sx={{ color: "#6C5CE7" }}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() =>
                              removeEducation(
                                education.findIndex((e) => e.id === edu.id)
                              )
                            }
                            sx={{ color: "#f44336" }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
            </Stack>
          </Paper>
        </Grid>

        {/* Experience Section */}
        <Grid item xs={12}>
          <Paper
            sx={{
              p: { xs: 2, sm: 3 },
              border: "1px solid #E0E0E0",
              borderRadius: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 1, sm: 0 },
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <WorkIcon sx={{ color: "#6C5CE7", mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: "600" }}>
                  Experience
                </Typography>
              </Box>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() =>
                  setExperienceDialog({ open: true, data: null, index: -1 })
                }
                sx={{
                  borderColor: "#6C5CE7",
                  color: "#6C5CE7",
                  "&:hover": {
                    borderColor: "#5A4ED4",
                    backgroundColor: "#F8F7FF",
                  },
                }}
              >
                Add Experience
              </Button>
            </Box>

            <Stack spacing={2}>
              {experience
                .filter((exp) => !exp.isRemoved)
                .map((exp, index) => (
                  <Card
                    key={exp.id || index}
                    sx={{ border: "1px solid #E0E0E0" }}
                  >
                    <CardContent>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                          flexDirection: { xs: "column", sm: "row" },
                          gap: { xs: 2, sm: 0 },
                        }}
                      >
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="subtitle1"
                            sx={{ fontWeight: "600", mb: 1 }}
                          >
                            {exp.experience}
                          </Typography>
                          <Typography
                            variant="body2"
                            color="textSecondary"
                            sx={{ mb: 0.5 }}
                          >
                            {exp.work_place}
                          </Typography>
                          <Typography
                            variant="body2"
                            color="textSecondary"
                            sx={{ mb: 0.5 }}
                          >
                            {exp.experience_period}
                          </Typography>
                          {exp.is_current_work && (
                            <Chip
                              label="Current Position"
                              size="small"
                              sx={{
                                backgroundColor: "#4CAF50",
                                color: "white",
                              }}
                            />
                          )}
                        </Box>
                        <Box sx={{ display: "flex", gap: 1 }}>
                          <IconButton
                            size="small"
                            onClick={() =>
                              setExperienceDialog({
                                open: true,
                                data: exp,
                                index: experience.findIndex(
                                  (e) => e.id === exp.id
                                ),
                              })
                            }
                            sx={{ color: "#6C5CE7" }}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() =>
                              removeExperience(
                                experience.findIndex((e) => e.id === exp.id)
                              )
                            }
                            sx={{ color: "#f44336" }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
            </Stack>
          </Paper>
        </Grid>

        {/* Available Days Section */}
        <Grid item xs={12}>
          <Paper
            sx={{
              p: { xs: 2, sm: 3 },
              border: "1px solid #E0E0E0",
              borderRadius: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 1, sm: 0 },
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <ScheduleIcon sx={{ color: "#6C5CE7", mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: "600" }}>
                  Available Days
                </Typography>
              </Box>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() =>
                  setDayDialog({ open: true, data: null, index: -1 })
                }
                sx={{
                  borderColor: "#6C5CE7",
                  color: "#6C5CE7",
                  "&:hover": {
                    borderColor: "#5A4ED4",
                    backgroundColor: "#F8F7FF",
                  },
                }}
              >
                Add Available Day
              </Button>
            </Box>

            <Grid container spacing={2}>
              {availableDays
                .filter((day) => !day.isRemoved)
                .map((day, index) => (
                  <Grid item xs={12} sm={6} md={4} key={day.id || index}>
                    <Card sx={{ border: "1px solid #E0E0E0", height: "100%" }}>
                      <CardContent>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "flex-start",
                            mb: 1,
                          }}
                        >
                          <Typography
                            variant="subtitle1"
                            sx={{ fontWeight: "600" }}
                          >
                            {day.day}
                          </Typography>
                          <Box sx={{ display: "flex", gap: 0.5 }}>
                            <IconButton
                              size="small"
                              onClick={() =>
                                setDayDialog({
                                  open: true,
                                  data: day,
                                  index: availableDays.findIndex(
                                    (d) => d.id === day.id
                                  ),
                                })
                              }
                              sx={{ color: "#6C5CE7" }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() =>
                                removeDay(
                                  availableDays.findIndex(
                                    (d) => d.id === day.id
                                  )
                                )
                              }
                              sx={{ color: "#f44336" }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Box>
                        </Box>
                        <Typography variant="body2" color="textSecondary">
                          {day.start_time} - {day.end_time}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
            </Grid>
          </Paper>
        </Grid>

        {/* Holidays Section */}
        <Grid item xs={12}>
          <Paper
            sx={{
              p: { xs: 2, sm: 3 },
              border: "1px solid #E0E0E0",
              borderRadius: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 1, sm: 0 },
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <EventBusyIcon sx={{ color: "#6C5CE7", mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: "600" }}>
                  Holidays / Unavailable Dates
                </Typography>
              </Box>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() =>
                  setHolidayDialog({ open: true, selectedDates: [] })
                }
                sx={{
                  borderColor: "#6C5CE7",
                  color: "#6C5CE7",
                  "&:hover": {
                    borderColor: "#5A4ED4",
                    backgroundColor: "#F8F7FF",
                  },
                }}
              >
                Add Holiday
              </Button>
            </Box>

            <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
              {holidays
                .filter((holiday) => !holiday.isRemoved)
                .map((holiday, index) => (
                  <Chip
                    key={holiday.id || index}
                    label={new Date(holiday.holiday_date).toLocaleDateString()}
                    onDelete={() =>
                      removeHoliday(
                        holidays.findIndex((h) => h.id === holiday.id)
                      )
                    }
                    sx={{
                      backgroundColor: "#f44336",
                      color: "white",
                      "& .MuiChip-deleteIcon": { color: "white" },
                    }}
                  />
                ))}
            </Box>
          </Paper>
        </Grid>

        {/* Rates Section */}
        <Grid item xs={12}>
          <Paper
            sx={{
              p: { xs: 2, sm: 3 },
              border: "1px solid #E0E0E0",
              borderRadius: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 1, sm: 0 },
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <MoneyIcon sx={{ color: "#6C5CE7", mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: "600" }}>
                  Hourly Rates
                </Typography>
              </Box>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() =>
                  setRateDialog({ open: true, data: null, index: -1 })
                }
                sx={{
                  borderColor: "#6C5CE7",
                  color: "#6C5CE7",
                  "&:hover": {
                    borderColor: "#5A4ED4",
                    backgroundColor: "#F8F7FF",
                  },
                }}
              >
                Add Rate
              </Button>
            </Box>

            <Stack spacing={2}>
              {rates
                .filter((rate) => !rate.isRemoved)
                .map((rate, index) => (
                  <Card
                    key={rate.id || index}
                    sx={{ border: "1px solid #E0E0E0" }}
                  >
                    <CardContent>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <Typography
                          variant="h6"
                          sx={{ fontWeight: "600", color: "#6C5CE7" }}
                        >
                          ${rate.rate}/hour
                        </Typography>
                        <Box sx={{ display: "flex", gap: 1 }}>
                          <IconButton
                            size="small"
                            onClick={() =>
                              setRateDialog({
                                open: true,
                                data: rate,
                                index: rates.findIndex((r) => r.id === rate.id),
                              })
                            }
                            sx={{ color: "#6C5CE7" }}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() =>
                              removeRate(
                                rates.findIndex((r) => r.id === rate.id)
                              )
                            }
                            sx={{ color: "#f44336" }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
            </Stack>
          </Paper>
        </Grid>

        {/* Daily Rates Section */}
        <Grid item xs={12}>
          <Paper
            sx={{
              p: { xs: 2, sm: 3 },
              border: "1px solid #E0E0E0",
              borderRadius: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 1, sm: 0 },
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <MoneyIcon sx={{ color: "#6C5CE7", mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: "600" }}>
                  Daily Rates
                </Typography>
              </Box>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() =>
                  setDailyRateDialog({ open: true, data: null, index: -1 })
                }
                sx={{
                  borderColor: "#6C5CE7",
                  color: "#6C5CE7",
                  "&:hover": {
                    borderColor: "#5A4ED4",
                    backgroundColor: "#F8F7FF",
                  },
                }}
              >
                Add Daily Rate
              </Button>
            </Box>

            <Stack spacing={2}>
              {dailyRates
                .filter((rate) => !rate.isRemoved)
                .map((rate, index) => (
                  <Card
                    key={rate.id || index}
                    sx={{ border: "1px solid #E0E0E0" }}
                  >
                    <CardContent>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <Typography
                          variant="h6"
                          sx={{ fontWeight: "600", color: "#6C5CE7" }}
                        >
                          ${rate.rate}/day
                        </Typography>
                        <Box sx={{ display: "flex", gap: 1 }}>
                          <IconButton
                            size="small"
                            onClick={() =>
                              setDailyRateDialog({
                                open: true,
                                data: rate,
                                index: dailyRates.findIndex(
                                  (r) => r.id === rate.id
                                ),
                              })
                            }
                            sx={{ color: "#6C5CE7" }}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() =>
                              removeDailyRate(
                                dailyRates.findIndex((r) => r.id === rate.id)
                              )
                            }
                            sx={{ color: "#f44336" }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
            </Stack>
          </Paper>
        </Grid>

        {/* Fixed Prices Toggle Section */}
        <Grid item xs={12}>
          <Paper
            sx={{
              p: { xs: 2, sm: 3 },
              border: "1px solid #E0E0E0",
              borderRadius: 2,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <CategoryIcon sx={{ color: "#6C5CE7", mr: 1 }} />
              <Typography variant="h6" sx={{ fontWeight: "600" }}>
                Fixed Menu Prices
              </Typography>
            </Box>

            <FormControlLabel
              control={
                <Checkbox
                  checked={fixedPricesEnabled}
                  onChange={(e) => handleFixedPricesToggle(e.target.checked)}
                  disabled={
                    selectedCategories.filter((cat) => !cat.isRemoved)
                      .length === 0
                  }
                  sx={{
                    color: "#6C5CE7",
                    "&.Mui-checked": { color: "#6C5CE7" },
                    "&.Mui-disabled": { color: "#ccc" },
                  }}
                />
              }
              label={
                <Box>
                  <Typography variant="body1" sx={{ fontWeight: "500" }}>
                    Enable Fixed Menu Pricing
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {selectedCategories.filter((cat) => !cat.isRemoved)
                      .length === 0
                      ? "Please select at least one category to enable this feature"
                      : "Create custom menus with fixed prices for your selected categories"}
                  </Typography>
                </Box>
              }
            />
          </Paper>
        </Grid>

        {/* Menu Management Section - Only show if fixed prices enabled */}
        {/* Menu Management Section - RESTRUCTURED */}
        {fixedPricesEnabled && (
          <Grid item xs={12}>
            <Stack spacing={4}>
              {console.log(
                "🔍 Rendering menus for categories:",
                selectedCategories.filter((category) => !category.isRemoved)
              )}
              {selectedCategories
                .filter((category) => !category.isRemoved)
                .map((category) => (
                  <Paper
                    key={category.id}
                    sx={{
                      p: { xs: 2, sm: 3 },
                      border: "1px solid #E0E0E0",
                      borderRadius: 2,
                      backgroundColor: "#FAFAFA",
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        mb: 3,
                        flexDirection: { xs: "column", sm: "row" },
                        gap: { xs: 2, sm: 0 },
                      }}
                    >
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <CategoryIcon sx={{ color: "#6C5CE7", mr: 1 }} />
                        <Typography variant="h6" sx={{ fontWeight: "600" }}>
                          {category.category_name}
                        </Typography>
                      </Box>
                      <Button
                        variant="outlined"
                        startIcon={<AddIcon />}
                        onClick={() =>
                          setMenuDialog({
                            open: true,
                            data: null,
                            categoryId: category.category_id,
                            index: -1,
                          })
                        }
                        sx={{
                          borderColor: "#6C5CE7",
                          color: "#6C5CE7",
                          "&:hover": {
                            borderColor: "#5A4ED4",
                            backgroundColor: "#F8F7FF",
                          },
                        }}
                      >
                        Add Menu
                      </Button>
                    </Box>

                    {/* Menus for this category */}
                    <Stack spacing={3}>
                      {console.log(
                        `🔍 About to render menus for category ${category.id}:`,
                        menusByCategory
                      )}
                      {(menusByCategory[category.category_id] || [])
                        .filter((menu) => !menu.isRemoved)
                        .map((menu, menuIndex) => (
                          <Card
                            key={menu.id || menuIndex}
                            sx={{
                              border: "2px solid #E0E0E0",
                              backgroundColor: "white",
                              borderRadius: 2,
                            }}
                          >
                            <CardContent sx={{ p: 3 }}>
                              {/* Menu Header */}
                              <Box
                                sx={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  mb: 3,
                                  flexDirection: { xs: "column", sm: "row" },
                                  gap: { xs: 2, sm: 0 },
                                }}
                              >
                                <Typography
                                  variant="h6"
                                  sx={{ fontWeight: "600", color: "#333" }}
                                >
                                  📋 {menu.name}
                                </Typography>
                                <Box sx={{ display: "flex", gap: 1 }}>
                                  <IconButton
                                    size="small"
                                    onClick={() =>
                                      setMenuDialog({
                                        open: true,
                                        data: menu,
                                        categoryId: category.id,
                                        index: (
                                          menusByCategory[category.id] || []
                                        ).findIndex((m) => m.id === menu.id),
                                      })
                                    }
                                    sx={{ color: "#6C5CE7" }}
                                  >
                                    <EditIcon />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    onClick={() =>
                                      removeMenu(
                                        category.id,
                                        (
                                          menusByCategory[category.id] || []
                                        ).findIndex((m) => m.id === menu.id)
                                      )
                                    }
                                    sx={{ color: "#f44336" }}
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </Box>
                              </Box>

                              <Grid container spacing={3}>
                                {/* Menu Items Section */}
                                <Grid item xs={12} md={6}>
                                  <Box
                                    sx={{
                                      border: "1px solid #E0E0E0",
                                      borderRadius: 2,
                                      p: 2,
                                      backgroundColor: "#F9F9F9",
                                      height: "100%",
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        mb: 2,
                                      }}
                                    >
                                      <Typography
                                        variant="subtitle1"
                                        sx={{
                                          fontWeight: "600",
                                          color: "#666",
                                        }}
                                      >
                                        🍽️ Menu Items
                                      </Typography>
                                      <Button
                                        size="small"
                                        variant="outlined"
                                        startIcon={<AddIcon />}
                                        onClick={() =>
                                          setMenuItemDialog({
                                            open: true,
                                            data: null,
                                            menuId: menu.id,
                                            index: -1,
                                          })
                                        }
                                        sx={{
                                          borderColor: "#4CAF50",
                                          color: "#4CAF50",
                                          fontSize: "0.75rem",
                                          "&:hover": {
                                            borderColor: "#45A049",
                                            backgroundColor: "#F1F8E9",
                                          },
                                        }}
                                      >
                                        Add Item
                                      </Button>
                                    </Box>

                                    <Stack spacing={1}>
                                      {(menuItemsByMenu[menu.id] || [])
                                        .filter((item) => !item.isRemoved)
                                        .map((item, itemIndex) => (
                                          <Card
                                            key={item.id || itemIndex}
                                            sx={{
                                              border: "1px solid #DDD",
                                              backgroundColor: "white",
                                            }}
                                          >
                                            <CardContent
                                              sx={{
                                                p: 2,
                                                "&:last-child": { pb: 2 },
                                              }}
                                            >
                                              <Box
                                                sx={{
                                                  display: "flex",
                                                  justifyContent:
                                                    "space-between",
                                                  alignItems: "flex-start",
                                                }}
                                              >
                                                <Box sx={{ flex: 1 }}>
                                                  <Typography
                                                    variant="body2"
                                                    sx={{
                                                      fontWeight: "600",
                                                      mb: 0.5,
                                                    }}
                                                  >
                                                    {item.name}
                                                  </Typography>
                                                  {item.description && (
                                                    <Typography
                                                      variant="caption"
                                                      color="textSecondary"
                                                    >
                                                      {item.description}
                                                    </Typography>
                                                  )}
                                                </Box>
                                                <Box
                                                  sx={{
                                                    display: "flex",
                                                    gap: 0.5,
                                                  }}
                                                >
                                                  <IconButton
                                                    size="small"
                                                    onClick={() =>
                                                      setMenuItemDialog({
                                                        open: true,
                                                        data: item,
                                                        menuId: menu.id,
                                                        index: (
                                                          menuItemsByMenu[
                                                            menu.id
                                                          ] || []
                                                        ).findIndex(
                                                          (i) =>
                                                            i.id === item.id
                                                        ),
                                                      })
                                                    }
                                                    sx={{
                                                      color: "#4CAF50",
                                                      p: 0.5,
                                                    }}
                                                  >
                                                    <EditIcon fontSize="small" />
                                                  </IconButton>
                                                  <IconButton
                                                    size="small"
                                                    onClick={() =>
                                                      removeMenuItem(
                                                        menu.id,
                                                        (
                                                          menuItemsByMenu[
                                                            menu.id
                                                          ] || []
                                                        ).findIndex(
                                                          (i) =>
                                                            i.id === item.id
                                                        )
                                                      )
                                                    }
                                                    sx={{
                                                      color: "#f44336",
                                                      p: 0.5,
                                                    }}
                                                  >
                                                    <DeleteIcon fontSize="small" />
                                                  </IconButton>
                                                </Box>
                                              </Box>
                                            </CardContent>
                                          </Card>
                                        ))}
                                    </Stack>

                                    {(menuItemsByMenu[menu.id] || []).filter(
                                      (item) => !item.isRemoved
                                    ).length === 0 && (
                                      <Typography
                                        variant="body2"
                                        color="textSecondary"
                                        sx={{
                                          textAlign: "center",
                                          py: 3,
                                          fontStyle: "italic",
                                        }}
                                      >
                                        No menu items added yet
                                      </Typography>
                                    )}
                                  </Box>
                                </Grid>

                                {/* Pricing Options Section */}
                                <Grid item xs={12} md={6}>
                                  <Box
                                    sx={{
                                      border: "1px solid #E0E0E0",
                                      borderRadius: 2,
                                      p: 2,
                                      backgroundColor: "#FFF8E1",
                                      height: "100%",
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        mb: 2,
                                      }}
                                    >
                                      <Typography
                                        variant="subtitle1"
                                        sx={{
                                          fontWeight: "600",
                                          color: "#666",
                                        }}
                                      >
                                        💰 Pricing Options
                                      </Typography>
                                      <Button
                                        size="small"
                                        variant="outlined"
                                        startIcon={<AddIcon />}
                                        onClick={() =>
                                          setFixedPriceDialog({
                                            open: true,
                                            data: null,
                                            menuId: menu.id,
                                            index: -1,
                                          })
                                        }
                                        sx={{
                                          borderColor: "#FF9800",
                                          color: "#FF9800",
                                          fontSize: "0.75rem",
                                          "&:hover": {
                                            borderColor: "#F57C00",
                                            backgroundColor: "#FFF3E0",
                                          },
                                        }}
                                      >
                                        Add Price
                                      </Button>
                                    </Box>

                                    <Stack spacing={1}>
                                      {(pricesByMenu[menu.id] || [])
                                        .filter((price) => !price.isRemoved)
                                        .map((price, priceIndex) => (
                                          <Card
                                            key={price.id || priceIndex}
                                            sx={{
                                              border: "1px solid #FFE0B2",
                                              backgroundColor: "white",
                                            }}
                                          >
                                            <CardContent
                                              sx={{
                                                p: 2,
                                                "&:last-child": { pb: 2 },
                                              }}
                                            >
                                              <Box
                                                sx={{
                                                  display: "flex",
                                                  justifyContent:
                                                    "space-between",
                                                  alignItems: "center",
                                                }}
                                              >
                                                <Box>
                                                  <Typography
                                                    variant="h6"
                                                    sx={{
                                                      fontWeight: "600",
                                                      color: "#FF9800",
                                                      mb: 0.5,
                                                    }}
                                                  >
                                                    LKR {price.price}
                                                  </Typography>
                                                  <Typography
                                                    variant="caption"
                                                    color="textSecondary"
                                                  >
                                                    For {price.persons} person
                                                    {price.persons > 1
                                                      ? "s"
                                                      : ""}
                                                  </Typography>
                                                </Box>
                                                <Box
                                                  sx={{
                                                    display: "flex",
                                                    gap: 0.5,
                                                  }}
                                                >
                                                  <IconButton
                                                    size="small"
                                                    onClick={() =>
                                                      setFixedPriceDialog({
                                                        open: true,
                                                        data: price,
                                                        menuId: menu.id,
                                                        index: (
                                                          pricesByMenu[
                                                            menu.id
                                                          ] || []
                                                        ).findIndex(
                                                          (p) =>
                                                            p.id === price.id
                                                        ),
                                                      })
                                                    }
                                                    sx={{
                                                      color: "#FF9800",
                                                      p: 0.5,
                                                    }}
                                                  >
                                                    <EditIcon fontSize="small" />
                                                  </IconButton>
                                                  <IconButton
                                                    size="small"
                                                    onClick={() =>
                                                      removeFixedPrice(
                                                        menu.id,
                                                        (
                                                          pricesByMenu[
                                                            menu.id
                                                          ] || []
                                                        ).findIndex(
                                                          (p) =>
                                                            p.id === price.id
                                                        )
                                                      )
                                                    }
                                                    sx={{
                                                      color: "#f44336",
                                                      p: 0.5,
                                                    }}
                                                  >
                                                    <DeleteIcon fontSize="small" />
                                                  </IconButton>
                                                </Box>
                                              </Box>
                                            </CardContent>
                                          </Card>
                                        ))}
                                    </Stack>

                                    {(pricesByMenu[menu.id] || []).filter(
                                      (price) => !price.isRemoved
                                    ).length === 0 && (
                                      <Typography
                                        variant="body2"
                                        color="textSecondary"
                                        sx={{
                                          textAlign: "center",
                                          py: 3,
                                          fontStyle: "italic",
                                        }}
                                      >
                                        No pricing options added yet
                                      </Typography>
                                    )}
                                  </Box>
                                </Grid>
                              </Grid>
                            </CardContent>
                          </Card>
                        ))}
                    </Stack>

                    {(menusByCategory[category.category_id] || []).filter(
                      (menu) => !menu.isRemoved
                    ).length === 0 && (
                      <Typography
                        variant="body2"
                        color="textSecondary"
                        sx={{ textAlign: "center", py: 4, fontStyle: "italic" }}
                      >
                        No menus added for this category yet
                      </Typography>
                    )}
                  </Paper>
                ))}
            </Stack>
          </Grid>
        )}
      </Grid>

      {/* Dialogs */}
      <EducationDialog
        open={educationDialog.open}
        data={educationDialog.data}
        onClose={() =>
          setEducationDialog({ open: false, data: null, index: -1 })
        }
        onSave={handleEducationSave}
      />

      <ExperienceDialog
        open={experienceDialog.open}
        data={experienceDialog.data}
        onClose={() =>
          setExperienceDialog({ open: false, data: null, index: -1 })
        }
        onSave={handleExperienceSave}
      />

      <DayDialog
        open={dayDialog.open}
        data={dayDialog.data}
        onClose={() => setDayDialog({ open: false, data: null, index: -1 })}
        onSave={handleDaySave}
        daysOfWeek={daysOfWeek}
      />
      <MenuItemDialog
        open={menuItemDialog.open}
        data={menuItemDialog.data}
        onClose={() =>
          setMenuItemDialog({
            open: false,
            data: null,
            menuId: null,
            index: -1,
          })
        }
        onSave={handleMenuItemSave}
      />

      <HolidayDialog
        open={holidayDialog.open}
        selectedDates={holidayDialog.selectedDates}
        onClose={() => setHolidayDialog({ open: false, selectedDates: [] })}
        onSave={handleHolidayAdd}
      />

      <RateDialog
        open={rateDialog.open}
        data={rateDialog.data}
        onClose={() => setRateDialog({ open: false, data: null, index: -1 })}
        onSave={handleRateSave}
      />
      <DailyRateDialog
        open={dailyRateDialog.open}
        data={dailyRateDialog.data}
        onClose={() =>
          setDailyRateDialog({ open: false, data: null, index: -1 })
        }
        onSave={handleDailyRateSave}
      />

      {/* Menu Dialog */}
      <MenuDialog
        open={menuDialog.open}
        data={menuDialog.data}
        onClose={() =>
          setMenuDialog({
            open: false,
            data: null,
            categoryId: null,
            index: -1,
          })
        }
        onSave={handleMenuSave}
      />

      {/* Fixed Price Dialog */}
      <FixedPriceDialog
        open={fixedPriceDialog.open}
        data={fixedPriceDialog.data}
        onClose={() =>
          setFixedPriceDialog({
            open: false,
            data: null,
            menuId: null,
            index: -1,
          })
        }
        onSave={handleFixedPriceSave}
      />

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

// Education Dialog Component
const EducationDialog = ({ open, data, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    education: "",
    education_institution: "",
    education_period: "",
  });

  useEffect(() => {
    if (data) {
      setFormData({
        education: data.education || "",
        education_institution: data.education_institution || "",
        education_period: data.education_period || "",
      });
    } else {
      setFormData({
        education: "",
        education_institution: "",
        education_period: "",
      });
    }
  }, [data]);

  const handleSave = () => {
    if (
      formData.education &&
      formData.education_institution &&
      formData.education_period
    ) {
      onSave(formData);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{data ? "Edit Education" : "Add Education"}</DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <TextField
            fullWidth
            label="Education/Degree"
            value={formData.education}
            onChange={(e) =>
              setFormData({ ...formData, education: e.target.value })
            }
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
          <TextField
            fullWidth
            label="Institution"
            value={formData.education_institution}
            onChange={(e) =>
              setFormData({
                ...formData,
                education_institution: e.target.value,
              })
            }
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
          <TextField
            fullWidth
            label="Period (e.g., 2018-2020)"
            value={formData.education_period}
            onChange={(e) =>
              setFormData({ ...formData, education_period: e.target.value })
            }
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: "#6C5CE7",
            "&:hover": { backgroundColor: "#5A4ED4" },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Experience Dialog Component
const ExperienceDialog = ({ open, data, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    experience: "",
    work_place: "",
    experience_period: "",
    is_current_work: false,
  });

  useEffect(() => {
    if (data) {
      setFormData({
        experience: data.experience || "",
        work_place: data.work_place || "",
        experience_period: data.experience_period || "",
        is_current_work: data.is_current_work || false,
      });
    } else {
      setFormData({
        experience: "",
        work_place: "",
        experience_period: "",
        is_current_work: false,
      });
    }
  }, [data]);

  const handleSave = () => {
    if (
      formData.experience &&
      formData.work_place &&
      formData.experience_period
    ) {
      onSave(formData);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{data ? "Edit Experience" : "Add Experience"}</DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <TextField
            fullWidth
            label="Position/Role"
            value={formData.experience}
            onChange={(e) =>
              setFormData({ ...formData, experience: e.target.value })
            }
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
          <TextField
            fullWidth
            label="Workplace"
            value={formData.work_place}
            onChange={(e) =>
              setFormData({ ...formData, work_place: e.target.value })
            }
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
          <TextField
            fullWidth
            label="Period (e.g., 2018-2021)"
            value={formData.experience_period}
            onChange={(e) =>
              setFormData({ ...formData, experience_period: e.target.value })
            }
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={formData.is_current_work}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    is_current_work: e.target.checked,
                  })
                }
                sx={{
                  color: "#6C5CE7",
                  "&.Mui-checked": { color: "#6C5CE7" },
                }}
              />
            }
            label="This is my current position"
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: "#6C5CE7",
            "&:hover": { backgroundColor: "#5A4ED4" },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Day Dialog Component
const DayDialog = ({ open, data, onClose, onSave, daysOfWeek }) => {
  const [formData, setFormData] = useState({
    day: "",
    start_time: "",
    end_time: "",
  });

  useEffect(() => {
    if (data) {
      setFormData({
        day: data.day || "",
        start_time: data.start_time || "",
        end_time: data.end_time || "",
      });
    } else {
      setFormData({
        day: "",
        start_time: "",
        end_time: "",
      });
    }
  }, [data]);

  const handleSave = () => {
    if (formData.day && formData.start_time && formData.end_time) {
      onSave(formData);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {data ? "Edit Available Day" : "Add Available Day"}
      </DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <FormControl fullWidth required>
            <InputLabel sx={{ "&.Mui-focused": { color: "#6C5CE7" } }}>
              Day
            </InputLabel>
            <Select
              value={formData.day}
              label="Day"
              onChange={(e) =>
                setFormData({ ...formData, day: e.target.value })
              }
              sx={{
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#6C5CE7",
                },
              }}
            >
              {daysOfWeek.map((day) => (
                <MenuItem key={day} value={day}>
                  {day}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <TextField
            fullWidth
            label="Start Time"
            type="time"
            value={formData.start_time}
            onChange={(e) =>
              setFormData({ ...formData, start_time: e.target.value })
            }
            required
            InputLabelProps={{ shrink: true }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
          <TextField
            fullWidth
            label="End Time"
            type="time"
            value={formData.end_time}
            onChange={(e) =>
              setFormData({ ...formData, end_time: e.target.value })
            }
            required
            InputLabelProps={{ shrink: true }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: "#6C5CE7",
            "&:hover": { backgroundColor: "#5A4ED4" },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Holiday Dialog Component
const HolidayDialog = ({ open, selectedDates, onClose, onSave }) => {
  const [dates, setDates] = useState([]);

  useEffect(() => {
    setDates(selectedDates);
  }, [selectedDates]);

  const handleDateAdd = () => {
    const dateInput = document.getElementById("holiday-date-input");
    const selectedDate = dateInput.value;
    if (selectedDate && !dates.includes(selectedDate)) {
      setDates([...dates, selectedDate]);
    }
  };

  const handleDateRemove = (dateToRemove) => {
    setDates(dates.filter((date) => date !== dateToRemove));
  };

  const handleSave = () => {
    if (dates.length > 0) {
      onSave(dates);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add Holiday Dates</DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
            <TextField
              id="holiday-date-input"
              type="date"
              label="Select Date"
              InputLabelProps={{ shrink: true }}
              sx={{
                flex: 1,
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
                },
                "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
              }}
            />
            <Button
              variant="outlined"
              onClick={handleDateAdd}
              sx={{
                borderColor: "#6C5CE7",
                color: "#6C5CE7",
                "&:hover": {
                  borderColor: "#5A4ED4",
                  backgroundColor: "#F8F7FF",
                },
              }}
            >
              Add
            </Button>
          </Box>

          {dates.length > 0 && (
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Selected Dates:
              </Typography>
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                {dates.map((date, index) => (
                  <Chip
                    key={index}
                    label={new Date(date).toLocaleDateString()}
                    onDelete={() => handleDateRemove(date)}
                    sx={{
                      backgroundColor: "#f44336",
                      color: "white",
                      "& .MuiChip-deleteIcon": { color: "white" },
                    }}
                  />
                ))}
              </Box>
            </Box>
          )}
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={dates.length === 0}
          sx={{
            backgroundColor: "#6C5CE7",
            "&:hover": { backgroundColor: "#5A4ED4" },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Rate Dialog Component
const RateDialog = ({ open, data, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    rate: "",
  });

  useEffect(() => {
    if (data) {
      setFormData({
        rate: data.rate || "",
      });
    } else {
      setFormData({
        rate: "",
      });
    }
  }, [data]);

  const handleSave = () => {
    if (formData.rate && parseFloat(formData.rate) > 0) {
      onSave({ rate: parseFloat(formData.rate) });
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{data ? "Edit Rate" : "Add Rate"}</DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <TextField
            fullWidth
            label="Hourly Rate ($)"
            type="number"
            value={formData.rate}
            onChange={(e) => setFormData({ ...formData, rate: e.target.value })}
            required
            inputProps={{ min: 0, step: 0.01 }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: "#6C5CE7",
            "&:hover": { backgroundColor: "#5A4ED4" },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Daily Rate Dialog Component
const DailyRateDialog = ({ open, data, onClose, onSave }) => {
  const [formData, setFormData] = useState({ rate: "" });

  useEffect(() => {
    if (data) {
      setFormData({ rate: data.rate || "" });
    } else {
      setFormData({ rate: "" });
    }
  }, [data]);

  const handleSave = () => {
    if (formData.rate && parseFloat(formData.rate) > 0) {
      onSave({ rate: parseFloat(formData.rate) });
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{data ? "Edit Daily Rate" : "Add Daily Rate"}</DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <TextField
            fullWidth
            label="Daily Rate ($)"
            type="number"
            value={formData.rate}
            onChange={(e) => setFormData({ ...formData, rate: e.target.value })}
            required
            inputProps={{ min: 0, step: 0.01 }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: "#6C5CE7",
            "&:hover": { backgroundColor: "#5A4ED4" },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Menu Dialog Component
const MenuDialog = ({ open, data, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    name: "",
  });

  useEffect(() => {
    if (data) {
      setFormData({
        name: data.name || "",
      });
    } else {
      setFormData({
        name: "",
      });
    }
  }, [data]);

  const handleSave = () => {
    if (formData.name) {
      onSave({
        name: formData.name,
      });
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{data ? "Edit Menu Item" : "Add Menu Item"}</DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <TextField
            fullWidth
            label="Menu Item Name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: "#6C5CE7",
            "&:hover": { backgroundColor: "#5A4ED4" },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Menu Item Dialog Component - NEW
const MenuItemDialog = ({ open, data, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
  });

  useEffect(() => {
    if (data) {
      setFormData({
        name: data.name || "",
        description: data.description || "",
      });
    } else {
      setFormData({
        name: "",
        description: "",
      });
    }
  }, [data]);

  const handleSave = () => {
    if (formData.name) {
      onSave({
        name: formData.name,
        description: formData.description,
      });
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{data ? "Edit Menu Item" : "Add Menu Item"}</DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <TextField
            fullWidth
            label="Menu Item Name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
          <TextField
            fullWidth
            label="Description (Optional)"
            multiline
            rows={3}
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: "#6C5CE7",
            "&:hover": { backgroundColor: "#5A4ED4" },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Fixed Price Dialog Component
const FixedPriceDialog = ({ open, data, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    price: "",
    persons: "",
  });

  useEffect(() => {
    if (data) {
      setFormData({
        price: data.price || "",
        persons: data.persons || "",
      });
    } else {
      setFormData({
        price: "",
        persons: "",
      });
    }
  }, [data]);

  const handleSave = () => {
    if (
      formData.price &&
      formData.persons &&
      parseFloat(formData.price) > 0 &&
      parseInt(formData.persons) > 0
    ) {
      onSave({
        price: parseFloat(formData.price),
        persons: parseInt(formData.persons),
      });
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {data ? "Edit Price Option" : "Add Price Option"}
      </DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <TextField
            fullWidth
            label="Price ($)"
            type="number"
            value={formData.price}
            onChange={(e) =>
              setFormData({ ...formData, price: e.target.value })
            }
            required
            inputProps={{ min: 0, step: 0.01 }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
          <TextField
            fullWidth
            label="Number of Persons"
            type="number"
            value={formData.persons}
            onChange={(e) =>
              setFormData({ ...formData, persons: e.target.value })
            }
            required
            inputProps={{ min: 1, step: 1 }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": { borderColor: "#6C5CE7" },
              },
              "& .MuiInputLabel-root.Mui-focused": { color: "#6C5CE7" },
            }}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: "#6C5CE7",
            "&:hover": { backgroundColor: "#5A4ED4" },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProfessionalInfoTab;
