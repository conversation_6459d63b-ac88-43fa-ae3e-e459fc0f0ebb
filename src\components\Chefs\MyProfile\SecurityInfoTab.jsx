import React, { useState } from 'react';
import {
  <PERSON><PERSON>graphy,
  TextField,
  Box,
  Button,
  Paper,
  Grid,
  IconButton,
  InputAdornment,
  Alert,
  Divider
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Security,
  Lock,
  Person
} from '@mui/icons-material';

export default function SecurityInfoTab({ chef, onUpdate }) {
  const [formData, setFormData] = useState({
    username: chef?.username || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [showPasswords, setShowPasswords] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false
  });

  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');

  const handleInputChange = (field) => (event) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    } else if (formData.username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters';
    }

    // Password validation (only if user is trying to change password)
    if (formData.newPassword || formData.confirmPassword || formData.currentPassword) {
      if (!formData.currentPassword) {
        newErrors.currentPassword = 'Current password is required to change password';
      }

      if (!formData.newPassword) {
        newErrors.newPassword = 'New password is required';
      } else if (formData.newPassword.length < 6) {
        newErrors.newPassword = 'New password must be at least 6 characters';
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your new password';
      } else if (formData.newPassword !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }

      // Check if new password is different from current
      if (formData.currentPassword === formData.newPassword) {
        newErrors.newPassword = 'New password must be different from current password';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    setSuccessMessage('');

    if (!validateForm()) {
      return;
    }

    // Simulate API call
    setTimeout(() => {
      // Update chef data if onUpdate is provided
      if (onUpdate && formData.username !== chef?.username) {
        onUpdate({
          ...chef,
          username: formData.username
        });
      }

      setSuccessMessage('Security information updated successfully!');
      
      // Clear password fields after successful update
      setFormData(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }));
    }, 1000);
  };

  const handleUsernameUpdate = () => {
    if (!formData.username.trim()) {
      setErrors({ username: 'Username is required' });
      return;
    }

    if (formData.username.length < 3) {
      setErrors({ username: 'Username must be at least 3 characters' });
      return;
    }

    // Update username only
    if (onUpdate) {
      onUpdate({
        ...chef,
        username: formData.username
      });
    }

    setSuccessMessage('Username updated successfully!');
    setErrors({});
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Security sx={{ color: '#6C5CE7', mr: 1, fontSize: 28 }} />
        <Typography 
          variant="h5" 
          sx={{ 
            fontWeight: '600', 
            color: '#333',
            fontSize: { xs: '1.3rem', sm: '1.5rem' }
          }}
        >
          Security Information
        </Typography>
      </Box>

      {/* Success Message */}
      {successMessage && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {successMessage}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Username Section */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3, border: '1px solid #E0E0E0', borderRadius: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Person sx={{ color: '#6C5CE7', mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: '600', color: '#333' }}>
                  Username Settings
                </Typography>
              </Box>
              
              <Grid container spacing={2} alignItems="end">
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    label="Username"
                    value={formData.username}
                    onChange={handleInputChange('username')}
                    error={!!errors.username}
                    helperText={errors.username}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Typography color="textSecondary">@</Typography>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: '#6C5CE7',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#6C5CE7',
                        },
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Button
                    variant="outlined"
                    onClick={handleUsernameUpdate}
                    sx={{
                      height: 56,
                      borderColor: '#6C5CE7',
                      color: '#6C5CE7',
                      '&:hover': {
                        borderColor: '#5B4BD6',
                        backgroundColor: '#F8F7FF'
                      }
                    }}
                  >
                    Update Username
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          <Divider sx={{ width: '100%', my: 2 }} />

          {/* Password Section */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3, border: '1px solid #E0E0E0', borderRadius: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Lock sx={{ color: '#6C5CE7', mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: '600', color: '#333' }}>
                  Change Password
                </Typography>
              </Box>

              <Grid container spacing={3}>
                {/* Current Password */}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    type={showPasswords.currentPassword ? 'text' : 'password'}
                    label="Current Password"
                    value={formData.currentPassword}
                    onChange={handleInputChange('currentPassword')}
                    error={!!errors.currentPassword}
                    helperText={errors.currentPassword}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => togglePasswordVisibility('currentPassword')}
                            edge="end"
                          >
                            {showPasswords.currentPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: '#6C5CE7',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#6C5CE7',
                        },
                      },
                    }}
                  />
                </Grid>

                {/* New Password */}
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    type={showPasswords.newPassword ? 'text' : 'password'}
                    label="New Password"
                    value={formData.newPassword}
                    onChange={handleInputChange('newPassword')}
                    error={!!errors.newPassword}
                    helperText={errors.newPassword || 'Minimum 6 characters'}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => togglePasswordVisibility('newPassword')}
                            edge="end"
                          >
                            {showPasswords.newPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: '#6C5CE7',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#6C5CE7',
                        },
                      },
                    }}
                  />
                </Grid>

                {/* Confirm Password */}
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    type={showPasswords.confirmPassword ? 'text' : 'password'}
                    label="Confirm New Password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange('confirmPassword')}
                    error={!!errors.confirmPassword}
                    helperText={errors.confirmPassword}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => togglePasswordVisibility('confirmPassword')}
                            edge="end"
                          >
                            {showPasswords.confirmPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: '#6C5CE7',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#6C5CE7',
                        },
                      },
                    }}
                  />
                </Grid>
              </Grid>

              {/* Update Password Button */}
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  type="submit"
                  variant="contained"
                  sx={{
                    backgroundColor: '#6C5CE7',
                    color: 'white',
                    px: 4,
                    py: 1.5,
                    '&:hover': {
                      backgroundColor: '#5B4BD6',
                    },
                    '&:disabled': {
                      backgroundColor: '#E0E0E0',
                    }
                  }}
                >
                  Update Password
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
}