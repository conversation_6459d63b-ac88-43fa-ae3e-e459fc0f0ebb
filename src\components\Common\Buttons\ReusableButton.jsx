import { But<PERSON> } from "@mui/material";
import React from "react";
// import { useNavigate } from "react-router-dom";

export default function ReusableButton({ text, navigateTo, onClick,type,disabled }) {
//   const navigate = useNavigate();

  const handleClick = () => {
    if (navigateTo) {
    //   navigate(/dashboard${navigateTo});
    }
    if (onClick) {
      onClick();
    }
  };
  return (
    <>
      <Button
        onClick={handleClick}
        type={type}
        disabled={disabled}
        sx={{
          fontSize: "12px",
          fontFamily: "'poppins', san-serif",
          textTransform: "capitalize",
          padding: "10px 40px",
          borderRadius: "5px",
          backgroundColor: "#000",
          color: "#ffffff",
          fontWeight: 500,
          "&:hover": {
            backgroundColor: "#000",
            color: "#ffffff",
          },
        }}
      >
        {text}
      </Button>
    </>
  );
}