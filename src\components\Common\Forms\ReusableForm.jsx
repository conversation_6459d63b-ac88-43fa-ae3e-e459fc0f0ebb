import React from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { 
  TextField,
  Button,
  Box,
  Autocomplete,
  FormControl,
  FormHelperText,
  Typography,
  Paper
} from '@mui/material';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ReusableButton from '../Buttons/ReusableButton';

const ReusableForm = ({
  fields = [],
  onSubmit,
  initialValues = {}
}) => {
  const { 
    control, 
    handleSubmit, 
    formState: { errors }
  } = useForm({
    defaultValues: initialValues
  });

  const renderField = (field) => {
    const { 
      type, 
      name, 
      label, 
      required, 
      validation = {}, 
      options = [],
      placeholder 
    } = field;

    const commonProps = {
      fullWidth: true,
      variant: "outlined",
      size: "medium",
      margin: "normal"
    };

    switch (type) {
      case 'text':
      case 'email':
      case 'password':
        return (
          <Controller
            key={name}
            name={name}
            control={control}
            rules={{
              required: required ? 'This field is required' : false,
              pattern: type === 'email' ? {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address'
              } : undefined,
              ...validation
            }}
            render={({ field }) => (
              <TextField
                {...field}
                {...commonProps}
                type={type}
                label={label}
                placeholder={placeholder}
                error={!!errors[name]}
                helperText={errors[name]?.message}
              />
            )}
          />
        );

      case 'date':
        return (
          <Controller
            key={name}
            name={name}
            control={control}
            rules={{
              required: required ? 'This field is required' : false,
              ...validation
            }}
            render={({ field: { value, onChange, ...field } }) => (
              <TextField
                {...field}
                {...commonProps}
                type="date"
                label={label}
                value={value || ''}
                onChange={onChange}
                InputLabelProps={{ shrink: true }}
                error={!!errors[name]}
                helperText={errors[name]?.message}
              />
            )}
          />
        );

      case 'time':
        return (
          <Controller
            key={name}
            name={name}
            control={control}
            rules={{
              required: required ? 'This field is required' : false,
              ...validation
            }}
            render={({ field: { value, onChange, ...field } }) => (
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <TimePicker
                  {...field}
                  label={label}
                  value={value ? new Date(value) : null}
                  onChange={(newValue) => {
                    onChange(newValue ? newValue.toISOString() : null);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      {...commonProps}
                      error={!!errors[name]}
                      helperText={errors[name]?.message}
                    />
                  )}
                />
              </LocalizationProvider>
            )}
          />
        );

      case 'autocomplete':
        return (
          <Controller
            key={name}
            name={name}
            control={control}
            rules={{
              required: required ? 'This field is required' : false,
              ...validation
            }}
            render={({ field: { onChange, value, ...field } }) => (
              <FormControl 
                fullWidth 
                error={!!errors[name]}
                margin="normal"
              >
                <Autocomplete
                  {...field}
                  options={options}
                  value={value ? options.find(option => option.value === value) || null : null}
                  onChange={(_, newValue) => onChange(newValue ? newValue.value : null)}
                  getOptionLabel={(option) => {
                    if (typeof option === 'string') return option;
                    return option.label || '';
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={label}
                      placeholder={placeholder}
                      error={!!errors[name]}
                      helperText={errors[name]?.message}
                    />
                  )}
                />
              </FormControl>
            )}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Typography variant="h6" gutterBottom>
          Add New Race Schedule
        </Typography>
        {fields.map(renderField)}
        <Box sx={{ mt: 3 }}>
         <ReusableButton text={} />
        </Box>
      </Box>
    </Paper>
  );
};

export default ReusableForm;