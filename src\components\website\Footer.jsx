// src/components/website/Footer.jsx
import React from "react";
import {
  Box,
  Container,
  Grid,
  Typography,
  Link,
  IconButton,
  Divider,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import {
  Facebook,
  Twitter,
  LinkedIn,
  Instagram,
  Email,
  Phone,
  LocationOn,
  TrendingUp,
  RestaurantMenu,
} from "@mui/icons-material";
import { motion } from "framer-motion";
import logo from "../../assets/perfect_solution.png";

const Footer = () => {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("md"));
  const theme = useTheme();

  const quickLinks = [
    { label: "Home", href: "/" },
    { label: "About Us", href: "/about-us" },
    { label: "Subscription", href: "/subscription" },
    // { label: "Investment Plans", href: "/investment" },
    { label: "Contact Us", href: "/contact-us" },
  ];

  const services = [
    { label: "Chef Booking", href: "#" },
    { label: "Membership Subscriptions", href: "#" },
  ];

  const socialLinks = [
    { icon: <Facebook />, href: "#", color: "#1877f2" },
    { icon: <Twitter />, href: "#", color: "#1da1f2" },
    { icon: <LinkedIn />, href: "#", color: "#0077b5" },
    { icon: <Instagram />, href: "#", color: "#e4405f" },
  ];

  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: "#1A1A1A",
        borderTop: 1,
        borderColor: "divider",
        mt: "auto",
      }}
    >
      <Container maxWidth="xl" sx={{ py: 6 }}>
        <Grid container spacing={4}>
          {/* Company Info */}
          <Grid item xs={12} md={4}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <Box sx={{ mb: 3 }}>
                <Box
                  sx={{ display: "flex", alignItems: "center", gap: 1, mb: 2 }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width: 32,
                      height: 32,
                      borderRadius: "50%",
                      backgroundColor: theme.palette.primary.main,
                    }}
                  >
                    <RestaurantMenu sx={{ color: "white", fontSize: 20 }} />
                  </Box>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      color: "white",
                    }}
                  >
                    Hire a Chef
                  </Typography>
                </Box>
                <Typography
                  variant="body2"
                  sx={{
                    mb: 3,
                    lineHeight: 1.6,
                    color: theme.palette.primary.contrastText,
                  }}
                >
                  Maximize your investment potential with our advanced ROI
                  calculation and analysis tools. Make informed financial
                  decisions with professional-grade investment insights.
                </Typography>

                {/* Social Links */}
                <Box sx={{ display: "flex", gap: 1 }}>
                  {socialLinks.map((social, index) => (
                    <IconButton
                      key={index}
                      href={social.href}
                      sx={{
                        color: theme.palette.primary.contrastText,
                        "&:hover": {
                          color: social.color,
                          backgroundColor: `${social.color}15`,
                        },
                      }}
                    >
                      {social.icon}
                    </IconButton>
                  ))}
                </Box>
              </Box>
            </motion.div>
          </Grid>

          {/* Quick Links */}
          <Grid item xs={12} sm={6} md={2}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  mb: 2,
                  color: theme.palette.primary.text,
                }}
              >
                Quick Links
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                {quickLinks.map((link, index) => (
                  <Link
                    key={index}
                    href={link.href}
                    sx={{
                      color: theme.palette.primary.contrastText,
                      textDecoration: "none",
                      fontSize: "0.875rem",
                      "&:hover": {
                        color: "primary.main",
                        textDecoration: "underline",
                      },
                    }}
                  >
                    {link.label}
                  </Link>
                ))}
              </Box>
            </motion.div>
          </Grid>

          {/* Services */}
          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  mb: 2,
                  color: theme.palette.primary.text,
                }}
              >
                Our Services
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                {services.map((service, index) => (
                  <Link
                    key={index}
                    href={service.href}
                    sx={{
                      color: theme.palette.primary.contrastText,
                      textDecoration: "none",
                      fontSize: "0.875rem",
                      "&:hover": {
                        color: "primary.main",
                        textDecoration: "underline",
                      },
                    }}
                  >
                    {service.label}
                  </Link>
                ))}
              </Box>
            </motion.div>
          </Grid>

          {/* Contact Info */}
          <Grid item xs={12} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  mb: 2,
                  color: theme.palette.primary.text,
                }}
              >
                Contact Info
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Email sx={{ color: "primary.main", fontSize: 18 }} />
                  <Typography
                    variant="body2"
                    sx={{ color: theme.palette.primary.contrastText }}
                  >
                    <EMAIL>
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Phone sx={{ color: "primary.main", fontSize: 18 }} />
                  <Typography
                    variant="body2"
                    sx={{ color: theme.palette.primary.contrastText }}
                  >
                    +****************
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "flex-start", gap: 1 }}>
                  <LocationOn sx={{ color: "primary.main", fontSize: 18 }} />
                  <Typography
                    variant="body2"
                    sx={{ color: theme.palette.primary.contrastText }}
                  >
                    123 Finance Street, Business District, NY 10001
                  </Typography>
                </Box>
              </Box>
            </motion.div>
          </Grid>
        </Grid>

        <Divider sx={{ my: 4 }} />

        {/* Bottom section */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "space-between",
              alignItems: "center",
              gap: 2,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography
                variant="body2"
                sx={{ color: theme.palette.primary.contrastText }}
              >
                © {new Date().getFullYear()} Hire a Chef. All rights reserved.
                Developed By
              </Typography>
              <img
                src={logo}
                onClick={() => window.open("https://perfectsolutioninternational.com/", "_blank")}               
                alt=""
                style={{ width: "120px", height: "auto", cursor: "pointer" }}
              />
            </Box>

            <Box sx={{ display: "flex", gap: 3 }}>
              <Link
                href="#"
                sx={{
                  color: theme.palette.primary.contrastText,
                  textDecoration: "none",
                  fontSize: "0.875rem",
                  "&:hover": {
                    color: "primary.main",
                    textDecoration: "underline",
                  },
                }}
              >
                Privacy Policy
              </Link>
              <Link
                href="#"
                sx={{
                  color: theme.palette.primary.contrastText,
                  textDecoration: "none",
                  fontSize: "0.875rem",
                  "&:hover": {
                    color: "primary.main",
                    textDecoration: "underline",
                  },
                }}
              >
                Terms of Service
              </Link>
              <Link
                href="#"
                sx={{
                  color: theme.palette.primary.contrastText,
                  textDecoration: "none",
                  fontSize: "0.875rem",
                  "&:hover": {
                    color: "primary.main",
                    textDecoration: "underline",
                  },
                }}
              >
                Cookie Policy
              </Link>
            </Box>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default Footer;
