// src/components/website/Header.jsx
import { useState } from "react";
import {
  AppBar,
  <PERSON><PERSON><PERSON>,
  Box,
  Typography,
  Button,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  useMediaQuery,
  Container,
  Slide,
  useScrollTrigger,
  TextField,
  InputAdornment,
  useTheme,
} from "@mui/material";
import {
  Menu as MenuIcon,
  Close as CloseIcon,
  RestaurantMenu,
  Search as SearchIcon,
} from "@mui/icons-material";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import { useNavigate, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
import { useSelector } from "react-redux";
import { selectIsLoggedIn, selectUserType } from "../../reducers/auth.reducer";

const HideOnScroll = ({ children }) => {
  const trigger = useScrollTrigger();
  return (
    <Slide appear={false} direction="down" in={!trigger}>
      {children}
    </Slide>
  );
};

const Header = () => {
  const [mobileOpen, setMobileOpen] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const isLoggedIn = useSelector(selectIsLoggedIn);
  const userType = useSelector(selectUserType);
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const isTablet = useMediaQuery((theme) => theme.breakpoints.down("md"));

  const theme = useTheme();

  const navigationItems = [
    { label: "Home", path: "/", hasDropdown: true },
    { label: "About Us", path: "/about-us", hasDropdown: true },
    { label: "Subscription", path: "/subscription", hasDropdown: true },
    { label: "Contact Us", path: "/contact-us", hasDropdown: false },
  ];

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleNavigation = (path) => {
    navigate(path);
    setMobileOpen(false);
  };

  const handleSignIn = () => {
    navigate("/auth/login");
  };

  const handleRegister = () => {
    navigate("/auth/register");
  };

  const handleAccountClick = () => {
    // Navigate to account/profile page or show account menu
    navigate("/account");
  };

  const drawer = (
    <Box sx={{ width: 280, height: "100%", zIndex: 1500 }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          p: 2,
          borderBottom: 1,
          borderColor: "divider",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: 32,
              height: 32,
              borderRadius: "50%",
              backgroundColor: theme.palette.primary.main,
            }}
          >
            <RestaurantMenu sx={{ color: "white", fontSize: 20 }} />
          </Box>
          <Typography
            variant="h6"
            sx={{ fontWeight: 700, color: "text.primary" }}
          >
            Hire a Chef
          </Typography>
        </Box>
        <IconButton onClick={handleDrawerToggle}>
          <CloseIcon />
        </IconButton>
      </Box>

      <List sx={{ px: 2, py: 2 }}>
        {navigationItems.map((item) => (
          <ListItem key={item.label} disablePadding sx={{ mb: 1 }}>
            <ListItemButton
              onClick={() => handleNavigation(item.path)}
              sx={{
                borderRadius: 2,
                fontSize: "14px",
                backgroundColor:
                  location.pathname === item.path
                    ? "primary.main"
                    : "transparent",
                color:
                  location.pathname === item.path
                    ? "primary.contrastText"
                    : "text.primary",
                "&:hover": {
                  backgroundColor:
                    location.pathname === item.path
                      ? "primary.dark"
                      : "action.hover",
                },
              }}
            >
              <ListItemText primary={item.label} />
            </ListItemButton>
          </ListItem>
        ))}

        <Box sx={{ mt: 3, display: "flex", flexDirection: "column", gap: 1 }}>
          <Button
            variant="outlined"
            onClick={handleSignIn}
            fullWidth
            sx={{
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              textTransform: "none",
              fontWeight: 500,
              letterSpacing: 0.5,
              "&:hover": {
                borderColor: theme.palette.primary.dark,
                backgroundColor: "rgba(255, 77, 0, 0.04)",
              },
            }}
          >
            Sign In
          </Button>
          {!isLoggedIn && (
            <>
              <Button
                variant="contained"
                onClick={handleRegister}
                fullWidth
                sx={{
                  backgroundColor: theme.palette.primary.main,
                  color: "white",
                  textTransform: "none",
                  fontWeight: 600,
                  letterSpacing: 0.5,
                  "&:hover": {
                    backgroundColor: theme.palette.primary.dark,
                  },
                }}
              >
                Register
              </Button>
            </>
          )}
        </Box>
      </List>
    </Box>
  );

  return (
    <>
      {/* Top Promotional Bar */}
      <Box
        sx={{
          backgroundColor: "#1A1A1A",
          color: "white",
          py: 1,
          textAlign: "center",
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1300,
          fontSize: "0.8rem",
        }}
      >
        <Container maxWidth="xl" backgroundColor="#1A1A1A">
          <Typography
            variant="body2"
            sx={{
              fontSize: "0.76rem",
              fontWeight: 400,
            }}
          >
            Discover the top chef platform on the market!
          </Typography>
        </Container>
      </Box>
      <HideOnScroll>
        <AppBar
          position="fixed"
          elevation={0}
          sx={{
            backgroundColor: "background.paper",
            backdropFilter: "blur(20px)",
            borderBottom: 1,
            borderColor: "divider",
            top: "37px", // Account for promotional bar height
            borderRadius: "20px 20px 0 0",
          }}
        >
          <Container maxWidth="xl">
            <Toolbar
              sx={{
                justifyContent: "space-between",
                py: 1.3,
                minHeight: "70px !important",
              }}
            >
              {/* Logo */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                    cursor: "pointer",
                  }}
                  onClick={() => navigate("/")}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width: 32,
                      height: 32,
                      borderRadius: "50%",
                      backgroundColor: theme.palette.primary.main,
                    }}
                  >
                    <RestaurantMenu sx={{ color: "white", fontSize: 20 }} />
                  </Box>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      color: "text.primary",
                      fontSize: "1.25rem",
                    }}
                  >
                    Hire a Chef
                  </Typography>
                </Box>
              </motion.div>

              {/* Desktop Navigation */}
              {!isMobile && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                    {navigationItems.map((item) => (
                      <Button
                        key={item.label}
                        onClick={() => handleNavigation(item.path)}
                        sx={{
                          color: "text.primary",
                          fontWeight: 500,
                          px: 2,
                          py: 1.5,
                          borderRadius: 1,
                          textTransform: "none",
                          fontSize: "0.9rem",
                          "&:hover": {
                            backgroundColor: "action.hover",
                          },
                        }}
                      >
                        {item.label}
                      </Button>
                    ))}
                  </Box>
                </motion.div>
              )}

              {/* Right side buttons */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  {/* Desktop Auth Buttons */}
                  {!isMobile && (
                    <>
                      {userType !== "Customer" && isLoggedIn && (
                        <>
                          <Button
                            variant="text"
                            onClick={handleSignIn}
                            sx={{
                              color: "text.primary",
                              fontWeight: 500,
                              px: 2,
                              py: 1,
                              borderRadius: 1,
                              letterSpacing: 0.5,
                              textTransform: "none",
                              "&:hover": {
                                backgroundColor: "action.hover",
                              },
                            }}
                          >
                            Sign In
                          </Button>
                        </>
                      )}

                      {!isLoggedIn && (
                        <>
                        <Button
                            variant="text"
                            onClick={handleSignIn}
                            sx={{
                              color: "text.primary",
                              fontWeight: 500,
                              px: 2,
                              py: 1,
                              borderRadius: 1,
                              letterSpacing: 0.5,
                              textTransform: "none",
                              "&:hover": {
                                backgroundColor: "action.hover",
                              },
                            }}
                          >
                            Sign In
                          </Button>
                          <Button
                            variant="contained"
                            onClick={handleRegister}
                            sx={{
                              backgroundColor: theme.palette.primary.main,
                              color: "white",
                              px: 3,
                              py: 1,
                              borderRadius: 1,
                              fontWeight: 600,
                              letterSpacing: 0.5,
                              textTransform: "none",
                              boxShadow: "none",
                              "&:hover": {
                                backgroundColor: theme.palette.primary.dark,
                                boxShadow: "0 2px 8px rgba(255, 77, 0, 0.3)",
                              },
                            }}
                          >
                            Register
                          </Button>
                        </>
                      )}
                    </>
                  )}

                  {/* Account Icon - Shows on both desktop and mobile */}

                  {/* //only visible if is login and userType only Customer(not disable) */}
                  {isLoggedIn && userType === "Customer" && (
                    <IconButton
                      onClick={handleAccountClick}
                      sx={{
                        color: "text.primary",
                        ml: 1,
                        "&:hover": {
                          backgroundColor: "action.hover",
                        },
                      }}
                    >
                      <AccountCircleIcon sx={{ fontSize: 28 }} />
                    </IconButton>
                  )}

                  {/* Mobile menu button */}
                  {isMobile && (
                    <IconButton
                      onClick={handleDrawerToggle}
                      sx={{
                        color: "text.primary",
                        "&:hover": {
                          backgroundColor: "action.hover",
                        },
                      }}
                    >
                      <MenuIcon />
                    </IconButton>
                  )}
                </Box>
              </motion.div>
            </Toolbar>
          </Container>
        </AppBar>
      </HideOnScroll>
      {/* Mobile Drawer */}
      <Drawer
        className="drawe-tes"
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
          sx: { zIndex: 1500 }, // <-- This sets the z-index on the modal root
        }}
        sx={{
          "& .MuiDrawer-paper": {
            boxSizing: "border-box",
            width: 280,
            backgroundColor: "background.paper",
            // zIndex:1500
          },

          "& .MuiDrawer-root": {
            // zIndex:1500
          },
        }}
      >
        {drawer}
      </Drawer>
      {/* Spacer for fixed header */}
      <Box sx={{ height: "40px" }} /> {/* Space for promotional bar */}
      <Toolbar sx={{ minHeight: "70px !important" }} />
    </>
  );
};

export default Header;
