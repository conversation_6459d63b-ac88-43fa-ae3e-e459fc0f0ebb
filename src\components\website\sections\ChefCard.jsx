// // src/components/ChefCard.jsx
// import React from "react";
// import {
//   Card,
//   CardContent,
//   CardMedia,
//   Typography,
//   Box,
//   Chip,
//   IconButton,
//   Rating,
//   Avatar,
// } from "@mui/material";
// import {
//   Visibility,
//   Favorite,
//   FavoriteBorder,
//   Verified,
//   LocationOn,
//   Schedule,
// } from "@mui/icons-material";

// const ChefCard = ({ chef }) => {
//   const [isFavorite, setIsFavorite] = React.useState(false);

//   const handleFavoriteClick = () => {
//     setIsFavorite(!isFavorite);
//   };

//   return (
//     <Card
//       sx={{
//         // maxWidth: 368,
//         height: '100%',
//         borderRadius: 2,
//       }}
//     >
//       <Box sx={{ position: "relative" }}>
//         <CardMedia
//           component="img"
//           height="220"
//           image={chef.image}
//           alt={chef.name}
//           sx={{ objectFit: "cover", p: 0.6, borderRadius: 2 }}
//         />
//         <Box
//           sx={{
//             position: "absolute",
//             top: 8,
//             right: 8,
//             display: "flex",
//             alignItems: "center",
//             gap: 0.5,
//             backgroundColor: "rgba(255,255,255,0.9)",
//             borderRadius: 1,
//             px: 1,
//             py: 0.5,
//           }}
//         >
//           <Visibility sx={{ fontSize: 14, color: "#ff5722" }} />
//           <Typography
//             variant="caption"
//             sx={{ color: "#ff5722", fontWeight: 500 }}
//           >
//             {chef.views} views
//           </Typography>
//         </Box>
//       </Box>

//       <CardContent sx={{ pb: 2 }}>
//         <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
//           <Typography
//             variant="body2"
//             component="h3"
//             sx={{ fontWeight: 500, color: "#9999A6" }}
//           >
//             {chef.name}
//           </Typography>
//           {chef.verified && (
//             <Verified sx={{ fontSize: 20, color: "#4caf50" }} />
//           )}
//         </Box>

//         <Typography
//           variant="body2"
//           sx={{
//             fontWeight: 500,
//             fontSize: "15px",
//             color: "#000",
//             letterSpacing: 0.5,
//             mb: 1,
//             lineHeight: 1.3,
//           }}
//         >
//           {chef.title}
//         </Typography>

//         <Box sx={{ display: "flex", alignItems: "center", gap: 0.5, mb: 1.5 }}>
//           <Rating
//             value={chef.rating}
//             precision={0.1}
//             size="small"
//             readOnly
//             sx={{ color: "#ffc107" }}
//           />
//           <Typography variant="body2" sx={{ color: "#666", ml: 0.5 }}>
//             {chef.rating.toFixed(1)} ({chef.reviews} review
//             {chef.reviews !== 1 ? "s" : ""})
//           </Typography>
//         </Box>

//         <Box
//           sx={{
//             display: "flex",
//             justifyContent: "space-between",
//             alignItems: "center",
//             mb: 2,
//           }}
//         >
//           <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
//             <Schedule sx={{ fontSize: 16, color: "#666" }} />
//             <Typography
//               variant="body2"
//               sx={{ color: "#666", fontSize: "14px", letterSpacing: 0.5 }}
//             >
//               Hourly Rate
//             </Typography>
//           </Box>
//           <Typography
//             variant="body1"
//             sx={{ fontWeight: 500, color: "#000", fontSize: "14px" }}
//           >
//             ${chef.hourlyRate} /hr
//           </Typography>
//         </Box>

//         <Box
//           sx={{
//             display: "flex",
//             justifyContent: "space-between",
//             alignItems: "center",
//             mb: 2,
//           }}
//         >
//           <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
//             <LocationOn sx={{ fontSize: 16, color: "#666" }} />
//             <Typography
//               variant="body2"
//               sx={{ color: "#666", fontSize: "14px", letterSpacing: 0.5 }}
//             >
//               Location
//             </Typography>
//           </Box>
//           <Typography
//             variant="body2"
//             sx={{ color: "#000", fontWeight: 500, fontSize: "14px" }}
//           >
//             {chef.location}
//           </Typography>
//         </Box>
//         <Box
//           sx={{
//             borderTop: "1px solid #ccc",
//             width: "100%",
//             my: 2, // vertical margin (theme spacing)
//           }}
//         />
//         <Box sx={{ display: "flex", gap: 1, mb: 2, flexWrap: "wrap" }}>
//           {chef.skills.slice(0, 3).map((skill, index) => (
//             <Chip
//               key={index}
//               label={skill}
//               size="small"
//               sx={{
//                 backgroundColor: "#f5f5f5",
//                 color: "#666",
//                 fontWeight: 500,
//                 fontSize: "0.75rem",
//               }}
//             />
//           ))}
//           {chef.skills.length > 3 && (
//             <Chip
//               label={`+${chef.skills.length - 3} more`}
//               size="small"
//               sx={{
//                 backgroundColor: "#f5f5f5",
//                 color: "#666",
//                 fontWeight: 500,
//                 fontSize: "0.75rem",
//               }}
//             />
//           )}
//         </Box>

//         <Box sx={{ display: "flex", gap: 1 }}>
//           <Box
//             component="button"
//             sx={{
//               flex: 1,
//               backgroundColor: "#ff5722",
//               color: "white",
//               border: "none",
//               borderRadius: 1,
//               py: 1.5,
//               px: 2,
//               fontWeight: 600,
//               cursor: "pointer",
//               "&:hover": {
//                 backgroundColor: "#e64a19",
//               },
//             }}
//           >
//             View profile
//           </Box>
//           <IconButton
//             onClick={handleFavoriteClick}
//             sx={{
//               border: "1px solid #ddd",
//               borderRadius: 1,
//               "&:hover": {
//                 backgroundColor: "#f5f5f5",
//               },
//             }}
//           >
//             {isFavorite ? (
//               <Favorite sx={{ color: "#f44336" }} />
//             ) : (
//               <FavoriteBorder />
//             )}
//           </IconButton>
//         </Box>
//       </CardContent>
//     </Card>
//   );
// };

// export default ChefCard;


// src/components/ChefCard.jsx
import React from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  CardContent,
  CardMedia,
  Typography,
  Box,
  Chip,
  IconButton,
  Rating,
  Avatar,
} from "@mui/material";
import {
  Visibility,
  Favorite,
  FavoriteBorder,
  Verified,
  LocationOn,
  Schedule,
} from "@mui/icons-material";

const ChefCard = ({ chef }) => {
  const [isFavorite, setIsFavorite] = React.useState(false);
  const navigate = useNavigate();

  const handleFavoriteClick = () => {
    setIsFavorite(!isFavorite);
  };

  const handleViewProfile = () => {
    // Navigate to chef profile page with chef ID
    navigate(`/chef-profile/${chef.id}`);
  };

  return (
    <Card
      sx={{
        // maxWidth: 368,
        height: '100%',
        borderRadius: 2,
      }}
    >
      <Box sx={{ position: "relative" }}>
        <CardMedia
          component="img"
          height="220"
          image={`http://localhost:3005/api/chef/get-chef-profile-image/${chef.image}`}
          alt={chef.name}
          sx={{ objectFit: "cover", p: 0.6, borderRadius: 2 }}
        />
        <Box
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
            display: "flex",
            alignItems: "center",
            gap: 0.5,
            backgroundColor: "rgba(255,255,255,0.9)",
            borderRadius: 1,
            px: 1,
            py: 0.5,
          }}
        >
          <Visibility sx={{ fontSize: 14, color: "#ff5722" }} />
          <Typography
            variant="caption"
            sx={{ color: "#ff5722", fontWeight: 500 }}
          >
            {chef.views} views
          </Typography>
        </Box>
      </Box>

      <CardContent sx={{ pb: 2 }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
          <Typography
            variant="body2"
            component="h3"
            sx={{ fontWeight: 500, color: "#9999A6" }}
          >
            {chef.name}
          </Typography>
          {chef.verified && (
            <Verified sx={{ fontSize: 20, color: "#4caf50" }} />
          )}
        </Box>

        <Typography
          variant="body2"
          className="Tet"
          sx={{
            fontWeight: 500,
            fontSize: "15px",
            color: "#000",
            letterSpacing: 0.5,
            mb: 1,
            lineHeight: 1.3,
          }}
        >
          {chef.title}
        </Typography>

        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5, mb: 1.5 }}>
          <Rating
            value={chef.rating}
            precision={0.1}
            size="small"
            readOnly
            sx={{ color: "#ffc107" }}
          />
          <Typography variant="body2" sx={{ color: "#666", ml: 0.5 }}>
            {chef.rating.toFixed(1)} ({chef.reviews} review
            {chef.reviews !== 1 ? "s" : ""})
          </Typography>
        </Box>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
            <Schedule sx={{ fontSize: 16, color: "#666" }} />
            <Typography
              variant="body2"
              sx={{ color: "#666", fontSize: "14px", letterSpacing: 0.5 }}
            >
              Hourly Rate
            </Typography>
          </Box>
          <Typography
            variant="body1"
            sx={{ fontWeight: 500, color: "#000", fontSize: "14px" }}
          >
            Rs {chef.hourlyRate} /hr
          </Typography>
        </Box>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
            <LocationOn sx={{ fontSize: 16, color: "#666" }} />
            <Typography
              variant="body2"
              sx={{ color: "#666", fontSize: "14px", letterSpacing: 0.5 }}
            >
              Location
            </Typography>
          </Box>
          <Typography
            variant="body2"
            sx={{ color: "#000", fontWeight: 500, fontSize: "14px" }}
          >
            {chef.location}
          </Typography>
        </Box>
        <Box
          sx={{
            borderTop: "1px solid #ccc",
            width: "100%",
            my: 2,
          }}
        />
        <Box sx={{ display: "flex", gap: 1, mb: 2, flexWrap: "wrap" }}>
          {chef.skills.slice(0, 3).map((skill, index) => (
            <Chip
              key={index}
              label={skill}
              size="small"
              sx={{
                backgroundColor: "#f5f5f5",
                color: "#666",
                fontWeight: 500,
                fontSize: "0.75rem",
              }}
            />
          ))}
          {chef.skills.length > 3 && (
            <Chip
              label={`+${chef.skills.length - 3} more`}
              size="small"
              sx={{
                backgroundColor: "#f5f5f5",
                color: "#666",
                fontWeight: 500,
                fontSize: "0.75rem",
              }}
            />
          )}
        </Box>

        <Box sx={{ display: "flex", gap: 1 }}>
          <Box
            component="button"
            onClick={handleViewProfile}
            sx={{
              flex: 1,
              backgroundColor: "#ff5722",
              color: "white",
              border: "none",
              borderRadius: 1,
              py: 1.5,
              px: 2,
              fontWeight: 600,
              cursor: "pointer",
              "&:hover": {
                backgroundColor: "#e64a19",
              },
            }}
          >
            View profile
          </Box>
          {/* <IconButton
            onClick={handleFavoriteClick}
            sx={{
              border: "1px solid #ddd",
              borderRadius: 1,
              "&:hover": {
                backgroundColor: "#f5f5f5",
              },
            }}
          >
            {isFavorite ? (
              <Favorite sx={{ color: "#f44336" }} />
            ) : (
              <FavoriteBorder />
            )}
          </IconButton> */}
        </Box>
      </CardContent>
    </Card>
  );
};

export default ChefCard;