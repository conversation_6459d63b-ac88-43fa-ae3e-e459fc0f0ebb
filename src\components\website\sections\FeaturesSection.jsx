// src/components/website/sections/FeaturesSection.jsx
import React from "react";
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  useMediaQuery,
} from "@mui/material";
import {
  Analytics,
  Security,
  Speed,
  TrendingUp,
  Assessment,
  AccountBalance,
  Timeline,
  PieChart,
} from "@mui/icons-material";
import { motion } from "framer-motion";

const FeaturesSection = () => {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("md"));

  const features = [
    {
      icon: <Analytics />,
      title: "Advanced ROI Calculator",
      description: "Calculate returns with precision using our sophisticated algorithms and real-time market data.",
      color: "#2563eb",
    },
    {
      icon: <Timeline />,
      title: "Portfolio Tracking",
      description: "Monitor your investments in real-time with comprehensive portfolio management tools.",
      color: "#7c3aed",
    },
    {
      icon: <Assessment />,
      title: "Risk Analysis",
      description: "Assess investment risks with detailed analytics and predictive modeling capabilities.",
      color: "#059669",
    },
    {
      icon: <PieChart />,
      title: "Visual Reports",
      description: "Generate beautiful, interactive reports and charts to visualize your investment performance.",
      color: "#dc2626",
    },
    {
      icon: <Security />,
      title: "Bank-Level Security",
      description: "Your financial data is protected with enterprise-grade encryption and security protocols.",
      color: "#7c2d12",
    },
    {
      icon: <Speed />,
      title: "Real-Time Updates",
      description: "Get instant notifications and updates on market changes affecting your investments.",
      color: "#0891b2",
    },
  ];

  return (
    <Box sx={{ py: { xs: 8, md: 12 }, backgroundColor: "background.default" }}>
      <Container maxWidth="xl">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <Box sx={{ textAlign: "center", mb: 8 }}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: "2rem", md: "2.5rem", lg: "3rem" },
                fontWeight: 700,
                mb: 2,
                background: "linear-gradient(135deg, #1e293b 0%, #475569 100%)",
                backgroundClip: "text",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              Powerful Features for Smart Investing
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: "text.secondary",
                maxWidth: 600,
                mx: "auto",
                lineHeight: 1.6,
              }}
            >
              Everything you need to make informed investment decisions and maximize your returns
            </Typography>
          </Box>
        </motion.div>

        {/* Features Grid */}
        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} lg={4} key={index}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
              >
                <Card
                  sx={{
                    height: "100%",
                    borderRadius: 4,
                    border: "1px solid",
                    borderColor: "divider",
                    boxShadow: "0 4px 20px rgba(0,0,0,0.08)",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      boxShadow: "0 12px 40px rgba(0,0,0,0.15)",
                      borderColor: feature.color + "40",
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        borderRadius: 3,
                        background: `linear-gradient(135deg, ${feature.color}15 0%, ${feature.color}25 100%)`,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        mb: 3,
                      }}
                    >
                      {React.cloneElement(feature.icon, {
                        sx: { color: feature.color, fontSize: 28 },
                      })}
                    </Box>

                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 2,
                        color: "text.primary",
                      }}
                    >
                      {feature.title}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        color: "text.secondary",
                        lineHeight: 1.6,
                      }}
                    >
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Box sx={{ textAlign: "center", mt: 8 }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                mb: 2,
                color: "text.primary",
              }}
            >
              Ready to start optimizing your investments?
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: "text.secondary",
                mb: 4,
                maxWidth: 500,
                mx: "auto",
              }}
            >
              Join thousands of investors who trust our platform for their ROI calculations and portfolio management.
            </Typography>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default FeaturesSection;