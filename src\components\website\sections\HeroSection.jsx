// src/components/website/sections/HeroSection.jsx
import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  useMediaQuery,
  Card,
  CardContent,
  Stack,
  Chip,
} from "@mui/material";
import {
  PlayArrow,
  TrendingUp,
  Analytics,
  Security,
  Speed,
  ArrowForward,
} from "@mui/icons-material";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";

// Counter animation hook
const useCounter = (end, duration = 2000, delay = 0) => {
  const [count, setCount] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    if (!hasStarted) {
      const timer = setTimeout(() => {
        setHasStarted(true);
      }, delay);
      return () => clearTimeout(timer);
    }

    if (hasStarted) {
      let startTime;
      const animate = (currentTime) => {
        if (!startTime) startTime = currentTime;
        const progress = Math.min((currentTime - startTime) / duration, 1);
        
        // Easing function for smooth animation
        const easeOutCubic = 1 - Math.pow(1 - progress, 3);
        setCount(Math.floor(easeOutCubic * end));

        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };
      requestAnimationFrame(animate);
    }
  }, [end, duration, hasStarted, delay]);

  return count;
};

// Individual stat component with counter
const StatItem = ({ value, label, index }) => {
  // Extract numeric value for animation
  const getNumericValue = (val) => {
    if (val.includes('%')) {
      return parseInt(val.replace('%', ''));
    }
    if (val.includes('K+')) {
      return parseInt(val.replace('K+', ''));
    }
    if (val.includes('B+')) {
      return parseFloat(val.replace('$', '').replace('B+', ''));
    }
    return parseInt(val);
  };

  const formatValue = (num, originalValue) => {
    if (originalValue.includes('%')) {
      return `${num}%`;
    }
    if (originalValue.includes('K+')) {
      return `${num}K+`;
    }
    if (originalValue.includes('B+')) {
      return `$${num.toFixed(1)}B+`;
    }
    return num.toString();
  };

  const targetValue = getNumericValue(value);
  const animatedValue = useCounter(targetValue, 2000, 600 + index * 200);
  const displayValue = formatValue(animatedValue, value);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
    >
      <Box>
        <Typography
          variant="h2"
          sx={{
            fontWeight: 800,
            color: "white",
            mb: 0.5,
            fontSize: { xs: "2.2rem", md: "3rem", lg: "3.5rem" },
            textShadow: "0 2px 10px rgba(0,0,0,0.2)",
            lineHeight: 1,
            fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
          }}
        >
          {displayValue}
        </Typography>
        <Typography
          variant="body1"
          sx={{
            color: "rgba(255, 255, 255, 0.8)",
            fontWeight: 500,
            fontSize: { xs: "1rem", md: "1.1rem" },
          }}
        >
          {label}
        </Typography>
      </Box>
    </motion.div>
  );
};

const HeroSection = () => {
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("md"));

  const heroData = {
    title: "Maximize Your Investment Returns with Smart ROI Analysis",
    subtitle:
      "Professional-grade investment tools to help you make informed financial decisions and optimize your portfolio performance.",
    stats: [
      { value: "98%", label: "Accuracy Rate" },
      { value: "50K+", label: "Active Users" },
      { value: "$2.5B+", label: "Assets Analyzed" },
    ],
  };

  const handleGetStarted = () => {
    navigate("/auth/login");
  };

  const handleWatchDemo = () => {
    console.log("Watch demo clicked");
  };

  return (
    <Box
      sx={{
        background: (theme) =>
          theme.palette.mode === "dark"
            ? "linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%)"
            : "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
        py: { xs: 3, md: 3 },
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%)",
          pointerEvents: "none",
        },
        "&::after": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%)",
          pointerEvents: "none",
        },
      }}
    >
      <Container
        maxWidth="xl"
        sx={{
          position: "relative",
          zIndex: 2,
          px: { xs: 2, sm: 3, md: 6 },
        }}
      >
        <Grid container spacing={{ xs: 4, md: 8 }} alignItems="center">
          {/* Left Content */}
          <Grid item xs={12} lg={6}>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              
            >
              {/* Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}

              >
                <Chip
                  label="🚀 New: AI-Powered Portfolio Analysis"
                  sx={{
                    mb: 3,
                    px: 2,
                    py: 1,
                    background: "rgba(255, 255, 255, 0.15)",
                    color: "white",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(255, 255, 255, 0.2)",
                    fontSize: "0.9rem",
                    fontWeight: 600,
                    "&:hover": {
                      background: "rgba(255, 255, 255, 0.2)",
                    },
                  }}
                />
              </motion.div>

              {/* Main Title */}
              <Typography
                variant="h1"
                sx={{
                  fontSize: {
                    xs: "2.5rem",
                    sm: "3.5rem",
                    md: "4.5rem",
                    lg: "5rem",
                  },
                  fontWeight: 800,
                  lineHeight: { xs: 1.2, md: 1.1 },
                  mb: 3,
                  color: "white",
                  textShadow: "0 4px 20px rgba(0,0,0,0.3)",
                  letterSpacing: "-0.02em",
                  textAlign: { xs: "center", md: "left" },
                }}
              >
                Maximize Your{" "}
                <Box
                  component="span"
                  sx={{
                    background: "linear-gradient(45deg, #FFD700, #FFA500)",
                    backgroundClip: "text",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                  }}
                >
                  Investment Returns
                </Box>{" "}
                with Smart ROI Analysis
              </Typography>

              {/* Subtitle */}
              <Typography
                variant="h6"
                sx={{
                  color: "rgba(255, 255, 255, 0.9)",
                  mb: 4,
                  lineHeight: 1.6,
                  fontSize: { xs: "1.1rem", md: "1.25rem" },
                  maxWidth: { xs: "100%", lg: "90%" },
                  fontWeight: 400,
                  textAlign: { xs: "center", md: "left" },
                }}
              >
                {heroData.subtitle}
              </Typography>

              {/* Action Buttons */}
              <Stack
                direction={{ xs: "column", sm: "row" }}
                spacing={2}
                sx={{ mb: 6 }}
              >
                <Button
                  variant="contained"
                  size="large"
                  endIcon={<ArrowForward />}
                  onClick={handleGetStarted}
                  sx={{
                    background: "linear-gradient(45deg, #ffffff, #f8f9fa)",
                    color: "#1a1a1a",
                    px: 4,
                    py: 1.5,
                    borderRadius: 3,
                    fontWeight: 700,
                    fontSize: "1.1rem",
                    textTransform: "none",
                    boxShadow: "0 8px 32px rgba(255,255,255,0.3)",
                    minWidth: { xs: "auto", sm: 200 },
                    "&:hover": {
                      background: "linear-gradient(45deg, #f8f9fa, #e9ecef)",
                      boxShadow: "0 12px 40px rgba(255,255,255,0.4)",
                      transform: "translateY(-2px)",
                    },
                    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                  }}
                >
                  Get Started Free
                </Button>

                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<PlayArrow />}
                  onClick={handleWatchDemo}
                  sx={{
                    px: 4,
                    py: 1.5,
                    borderRadius: 3,
                    fontWeight: 600,
                    fontSize: "1.1rem",
                    textTransform: "none",
                    borderColor: "rgba(255, 255, 255, 0.8)",
                    color: "white",
                    borderWidth: 2,
                    backdropFilter: "blur(10px)",
                    background: "rgba(255, 255, 255, 0.1)",
                    minWidth: { xs: "auto", sm: 180 },
                    "&:hover": {
                      backgroundColor: "rgba(255, 255, 255, 0.2)",
                      borderColor: "white",
                      transform: "translateY(-2px)",
                    },
                    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                  }}
                >
                  Watch Demo
                </Button>
              </Stack>

              {/* Stats with Animation */}
              <Stack
                direction={{ xs: "row", sm: "row" }}
                justifyContent={{ xs: "center", sm: "flex-start" }}
                spacing={{ xs: 3, sm: 6, md: 8 }}
                sx={{ mt: 2 }}
              >
                {heroData.stats.map((stat, index) => (
                  <StatItem
                    key={index}
                    value={stat.value}
                    label={stat.label}
                    index={index}
                  />
                ))}
              </Stack>
            </motion.div>
          </Grid>

          
        </Grid>
      </Container>
    </Box>
  );
};

export default HeroSection;