// src/components/website/sections/StatsSection.jsx
import React from "react";
import {
  Box,
  Container,
  Typography,
  Grid,
  useMediaQuery,
} from "@mui/material";
import {
  TrendingUp,
  People,
  AttachMoney,
  Speed,
} from "@mui/icons-material";
import { motion } from "framer-motion";

const StatsSection = () => {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("md"));

  // This data will come from API - placeholders for now
  const stats = [
    {
      icon: <TrendingUp />,
      value: "98.5%",
      label: "Average ROI Accuracy",
      description: "Precision in investment calculations",
      color: "#2563eb",
    },
    {
      icon: <People />,
      value: "50,000+",
      label: "Active Investors",
      description: "Trust our platform worldwide",
      color: "#7c3aed",
    },
    {
      icon: <AttachMoney />,
      value: "$2.5B+",
      label: "Assets Under Analysis",
      description: "Total portfolio value managed",
      color: "#059669",
    },
    {
      icon: <Speed />,
      value: "0.3s",
      label: "Average Response Time",
      description: "Lightning-fast calculations",
      color: "#dc2626",
    },
  ];

  return (
    <Box
      sx={{
        py: { xs: 8, md: 12 },
        background: "linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)",
        position: "relative",
        overflow: "hidden",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: "url('data:image/svg+xml,<svg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"none\" fill-rule=\"evenodd\"><g fill=\"%23ffffff\" fill-opacity=\"0.05\" fill-rule=\"nonzero\"><circle cx=\"30\" cy=\"30\" r=\"1.5\"/></g></g></svg>')",
          opacity: 0.3,
        },
      }}
    >
      <Container maxWidth="xl" sx={{ position: "relative", zIndex: 1 }}>
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <Box sx={{ textAlign: "center", mb: 8 }}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: "2rem", md: "2.5rem", lg: "3rem" },
                fontWeight: 700,
                mb: 2,
                color: "white",
              }}
            >
              Trusted by Investors Worldwide
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: "rgba(255, 255, 255, 0.8)",
                maxWidth: 600,
                mx: "auto",
                lineHeight: 1.6,
              }}
            >
              Our platform delivers exceptional results and performance metrics that speak for themselves
            </Typography>
          </Box>
        </motion.div>

        {/* Stats Grid */}
        <Grid container spacing={4}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Box
                  sx={{
                    textAlign: "center",
                    p: 4,
                    borderRadius: 4,
                    background: "rgba(255, 255, 255, 0.1)",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(255, 255, 255, 0.2)",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      background: "rgba(255, 255, 255, 0.15)",
                      transform: "translateY(-8px)",
                    },
                  }}
                >
                  {/* Icon */}
                  <Box
                    sx={{
                      width: 80,
                      height: 80,
                      borderRadius: "50%",
                      background: "rgba(255, 255, 255, 0.2)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mx: "auto",
                      mb: 3,
                    }}
                  >
                    {React.cloneElement(stat.icon, {
                      sx: { color: "white", fontSize: 36 },
                    })}
                  </Box>

                  {/* Value */}
                  <Typography
                    variant="h3"
                    sx={{
                      fontWeight: 800,
                      color: "white",
                      mb: 1,
                      fontSize: { xs: "2rem", md: "2.5rem" },
                    }}
                  >
                    {stat.value}
                  </Typography>

                  {/* Label */}
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: "white",
                      mb: 1,
                      fontSize: { xs: "1rem", md: "1.1rem" },
                    }}
                  >
                    {stat.label}
                  </Typography>

                  {/* Description */}
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(255, 255, 255, 0.7)",
                      lineHeight: 1.5,
                    }}
                  >
                    {stat.description}
                  </Typography>
                </Box>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Box sx={{ textAlign: "center", mt: 8 }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: "white",
                mb: 2,
              }}
            >
              Join the growing community of successful investors
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: "rgba(255, 255, 255, 0.8)",
                maxWidth: 500,
                mx: "auto",
                lineHeight: 1.6,
              }}
            >
              Experience the difference that professional-grade investment tools can make in your portfolio performance
            </Typography>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default StatsSection;