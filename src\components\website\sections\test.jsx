          <Grid  item xs={12} lg={6} >
            <motion.div className="hero-visual"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <Box
                sx={{
                  position: "relative",
                  height: { xs: 350, sm: 400, md: 450, lg: 500 },
                  mt: { xs: 4, lg: 0 },
                  mx: "auto",
                  maxWidth: { xs: "100%", sm: "400px", md: "100%" },
                }}
              >
                {/* Main Dashboard Card */}
                <motion.div
                  animate={{ y: [0, -10, 0] }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                >
                  <Card
                    sx={{
                      position: "absolute",
                      top: { xs: 10, sm: 20, md: 40 },
                      right: { xs: 10, sm: 20, md: 20 },
                      left: { xs: 10, sm: "auto" },
                      width: { xs: "calc(100% - 20px)", sm: "85%", md: "85%" },
                      height: { xs: "65%", sm: "70%", md: "70%" },
                      borderRadius: 4,
                      boxShadow: "0 25px 80px rgba(0,0,0,0.15)",
                      background:
                        "linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",
                      border: "1px solid rgba(255, 255, 255, 0.2)",
                      backdropFilter: "blur(20px)",
                      overflow: "hidden",
                    }}
                  >
                    <CardContent sx={{ p: { xs: 2, md: 3 }, height: "100%" }}>
                      {/* Header */}
                      <Stack
                        direction="row"
                        alignItems="center"
                        spacing={2}
                        sx={{ mb: 3 }}
                      >
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: 2,
                            background:
                              "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Analytics sx={{ color: "white", fontSize: 24 }} />
                        </Box>
                        <Box>
                          <Typography
                            variant="h6"
                            sx={{ fontWeight: 700, mb: 0.5 }}
                          >
                            ROI Dashboard
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Real-time analytics
                          </Typography>
                        </Box>
                      </Stack>

                      {/* Chart Area */}
                      <Box
                        sx={{
                          height: { xs: 120, md: 160 },
                          background:
                            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
                          borderRadius: 3,
                          mb: 3,
                          position: "relative",
                          overflow: "hidden",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <TrendingUp
                          sx={{
                            fontSize: 48,
                            color: "primary.main",
                            opacity: 0.6,
                          }}
                        />
                      </Box>

                      {/* Stats Row */}
                      <Stack direction="row" justifyContent="space-between">
                        <Box sx={{ textAlign: "center" }}>
                          <Typography
                            variant="h5"
                            sx={{
                              fontWeight: 800,
                              background:
                                "linear-gradient(45deg, #667eea, #764ba2)",
                              backgroundClip: "text",
                              WebkitBackgroundClip: "text",
                              WebkitTextFillColor: "transparent",
                            }}
                          >
                            +24.5%
                          </Typography>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ fontWeight: 500 }}
                          >
                            Monthly ROI
                          </Typography>
                        </Box>
                        <Box sx={{ textAlign: "center" }}>
                          <Typography
                            variant="h5"
                            sx={{
                              fontWeight: 800,
                              background:
                                "linear-gradient(45deg, #48bb78, #38a169)",
                              backgroundClip: "text",
                              WebkitBackgroundClip: "text",
                              WebkitTextFillColor: "transparent",
                            }}
                          >
                            $125K
                          </Typography>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ fontWeight: 500 }}
                          >
                            Total Returns
                          </Typography>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Floating Cards */}
                <motion.div
                  animate={{ y: [0, -15, 0] }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5,
                  }}
                >
                  <Card
                    sx={{
                      position: "absolute",
                      top: { xs: 60, sm: 80, md: 120 },
                      left: { xs: -5, sm: 0, md: 0 },
                      width: { xs: 140, sm: 160, md: 180 },
                      borderRadius: 3,
                      boxShadow: "0 20px 60px rgba(0,0,0,0.15)",
                      background: "rgba(255, 255, 255, 0.95)",
                      backdropFilter: "blur(20px)",
                      border: "1px solid rgba(255, 255, 255, 0.3)",
                      zIndex: 3,
                    }}
                  >
                    <CardContent
                      sx={{
                        p: { xs: 1.5, sm: 2, md: 2.5 },
                        textAlign: "center",
                      }}
                    >
                      <Security
                        sx={{
                          fontSize: 36,
                          mb: 1.5,
                          background:
                            "linear-gradient(45deg, #667eea, #764ba2)",
                          backgroundClip: "text",
                          WebkitBackgroundClip: "text",
                          WebkitTextFillColor: "transparent",
                        }}
                      />
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 700,
                          color: "text.primary",
                          fontSize: { xs: "0.75rem", sm: "0.875rem" },
                        }}
                      >
                        Bank-Level Security
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div
                  animate={{ y: [0, 15, 0] }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1.5,
                  }}
                >
                  <Card
                    sx={{
                      position: "absolute",
                      bottom: { xs: 20, sm: 30, md: 60 },
                      left: { xs: 20, sm: 40, md: 60 },
                      width: { xs: 140, sm: 160, md: 180 },
                      borderRadius: 3,
                      boxShadow: "0 20px 60px rgba(0,0,0,0.15)",
                      background: "rgba(255, 255, 255, 0.95)",
                      backdropFilter: "blur(20px)",
                      border: "1px solid rgba(255, 255, 255, 0.3)",
                      zIndex: 3,
                    }}
                  >
                    <CardContent
                      sx={{
                        p: { xs: 1.5, sm: 2, md: 2.5 },
                        textAlign: "center",
                      }}
                    >
                      <Speed
                        sx={{
                          fontSize: { xs: 28, sm: 32, md: 36 },
                          mb: 1.5,
                          background:
                            "linear-gradient(45deg, #ed64a6, #f093fb)",
                          backgroundClip: "text",
                          WebkitBackgroundClip: "text",
                          WebkitTextFillColor: "transparent",
                        }}
                      />
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 700,
                          color: "text.primary",
                          fontSize: { xs: "0.75rem", sm: "0.875rem" },
                        }}
                      >
                        Real-Time Analysis
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Box>
            </motion.div>
          </Grid>


{/* Right Content - Visual Elements */}
          <Grid item xs={12} lg={6}>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <Box
                sx={{
                  position: "relative",
                  height: 500,
                  mt: { xs: 4, lg: 0 },
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {/* Main Dashboard Card - Centered */}
                <motion.div
                  animate={{ y: [0, -10, 0] }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                >
                  <Card
                    sx={{
                      width: { xs: 320, sm: 380, md: 420 },
                      height: { xs: 300, sm: 340, md: 380 },
                      borderRadius: 4,
                      boxShadow: "0 25px 80px rgba(0,0,0,0.15)",
                      background:
                        "linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",
                      border: "1px solid rgba(255, 255, 255, 0.2)",
                      backdropFilter: "blur(20px)",
                      overflow: "hidden",
                      zIndex: 2,
                    }}
                  >
                    <CardContent sx={{ p: 3, height: "100%" }}>
                      <Stack
                        direction="row"
                        alignItems="center"
                        spacing={2}
                        sx={{ mb: 3 }}
                      >
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: 2,
                            background:
                              "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Analytics sx={{ color: "white", fontSize: 24 }} />
                        </Box>
                        <Box>
                          <Typography
                            variant="h6"
                            sx={{ fontWeight: 700, mb: 0.5 }}
                          >
                            ROI Dashboard
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Real-time analytics
                          </Typography>
                        </Box>
                      </Stack>

                      <Box
                        sx={{
                          height: 140,
                          background:
                            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
                          borderRadius: 3,
                          mb: 3,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <TrendingUp
                          sx={{
                            fontSize: 48,
                            color: "primary.main",
                            opacity: 0.6,
                          }}
                        />
                      </Box>

                      <Stack direction="row" justifyContent="space-between">
                        <Box sx={{ textAlign: "center" }}>
                          <Typography
                            variant="h5"
                            sx={{
                              fontWeight: 800,
                              background:
                                "linear-gradient(45deg, #667eea, #764ba2)",
                              backgroundClip: "text",
                              WebkitBackgroundClip: "text",
                              WebkitTextFillColor: "transparent",
                            }}
                          >
                            +24.5%
                          </Typography>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ fontWeight: 500 }}
                          >
                            Monthly ROI
                          </Typography>
                        </Box>
                        <Box sx={{ textAlign: "center" }}>
                          <Typography
                            variant="h5"
                            sx={{
                              fontWeight: 800,
                              background:
                                "linear-gradient(45deg, #48bb78, #38a169)",
                              backgroundClip: "text",
                              WebkitBackgroundClip: "text",
                              WebkitTextFillColor: "transparent",
                            }}
                          >
                            $125K
                          </Typography>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ fontWeight: 500 }}
                          >
                            Total Returns
                          </Typography>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Security Card - Top Left */}
                <motion.div
                  animate={{ y: [0, -15, 0] }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5,
                  }}
                  style={{
                    position: "absolute",
                    top: 20,
                    left: -20,
                    zIndex: 3,
                  }}
                >
                  <Card
                    sx={{
                      width: 140,
                      borderRadius: 3,
                      boxShadow: "0 20px 60px rgba(0,0,0,0.15)",
                      background: "rgba(255, 255, 255, 0.95)",
                      backdropFilter: "blur(20px)",
                      border: "1px solid rgba(255, 255, 255, 0.3)",
                    }}
                  >
                    <CardContent sx={{ p: 2, textAlign: "center" }}>
                      <Security
                        sx={{
                          fontSize: 32,
                          mb: 1.5,
                          background:
                            "linear-gradient(45deg, #667eea, #764ba2)",
                          backgroundClip: "text",
                          WebkitBackgroundClip: "text",
                          WebkitTextFillColor: "transparent",
                        }}
                      />
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 700,
                          color: "text.primary",
                          fontSize: "0.875rem",
                        }}
                      >
                        Bank-Level Security
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Speed Card - Bottom Right */}
                <motion.div
                  animate={{ y: [0, 15, 0] }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1.5,
                  }}
                  style={{
                    position: "absolute",
                    bottom: 20,
                    right: -20,
                    zIndex: 3,
                  }}
                >
                  <Card
                    sx={{
                      width: 140,
                      borderRadius: 3,
                      boxShadow: "0 20px 60px rgba(0,0,0,0.15)",
                      background: "rgba(255, 255, 255, 0.95)",
                      backdropFilter: "blur(20px)",
                      border: "1px solid rgba(255, 255, 255, 0.3)",
                    }}
                  >
                    <CardContent sx={{ p: 2, textAlign: "center" }}>
                      <Speed
                        sx={{
                          fontSize: 32,
                          mb: 1.5,
                          background:
                            "linear-gradient(45deg, #ed64a6, #f093fb)",
                          backgroundClip: "text",
                          WebkitBackgroundClip: "text",
                          WebkitTextFillColor: "transparent",
                        }}
                      />
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 700,
                          color: "text.primary",
                          fontSize: "0.875rem",
                        }}
                      >
                        Real-Time Analysis
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Box>
            </motion.div>
          </Grid>