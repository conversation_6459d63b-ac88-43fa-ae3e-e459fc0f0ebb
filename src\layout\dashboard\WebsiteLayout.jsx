// src/layouts/WebsiteLayout.jsx
import React, { useState, useMemo } from "react";
import { Box, CssBaseline, useMediaQuery } from "@mui/material";
import { ThemeProvider } from "@mui/material/styles";
import { Outlet } from "react-router-dom";
// import { getTheme } from "../../theme/theme";
import Header from "../../components/website/Header";
import Footer from "../../components/website/Footer";

const WebsiteLayout = () => {
  const [themeMode, setThemeMode] = useState("light");
  const [colorScheme, setColorScheme] = useState("blue");
  
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("md"));

  // // Create theme based on current settings
  // const theme = useMemo(
  //   () => getTheme(themeMode, colorScheme),
  //   [themeMode, colorScheme]
  // );

  // Toggle theme mode
  const handleThemeToggle = () => {
    setThemeMode((prevMode) => (prevMode === "light" ? "dark" : "light"));
  };

  return (
    // <ThemeProvider theme={theme}>
      // <CssBaseline />
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
          overflow: "hidden",
          backgroundColor: "#F7F7F7"
        }}
      >
        {/* Header */}
        <Header 
          onThemeToggle={handleThemeToggle}
          currentTheme={themeMode}
        />

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Outlet />
        </Box>

        {/* Footer */}
        <Footer />
      </Box>
    // </ThemeProvider>
  );
};

export default WebsiteLayout;