import { Box, Grid } from "@mui/material";
import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import PropTypes from "prop-types";
import SideBar from "./sideBar";
import TopBar from "./topBar";
import Main from "./main";
import { NAV } from "./config-layout";
import {
  logOut,
  selectCurrentUser,
  selectUserType,
} from "../../reducers/auth.reducer";
import Sidebar from "./sideBar";
import Topbar from "./topBar";
import MainContent from "./main";

const EXPANDED_WIDTH = "16%";
const COLLAPSED_WIDTH = "60px";

const pageIdentifier = {
  "/dashboard": "dashboard",
  "/students": "allStudents",
  "/teachers": "allTeachers",
  "/staff": "allStaff",
  "/institutes": "allInstitutes",
  "/user-registration": "userRegistration",
  "/student-registration": "studentRegistration",
  "/teacher-registration": "teacherRegistration",
  "/attendance": "attendance",
  "/classes": "classes",
  "/payment": "payment",
  "/settings": "settings",
  "/student-profile/": "studentProfile",
  "/teacher-profile/": "teacherProfile",
  "/user-profile": "userProfile",
  "/institute-registration": "instituteRegistration",
  "/class-student-attendance": "classStudentAttendance",
  "/admin-payments": "adminPayments",
  "/admin-settings": "adminSettings",
  "/institute-profile": "instituteProfile",
  "/student-payments": "studentPayments",
  "/student-settings": "studentSettings",
  "/my-courses": "myCourses",
  "/course-details": "studentCourseDetails",
  "/crm-users": "crmUser",
  "/user-settings": "userSettings",
  "/call-to-student": "callToStudent",
  "/inquiry-reports": "inquiryReports",
  "/crm-student-profile": "crmStudentProfile",
  "/assign-to-course": "assignToCourse",
};

export default function DashboardLayout() {
  const navigate = useNavigate();
  // const dispatch = useDispatch();
  // const user = useSelector(selectCurrentUser);
  const location = useLocation();
  const currentPath = location.pathname.split("/").pop();
  const currentPage = pageIdentifier[`/${currentPath}`] || "dashboard";
  // const userType = useSelector(selectUserType);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 600);

   // Get userType from URL query params
  const searchParams = new URLSearchParams(location.search);
  const userType = searchParams.get('userType') || 'Admin';

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 600);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogout = () => {
    dispatch(logOut());
    navigate("/login");
  };

  const handleSidebarCollapse = (collapsed) => {
    setIsCollapsed(collapsed);
  };

  return (
    // <Box
    //   className=""
    //   sx={{
    //     flexGrow: 1,
    //     width: `calc(100% - ${NAV.WIDTH - 250}px)`,
    //     transition: 'all 0.3s ease',
    //   }}
    // >
    //   <Grid container spacing={0}>
    //     <Grid
    //       item
    //       xs={12}
    //       sx={{
    //         display: {
    //           md: "block",
    //           sm: "block",
    //           lg: "block",
    //           xs: "block",
    //         },
    //         width: "100%",
    //       }}
    //     >
    //       <TopBar currentPage={currentPage} />
    //     </Grid>
    //     <Grid
    //       item
    //       xs={12}
    //       sx={{
    //         position: "relative",
    //       }}
    //     >
    //       <Grid container>
    //         <Grid
    //           item
    //           xs={12}
    //           sm={isCollapsed ? 1 : 2}
    //           sx={{
    //             display: {
    //               xs: "none",
    //               sm: userType === "Branch User" ? "none" : "block",
    //             },
    //             transition: 'all 0.3s ease',
    //             width: isCollapsed ? COLLAPSED_WIDTH : EXPANDED_WIDTH,
    //           }}
    //         >
    //           <Box
    //             sx={{
    //               px: 1,
    //             }}
    //           >
    //             <SideBar 
    //               onLogout={handleLogout} 
    //               onCollapse={handleSidebarCollapse}
    //               isCollapsed={isCollapsed}
    //             />
    //           </Box>
    //         </Grid>
    //         <Grid
    //           item
    //           xs={12}
    //           sm={userType === "Branch User" ? 12 : (isCollapsed ? 11 : 10)}
    //           sx={{
    //             pl: { sm: 1 },
    //             pt: 0.5,
    //             transition: 'all 0.3s ease',
    //           }}
    //         >
    //           <Box>
    //             <Main />
    //           </Box>
    //         </Grid>
    //       </Grid>
    //     </Grid>
    //   </Grid>
    // </Box>
    <Box sx={{ display: "flex", flexDirection: "column", height: "100vh", width: "100vw" }}>
      <Box sx={{ display: "flex", flexGrow: 1, overflow: "hidden" }}>
        <Sidebar userType={userType} />
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            flexGrow: 1,
            backgroundColor: "#F4F4F4",
          }}
        >
          <Topbar />
          <Box >
          <MainContent />
          </Box>
          
        </Box>
      </Box>
    </Box>
  );
}

DashboardLayout.propTypes = {
  onLogout: PropTypes.func,
  user: PropTypes.object,
};