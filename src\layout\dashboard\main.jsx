import { Box } from "@mui/material";
import React, { Suspense, lazy } from "react";
import { ThreeDot } from "react-loading-indicators";
import { Outlet } from "react-router-dom";

const LoadingFallback = () => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    height="100%"
    width="100%"
    position="absolute"
    top="0"
    left="0"
    backgroundColor="rgba(255, 255, 255, 0.7)"
  >
    <ThreeDot
      variant="pulsate"
      color="#000"
      size="medium"
      text=""
      textColor=""
    />
  </Box>
);
export default function MainContent() {
  return (
    <Box
      sx={{
        height: "100vh",
        padding: "20px 20px",
        overflow: "auto", // Add overflow auto to enable scrolling
        display: "flex",
        flexDirection: "column",
        gap: "10px",
      }}
    >
        <Suspense fallback={<LoadingFallback />}>
          <Box sx={{ pb:10 }}>
            <Outlet/>
          </Box>
        </Suspense>
    </Box>
  );
}
