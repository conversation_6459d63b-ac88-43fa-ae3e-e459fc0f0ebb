import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Collapse,
} from "@mui/material";
import DashboardIcon from "@mui/icons-material/Dashboard";
import PeopleIcon from "@mui/icons-material/People";
import PaymentIcon from "@mui/icons-material/Payment";
import SettingsIcon from "@mui/icons-material/Settings";
import LogoutIcon from "@mui/icons-material/Logout";
import PersonIcon from "@mui/icons-material/Person";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import EventAvailableIcon from "@mui/icons-material/EventAvailable";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import ArrowDropUpIcon from "@mui/icons-material/ArrowDropUp";
import MessageIcon from "@mui/icons-material/Message";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import MenuOpenIcon from "@mui/icons-material/MenuOpen";
import ArticleIcon from "@mui/icons-material/Article";
import MenuIcon from "@mui/icons-material/Menu";
import DescriptionIcon from "@mui/icons-material/Description";
import LocalDiningIcon from "@mui/icons-material/LocalDining";
import VerifiedIcon from "@mui/icons-material/Verified";
import NewReleasesIcon from "@mui/icons-material/NewReleases";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import SubscriptionsIcon from "@mui/icons-material/Subscriptions";
import BookmarkAddedIcon from "@mui/icons-material/BookmarkAdded";
import ReceiptIcon from "@mui/icons-material/Receipt";
import RestaurantMenuIcon from "@mui/icons-material/RestaurantMenu";
import EventIcon from "@mui/icons-material/Event";
import ReviewsIcon from "@mui/icons-material/Reviews";
import { useDispatch, useSelector } from "react-redux";
import { logOut, selectUserType } from "../../reducers/auth.reducer";

const Sidebar = () => {
  const navigate = useNavigate();
  const userType = useSelector(selectUserType);
  const location = useLocation();
  const dispatch = useDispatch();
  const [open, setOpen] = useState(true);

  // Track open state for each menu item
  const [openMenus, setOpenMenus] = useState({
    dashboard: false,
    members: true,
    attendance: false,
    payment: false,
    settings: false,
  });

  const toggleMenu = (menuName) => {
    setOpenMenus((prev) => ({
      ...prev,
      [menuName]: !prev[menuName],
    }));
  };

  // Check if a route is active
  const isActive = (path) => {
    if (!path) return false;
    return location.pathname === path;
  };

  // Handle navigation
  const handleNavigate = (path) => {
    if (path) {
      // Preserve userType in URL when navigating
      const searchParams = new URLSearchParams(location.search);
      const currentUserType = searchParams.get("userType");
      if (currentUserType) {
        navigate(`${path}?userType=${currentUserType}`);
      } else {
        navigate(path);
      }
    }
  };

  const handleLogout = () => {
    dispatch(logOut()); // Dispatch the logOut action to clear the auth state
    navigate("/auth/login"); // Redirect the user to the login page
  };

  // Main menu items with their respective submenus and navigation paths
  // const sidebarItems = [
  //   {
  //     text: "Dashboard",
  //     icon: <DashboardIcon />,
  //     menuName: "dashboard",
  //     hasSubmenu: false,
  //     path: "/system/dashboard",
  //   },
  //   {
  //     text: "Customers",
  //     icon: <PeopleIcon />,
  //     menuName: "customers",
  //     hasSubmenu: false,
  //     path: "/system/customers",
  //   },
  //   {
  //     text: "Chefs",
  //     icon: <LocalDiningIcon />,
  //     menuName: "chefs",
  //     hasSubmenu: true,
  //     subMenuItems: [
  //       {
  //         text: "All Chefs",
  //         icon: <PeopleIcon fontSize="small" />,
  //         path: "/system/chefs/all",
  //       },
  //       {
  //         text: "Verified Chefs",
  //         icon: <VerifiedIcon fontSize="small" />,
  //         path: "/system/chefs/verified",
  //       },
  //       {
  //         text: "Unverified Chefs",
  //         icon: <NewReleasesIcon fontSize="small" />,
  //         path: "/system/chefs/unverified",
  //       },
  //     ],
  //   },
  //   {
  //     text: "Payments",
  //     icon: <MonetizationOnIcon />,
  //     menuName: "payment",
  //     hasSubmenu: true,
  //     subMenuItems: [
  //       {
  //         text: "Subscription",
  //         icon: <SubscriptionsIcon fontSize="small" />,
  //         path: "/system/payments/subscription-payment",
  //       },
  //       {
  //         text: "Booking Payments",
  //         icon: <BookmarkAddedIcon fontSize="small" />,
  //         path: "/system/payments/booking-payment",
  //       },
  //     ]
  //   },
  //   {
  //     text: "Withdrawals",
  //     icon: <PaymentIcon />,
  //     menuName: "withdrawals",
  //     hasSubmenu: true,
  //     subMenuItems: [
  //       {
  //         text: "Withdrawal Requests",
  //         icon: <PersonIcon fontSize="small" />,
  //         path: "/system/withdrawals/requests",
  //       },
  //       {
  //         text: "Admin Withdrawals",
  //         icon: <PersonIcon fontSize="small" />,
  //         path: "/system/withdrawals/admin",
  //       },
  //       // {
  //       //   text: "Chef Widhdrawals",
  //       //   icon: <PersonIcon fontSize="small" />,
  //       //   path: "/system/withdrawals/chef",
  //       // },
  //     ]
  //   },
  //   {
  //     text: "Bookings",
  //     icon: <BookmarkAddedIcon />,
  //     menuName: "bookings",
  //     hasSubmenu: false,
  //     path: "/system/bookings",
  //   },
  //   {
  //     text: "Users",
  //     icon: <PeopleIcon />,
  //     menuName: "users",
  //     hasSubmenu: true,
  //     subMenuItems: [
  //       {
  //         text: "New User",
  //         icon: <PersonIcon fontSize="small" />,
  //         path: "/system/users/new-user",
  //       },
  //       {
  //         text: "All Users",
  //         icon: <PeopleIcon fontSize="small" />,
  //         path: "/system/users/all-users",
  //       },
  //     ],
  //   },
  //   {
  //     text: "Memberships",
  //     icon: <ArticleIcon />,
  //     menuName: "plans",
  //     hasSubmenu: true,
  //     subMenuItems: [
  //       {
  //         text: "New Membership",
  //         icon: <PersonIcon fontSize="small" />,
  //         path: "/system/plans/new-membership-plan",
  //       },
  //       {
  //         text: "All Memberships",
  //         icon: <DescriptionIcon fontSize="small" />,
  //         path: "/system/plans/all-membership-plan",
  //       },
  //     ],
  //   },
  //   {
  //     text: "Settings",
  //     icon: <SettingsIcon />,
  //     menuName: "settings",
  //     hasSubmenu: true,
  //     subMenuItems: [
  //       // {
  //       //   text: "General Settings",
  //       //   icon: <AdminPanelSettingsIcon fontSize="small" />,
  //       //   path: "/settings/general-settings",
  //       // },
  //       {
  //         text: "My Profile",
  //         icon: <AccountCircleIcon fontSize="small" />,
  //         path: "/system/settings/my-profile",
  //       },
  //       // {
  //       //   text: "Security Settings",
  //       //   icon: <SecurityIcon fontSize="small" />,
  //       //   path: "/settings/security-settings",
  //       // },
  //       // {
  //       //   text: "SMS",
  //       //   icon: <MessageIcon fontSize="small" />,
  //       //   path: "/settings/sms",
  //       // },
  //     ],
  //   },
  //   {
  //     text: "Logout",
  //     icon: <LogoutIcon />,
  //     menuName: "logout",
  //     hasSubmenu: false,
  //     onClick: handleLogout,
  //   },
  // ];

  // Admin menu items
  const adminMenuItems = [
    {
      text: "Dashboard",
      icon: <DashboardIcon />,
      menuName: "dashboard",
      hasSubmenu: false,
      path: "/system/dashboard",
    },
    {
      text: "Customers",
      icon: <PeopleIcon />,
      menuName: "customers",
      hasSubmenu: false,
      path: "/system/customers",
    },
    {
      text: "Chefs",
      icon: <LocalDiningIcon />,
      menuName: "chefs",
      hasSubmenu: true,
      subMenuItems: [
        {
          text: "All Chefs",
          icon: <PeopleIcon fontSize="small" />,
          path: "/system/chefs/all",
        },
        {
          text: "Verified Chefs",
          icon: <VerifiedIcon fontSize="small" />,
          path: "/system/chefs/verified",
        },
        {
          text: "Unverified Chefs",
          icon: <NewReleasesIcon fontSize="small" />,
          path: "/system/chefs/unverified",
        },
      ],
    },
    {
      text: "Payments",
      icon: <MonetizationOnIcon />,
      menuName: "payment",
      hasSubmenu: true,
      subMenuItems: [
        {
          text: "Subscription",
          icon: <SubscriptionsIcon fontSize="small" />,
          path: "/system/payments/subscription-payment",
        },
        {
          text: "Booking Payments",
          icon: <BookmarkAddedIcon fontSize="small" />,
          path: "/system/payments/booking-payment",
        },
      ],
    },
    {
      text: "Withdrawals",
      icon: <PaymentIcon />,
      menuName: "withdrawals",
      hasSubmenu: true,
      subMenuItems: [
        {
          text: "Withdrawal Requests",
          icon: <PersonIcon fontSize="small" />,
          path: "/system/withdrawals/requests",
        },
        {
          text: "Admin Withdrawals",
          icon: <PersonIcon fontSize="small" />,
          path: "/system/withdrawals/admin",
        },
      ],
    },
    {
      text: "Bookings",
      icon: <BookmarkAddedIcon />,
      menuName: "bookings",
      hasSubmenu: false,
      path: "/system/bookings",
    },
    {
      text: "Users",
      icon: <PeopleIcon />,
      menuName: "users",
      hasSubmenu: true,
      subMenuItems: [
        {
          text: "New User",
          icon: <PersonIcon fontSize="small" />,
          path: "/system/users/new-user",
        },
        {
          text: "All Users",
          icon: <PeopleIcon fontSize="small" />,
          path: "/system/users/all-users",
        },
      ],
    },
    {
      text: "Memberships",
      icon: <ArticleIcon />,
      menuName: "plans",
      hasSubmenu: true,
      subMenuItems: [
        {
          text: "New Membership",
          icon: <PersonIcon fontSize="small" />,
          path: "/system/plans/new-membership-plan",
        },
        {
          text: "All Memberships",
          icon: <DescriptionIcon fontSize="small" />,
          path: "/system/plans/all-membership-plan",
        },
      ],
    },
    {
      text: "Settings",
      icon: <SettingsIcon />,
      menuName: "settings",
      hasSubmenu: true,
      subMenuItems: [
        {
          text: "My Profile",
          icon: <AccountCircleIcon fontSize="small" />,
          path: "/system/settings/my-profile",
        },
      ],
    },
    {
      text: "Logout",
      icon: <LogoutIcon />,
      menuName: "logout",
      hasSubmenu: false,
      onClick: handleLogout,
    },
  ];

  // Chef menu items
  const chefMenuItems = [
    {
      text: "Dashboard",
      icon: <DashboardIcon />,
      menuName: "dashboard",
      hasSubmenu: false,
      path: "/system/dashboard",
    },
    {
      text: "My Profile",
      icon: <RestaurantMenuIcon />,
      menuName: "profile",
      hasSubmenu: false,
      path: "/system/my-profile",
    },
    {
      text: "Bookings",
      icon: <EventIcon />,
      menuName: "bookings",
      hasSubmenu: true,
      subMenuItems: [
        {
          text: "All Bookings",
          icon: <EventAvailableIcon fontSize="small" />,
          path: "/system/bookings/all",
        },
        {
          text: "Pending Bookings",
          icon: <EventIcon fontSize="small" />,
          path: "/system/bookings/pending",
        },
      ],
    },
    {
      text: "Reviews",
      icon: <ReviewsIcon />,
      menuName: "reviews",
      hasSubmenu: false,
      path: "/system/reviews",
    },
    {
      text: "Payments",
      icon: <MonetizationOnIcon />,
      menuName: "payments",
      hasSubmenu: false,
      path: "/system/all-payments",
    },
    {
      text: "Withdrawals",
      icon: <EventIcon />,
      menuName: "withdrawals",
      hasSubmenu: true,
      subMenuItems: [
        {
          text: "All Withdrawals",
          icon: <EventAvailableIcon fontSize="small" />,
          path: "/system/withdrawals/all",
        },
        {
          text: "Withdrawal Requests",
          icon: <EventIcon fontSize="small" />,
          path: "/system/withdrawals/requests-all",
        },
      ],
    },
    // {
    //   text: "Settings",
    //   icon: <SettingsIcon />,
    //   menuName: "settings",
    //   hasSubmenu: false,
    // },
    {
      text: "Logout",
      icon: <LogoutIcon />,
      menuName: "logout",
      hasSubmenu: false,
      onClick: handleLogout,
    },
  ];
  const getSidebarItems = () => {
    switch (userType) {
      case "Admin":
        return adminMenuItems;
      case "Chef":
        return chefMenuItems;
      default:
        return adminMenuItems;
    }
  };

  const sidebarItems = getSidebarItems();

  return (
    <Box
      sx={{
        position: { xs: "fixed", md: "relative" },
        width: open ? { xs: "100vw", md: "240px" } : { xs: "0", md: "60px" },
        transition: "width 0.3s ease",
        flexShrink: 0,
        height: "100vh",
        backgroundColor: "#FFFFFF",
        borderRight: "1px solid #EEEEEE",
        boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.05)",
        zIndex: 1200,
        display: "flex",
        flexDirection: "column",
        // overflow: "hidden",
      }}
    >
      {/* Header with title and expand button */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "19px 16px",
        }}
      >
        {open && (
          <Typography
            variant="h6"
            sx={{
              fontWeight: "bold",
              color: "#2D2D2D",
              fontFamily: "'Poppins', sans-serif",
            }}
          >
            Hire a Chef
          </Typography>
        )}

        <IconButton
          onClick={() => setOpen(!open)}
          sx={{
            padding: 0.5,
            borderRadius: 1,
            backgroundColor: "#F6F6F6",
          }}
        >
          {open ? (
            <MenuOpenIcon sx={{ fontSize: 18, color: "#333333" }} />
          ) : (
            <MenuIcon sx={{ fontSize: 18, color: "#333333" }} />
          )}
        </IconButton>
      </Box>

      {/* Navigation items with scrollbar */}
      <Box
        sx={{
          flexGrow: 1,
          overflowY: "auto",
          overflowX: "hidden",
          "&::-webkit-scrollbar": {
            width: "5px",
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "#f1f1f1",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#c1c1c1",
            borderRadius: "10px",
          },
          "&::-webkit-scrollbar-thumb:hover": {
            backgroundColor: "#a8a8a8",
          },
        }}
      >
        <List sx={{ width: "100%", padding: 0, mt: 1 }}>
          {sidebarItems.map((item, index) => (
            <React.Fragment key={index}>
              <ListItem
                button
                onClick={() => {
                  if (item.hasSubmenu) {
                    toggleMenu(item.menuName);
                  } else if (item.path) {
                    handleNavigate(item.path);
                  } else if (item.onClick) {
                    item.onClick(); // Call the logout handler
                  }
                }}
                sx={{
                  height: 60,
                  px: 2,
                  py: 1,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: open ? "flex-start" : "center",
                  "&:hover": {
                    backgroundColor: "#F6F6F6",
                  },
                  cursor: "pointer",
                  backgroundColor: isActive(item.path)
                    ? "rgba(0, 0, 0, 0.04)"
                    : "transparent",
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: open ? 40 : "auto",
                    color: isActive(item.path) ? "#000" : "#666666",
                  }}
                >
                  {item.icon}
                </ListItemIcon>

                {open && (
                  <>
                    <ListItemText
                      primary={item.text}
                      sx={{
                        "& .MuiTypography-root": {
                          fontSize: "12px",
                          fontWeight: 500,
                          fontFamily: "'Poppins', sans-serif",
                          color: isActive(item.path) ? "#000" : "#2D2D2D",
                        },
                      }}
                    />

                    {item.hasSubmenu &&
                      (openMenus[item.menuName] ? (
                        <ArrowDropUpIcon
                          sx={{ fontSize: 22, color: "#999999" }}
                        />
                      ) : (
                        <ArrowDropDownIcon
                          sx={{ fontSize: 22, color: "#999999" }}
                        />
                      ))}
                  </>
                )}
              </ListItem>

              {/* Dropdown submenus */}
              {item.hasSubmenu && open && (
                <Collapse
                  in={openMenus[item.menuName]}
                  timeout="auto"
                  unmountOnExit
                >
                  <List component="div" disablePadding>
                    {item.subMenuItems.map((subItem, subIndex) => (
                      <ListItem
                        key={subIndex}
                        button
                        onClick={() => handleNavigate(subItem.path)}
                        sx={{
                          pl: 6,
                          py: 0.5,
                          "&:hover": {
                            backgroundColor: "#F6F6F6",
                          },
                          backgroundColor: isActive(subItem.path)
                            ? "rgba(0, 0, 0, 0.04)"
                            : "transparent",
                        }}
                      >
                        <ListItemIcon
                          sx={{
                            minWidth: 36,
                            color: isActive(subItem.path) ? "#000" : "#777777",
                          }}
                        >
                          {subItem.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={subItem.text}
                          sx={{
                            "& .MuiTypography-root": {
                              fontSize: "12px",
                              fontWeight: 500,
                              fontFamily: "'Poppins', sans-serif",
                              color: isActive(subItem.path)
                                ? "#000"
                                : "#2D2D2D",
                            },
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Collapse>
              )}
            </React.Fragment>
          ))}
        </List>
      </Box>
    </Box>
  );
};

export default Sidebar;
