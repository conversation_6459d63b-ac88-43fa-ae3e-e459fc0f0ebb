import React, { useState, useEffect, useMemo } from "react";
import { Box, Typography, Avatar, IconButton } from "@mui/material";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit";
import { useSelector } from "react-redux";
import { selectUserType } from "../../reducers/auth.reducer";

const Topbar = () => {
  const userDetails = useSelector((state) => state.auth);
  const userType = useSelector(selectUserType);
  const baseURL = import.meta.env.VITE_BASE_URL;
  const [imagePath, setImagePath] = useState(null);
  const [profileImageError, setProfileImageError] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Toggle Fullscreen Mode
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Listen for fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () =>
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  // Format time to 12-hour format with AM/PM
  const formatTime = (date) => {
    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";

    hours = hours % 12 || 12; // Convert 0 to 12

    return `${hours}.${minutes} ${ampm}`;
  };

  const profileImageUrl = useMemo(() => {
    const VITE_BASE_URL = import.meta.env.VITE_BASE_URL;

    switch (userType) {
      case "Admin":
        return (
          userDetails.admin?.profile_photo &&
          `${VITE_BASE_URL}/api/admin/get-admin-profile-images/${userDetails.admin.profile_photo}`
        );

      case "Customer":
        return (
          userDetails.customer?.profile_photo &&
          `${VITE_BASE_URL}/api/customer/get-customer-profile-image/${userDetails.customer.profile_photo}`
        );

      case "Chef":
        return (
          userDetails.chef?.profile_photo &&
          `${VITE_BASE_URL}/api/chef/get-chef-profile-image/${userDetails.chef.profile_photo}`
        );

      default:
        return "";
    }
  }, [userType, userDetails]);

  const handleImageError = () => {
    setProfileImageError(true);
  };

  // Format date: e.g., "03rd March"
  const formatDate = (date) => {
    const day = date.getDate();
    const month = date.toLocaleString("default", { month: "long" });

    const getDaySuffix = (day) => {
      if (day > 3 && day < 21) return "th";
      switch (day % 10) {
        case 1:
          return "st";
        case 2:
          return "nd";
        case 3:
          return "rd";
        default:
          return "th";
      }
    };

    return `${day}${getDaySuffix(day)} ${month}`;
  };

  return (
    <Box
      sx={{
        height: "70px",
        display: "flex",
        alignItems: "center",
        // justifyContent: "space-between",
        justifyContent: { xs: "flex-end", md: "space-between" },
        padding: "13px 20px",
       
        backgroundColor: "#F4F4F4",
        gap: { xs: 1, sm: 2 },
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: {xs: 2, md: 6},
          backgroundColor: "white",
          padding: {xs: "10px 10px", md: "12px 20px"},
          borderRadius: 100,
        }}
      >
        {/* Time Box */}
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <AccessTimeIcon
            sx={{ fontSize: 17, mr: 1, color: "text.secondary" }}
          />
          <Typography
            sx={{
              fontSize: 12,
              fontFamily: "'Poppins', sans-serif",
              fontWeight: 500,
              color: "#2D2D2D",
            }}
          >
            {formatTime(currentTime)}
          </Typography>
        </Box>

        {/* Date Box */}
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <CalendarTodayIcon
            sx={{ fontSize: 15, mr: 1, color: "text.secondary" }}
          />
          <Typography
            sx={{
              fontSize: 12,
              fontFamily: "'Poppins', sans-serif",
              fontWeight: 500,
              color: "#2D2D2D",
            }}
            variant="body2"
          >
            {formatDate(currentTime)}
          </Typography>
        </Box>
      </Box>

      <Box display={"flex"} alignItems={"center"} gap={1}>
        {/* User Avatar */}
        <Avatar
          src={
            profileImageError
              ? "https://i.pravatar.cc/150?img=11"
              : profileImageUrl
          }
          alt="User"
          sx={{ 
            width: {xs: 30, md: 40}, 
            height: {xs: 30, md: 40} 
          }}
        />

        {/* Fullscreen Button */}
        <IconButton onClick={toggleFullscreen}>
          {isFullscreen ? (
            <FullscreenExitIcon sx={{ fontSize: 18, color: "#333333" }} />
          ) : (
            <FullscreenIcon sx={{ fontSize: 18, color: "#333333" }} />
          )}
        </IconButton>
      </Box>
    </Box>
  );
};

export default Topbar;
