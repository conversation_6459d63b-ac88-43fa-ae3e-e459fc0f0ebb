import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Grid,
  Chip,
  IconButton,
  Card,
  CardContent,
  useMediaQuery,
  useTheme,
  Avatar,
} from '@mui/material';
import { Close, Person, CalendarToday } from '@mui/icons-material';
import RestaurantMenuIcon from '@mui/icons-material/RestaurantMenu';

const BookingDetailsDialog = ({ open, onClose, booking }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  if (!booking) return null;

  const customer = booking.customer[0];
  const chef = booking.chef[0];

  const getStatusColor = (status) => {
    switch (status) {
      case "Confirmed":
        return {
          backgroundColor: "#E8F5E8",
          color: "#2E7D32",
          borderColor: "#A5D6A7",
        };
      case "Pending":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFCC02",
        };
      case "Completed":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      case "Cancelled":
        return {
          backgroundColor: "#FFEBEE",
          color: "#D32F2F",
          borderColor: "#FFCDD2",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }) +
      " at " +
      date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    );
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      fullScreen={isMobile}
      PaperProps={{
        sx: {
          borderRadius: isMobile ? 0 : 2,
          maxHeight: isMobile ? '100vh' : '90vh',
        }
      }}
    >
      <DialogTitle
        sx={{
          backgroundColor: '#F8F9FA',
          borderBottom: '1px solid #E0E0E0',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: '600', color: '#333' }}>
          Booking Details
        </Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3 }}>
          {/* Booking Status Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" sx={{ fontWeight: '600', color: '#333' }}>
              Booking #{booking.id}
            </Typography>
            <Chip
              label={booking.bookingStatus}
              sx={{
                ...getStatusColor(booking.bookingStatus),
                border: "1px solid",
                fontWeight: "600",
                fontSize: "12px",
              }}
            />
          </Box>

          {/* Booking Information */}
          <Card sx={{ mb: 3, border: '1px solid #E0E0E0' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CalendarToday sx={{ mr: 1, color: '#1976D2' }} />
                <Typography variant="h6" sx={{ fontWeight: '600', color: '#333' }}>
                  Booking Information
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Booking Date & Time
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: '500' }}>
                    {formatDateTime(booking.bookingDate)}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Chef Hourly Rate
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: '500', color: '#1976D2' }}>
                    {formatCurrency(chef.hourlyRate)}/hour
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Location
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: '500' }}>
                    {chef.location}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Customer Information */}
          <Card sx={{ mb: 3, border: '1px solid #E0E0E0' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Person sx={{ mr: 1, color: '#1976D2' }} />
                <Typography variant="h6" sx={{ fontWeight: '600', color: '#333' }}>
                  Customer Information
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Name
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: '500' }}>
                    {customer.name}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Username
                  </Typography>
                  <Typography variant="body1">
                    @{customer.username}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Email
                  </Typography>
                  <Typography variant="body1">
                    {customer.email}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Phone
                  </Typography>
                  <Typography variant="body1">
                    {customer.phone}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Membership Plan
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: '500', color: '#FF9800' }}>
                    {customer.membershipPlan}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Join Date
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(customer.joinDate)}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Chef Information */}
          <Card sx={{ border: '1px solid #E0E0E0' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <RestaurantMenuIcon sx={{ mr: 1, color: '#1976D2' }} />
                <Typography variant="h6" sx={{ fontWeight: '600', color: '#333' }}>
                  Chef Information
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      src={chef.image}
                      alt={chef.name}
                      sx={{ width: 60, height: 60, mr: 2 }}
                    />
                    <Box>
                      <Typography variant="body1" sx={{ fontWeight: '500' }}>
                        {chef.name}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Professional Chef
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Location
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: '500' }}>
                    {chef.location}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Email
                  </Typography>
                  <Typography variant="body1">
                    {chef.email}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Phone
                  </Typography>
                  <Typography variant="body1">
                    {chef.phone}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Hourly Rate
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: '500', color: '#1976D2' }}>
                    {formatCurrency(chef.hourlyRate)}/hour
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                    Join Date
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(chef.joinDate)}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          borderTop: '1px solid #E0E0E0',
          backgroundColor: '#F8F9FA',
          p: 2,
        }}
      >
        <Button
          onClick={onClose}
          variant="contained"
          sx={{
            backgroundColor: '#1976D2',
            color: '#fff',
            '&:hover': {
              backgroundColor: '#1565C0',
            },
            px: 3,
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BookingDetailsDialog;