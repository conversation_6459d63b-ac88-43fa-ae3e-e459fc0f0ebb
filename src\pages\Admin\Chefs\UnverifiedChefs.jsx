import { useState, useEffect, useCallback } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Typography,
  Stack,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
} from "@mui/material";
import { Visibility, Delete, Search } from "@mui/icons-material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { chefData } from "../../../Dummydata/chefdata";
import { useNavigate } from "react-router-dom";
import {
  getAllUnverifiedChefs,
  chefVerifiedByAdmin,
} from "../../../app/service/chef.service";

const AllUnverifiedChefsPage = () => {
  const navigate = useNavigate();
  const [chefs, setChefs] = useState([]);
  const [filteredChefs, setFilteredChefs] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [statusFilter, setStatusFilter] = useState("unverified");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const isTablet = useMediaQuery(theme.breakpoints.down("lg"));

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const fetchChefs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getAllUnverifiedChefs();
      console.log("API Response:", response.data);

      if (
        response.responseCode === 1000 &&
        response.data &&
        response.data.chefs
      ) {
        const chefArray = response.data.chefs;
        // Transform API data to match your existing UI structure
        console.log("Response Data:", response.data.chefs[0].phone);
        const transformedChefs = chefArray.map((chef) => ({
          id: chef.id,
          name: chef.name || "N/A",
          email: chef.email || "N/A",
          phone: chef.phone || "N/A",
          hourlyRate: chef.hourlyRate || 0,
          joinDate: chef.joinDate || "N/A",
          verified: chef.isVerified || false,
          originalData: chef, // Keep original data for reference
        }));

        setChefs(transformedChefs);
        setFilteredChefs(transformedChefs);
      } else {
        setChefs([]);
        setFilteredChefs([]);
        setError("No chefs available");
      }
    } catch (error) {
      setError("Failed to fetch chefs. Please try again.");
      setChefs([]);
      setFilteredChefs([]);
      console.error("Error fetching chefs:", error);
    } finally {
      setLoading(false);
    }
  }, []);
  // Load unverified chef data on component mount
  useEffect(() => {
    fetchChefs();
  }, [fetchChefs]);

  // Filter chefs based on search term, date range, and status
  useEffect(() => {
    let filtered = chefs;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (chef) =>
          chef.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          chef.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          chef.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          chef.phoneNumber.includes(searchTerm) ||
          chef.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Date range filter
    if (startDate || endDate) {
      filtered = filtered.filter((chef) => {
        const joinDate = new Date(chef.joinDate);
        const start = startDate ? new Date(startDate) : new Date("1900-01-01");
        const end = endDate ? new Date(endDate) : new Date("2100-12-31");
        return joinDate >= start && joinDate <= end;
      });
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter((chef) => {
        if (statusFilter === "verified") return chef.verified === true;
        if (statusFilter === "unverified") return chef.verified === false;
        return true;
      });
    }

    setFilteredChefs(filtered);
    setPage(0); // Reset page when filtering
  }, [searchTerm, startDate, endDate, statusFilter, chefs]);

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleView = (chefId) => {
    navigate(`/system/chefs/view/${chefId}`); // Navigate to chef details page
  };

  const handleDelete = (chefId) => {
    console.log("Delete chef:", chefId);
    // Add delete functionality here
  };

  const handleVerifyChef = useCallback(
    async (chefId) => {
      try {
        setLoading(true);
        const response = await chefVerifiedByAdmin(chefId);

        if (response.responseCode === 1000) {
          // Refresh the chef list after successful verification
          await fetchChefs();
        } else {
          setError("Failed to update chef status");
        }
      } catch (error) {
        setError("Failed to update chef status. Please try again.");
        console.error("Error verifying chef:", error);
      } finally {
        setLoading(false);
      }
    },
    [fetchChefs]
  );

  const handleStatusChange = async (chefId, newStatus) => {
    await handleVerifyChef(chefId);
  };

  const getStatusColor = (verified) => {
    if (verified) {
      return {
        backgroundColor: "#E8F5E8",
        color: "#2E7D32",
        borderColor: "#4CAF50",
      };
    } else {
      return {
        backgroundColor: "#FFF3E0",
        color: "#F57C00",
        borderColor: "#FFB74D",
      };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      }) +
      "; " +
      date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    );
  };

  // Calculate total pages
  const totalPages = Math.ceil(filteredChefs.length / rowsPerPage);


  // Mobile Card View Component
  const ChefCard = ({ chef }) => (
    <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
      <CardContent sx={{ p: 2 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-start",
            mb: 2,
          }}
        >
          <Typography
            variant="h6"
            sx={{ fontSize: "16px", fontWeight: "500", color: "#333" }}
          >
            {chef.name}
          </Typography>
          <FormControl size="small" sx={{ minWidth: 100 }}>
            <Select
              value={chef.verified ? "verified" : "unverified"}
              onChange={(e) => handleStatusChange(chef.id, e.target.value)}
              sx={{
                height: 28,
                fontSize: "11px",
                fontWeight: "500",
                ...getStatusColor(chef.verified),
                border: "1px solid",
                "& .MuiOutlinedInput-notchedOutline": {
                  border: "none",
                },
                "& .MuiSelect-select": {
                  padding: "4px 8px !important",
                },
              }}
            >
              <MenuItem value="verified" sx={{ fontSize: "11px" }}>
                Verified
              </MenuItem>
              <MenuItem value="unverified" sx={{ fontSize: "11px" }}>
                Unverified
              </MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Typography variant="body2" sx={{ color: "#666", mb: 1 }}>
          <strong>Title:</strong> {chef.title}
        </Typography>
        <Typography variant="body2" sx={{ color: "#666", mb: 1 }}>
          <strong>Email:</strong> {chef.email}
        </Typography>
        <Typography variant="body2" sx={{ color: "#666", mb: 1 }}>
          <strong>Phone:</strong> {chef.phone}
        </Typography>
        <Typography variant="body2" sx={{ color: "#666", mb: 1 }}>
          <strong>Hourly Rate:</strong> Rs {chef.hourlyRate}/hr
        </Typography>
        <Typography variant="body2" sx={{ color: "#666", mb: 1 }}>
          <strong>Location:</strong> {chef.location}
        </Typography>
        <Typography variant="body2" sx={{ color: "#666", mb: 2 }}>
          <strong>Join Date:</strong> {formatDateTime(chef.joinDate)}
        </Typography>

        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <IconButton
            onClick={() => handleView(chef.id)}
            sx={{
              color: "#3a86ff",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Visibility sx={{ fontSize: 18 }} />
          </IconButton>
          {/* <IconButton
            onClick={() => handleDelete(chef.id)}
            sx={{
              color: "#d00000",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Delete sx={{ fontSize: 18 }} />
          </IconButton> */}
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="xl" sx={{ py: 1 }}>
        <Typography
          variant="h4"
          sx={{
            mb: 3,
            color: "#333",
            fontWeight: "600",
            fontSize: isMobile ? "24px" : "32px",
          }}
        >
          All Unverified Chefs
        </Typography>

        {/* Search and Filters Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            p: 2,
            mb: 2,
          }}
        >
          <Grid container spacing={2}>
            {/* Search Bar */}
            <Grid item xs={12} md={6} lg={4}>
              <TextField
                fullWidth
                placeholder="Search chefs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ color: "#666", mr: 1 }} />,
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      borderColor: "#E0E0E0",
                    },
                  },
                }}
              />
            </Grid>

            {/* Start Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={3}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "& fieldset": {
                          borderColor: "#E0E0E0",
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* End Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={3}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "& fieldset": {
                          borderColor: "#E0E0E0",
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* Clear Filters Button */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm("");
                  setStartDate(null);
                  setEndDate(null);
                  setStatusFilter("unverified");
                }}
                sx={{
                  height: "48px",
                  borderColor: "#E0E0E0",
                  color: "#666",
                  "&:hover": {
                    borderColor: "#1976D2",
                    backgroundColor: "#F5F5F5",
                  },
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Table Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            overflow: "hidden",
          }}
        >
          {/* Desktop/Tablet Table View */}
          {!isMobile ? (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Join Date
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Name
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Email
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                          display: isTablet ? "none" : "table-cell",
                        }}
                      >
                        Phone Number
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Hourly Rate
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Status
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredChefs
                      .slice(
                        page * rowsPerPage,
                        page * rowsPerPage + rowsPerPage
                      )
                      .map((chef, index) => (
                        <TableRow
                          key={chef.id}
                          hover
                          sx={{
                            backgroundColor: "#fff",
                            "&:hover": {
                              backgroundColor: "#F8F9FA",
                            },
                            borderBottom: "1px solid #F0F0F0",
                          }}
                        >
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "400",
                            }}
                          >
                            {formatDateTime(chef.joinDate)}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "500",
                            }}
                          >
                            {chef.name}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                            }}
                          >
                            {chef.email}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              display: isTablet ? "none" : "table-cell",
                            }}
                          >
                            {chef.phone}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "500",
                            }}
                          >
                            Rs {chef.hourlyRate}/hr
                          </TableCell>
                          <TableCell>
                            <FormControl size="small" sx={{ minWidth: 100 }}>
                              <Select
                                value={
                                  chef.verified ? "verified" : "unverified"
                                }
                                onChange={(e) =>
                                  handleStatusChange(chef.id, e.target.value)
                                }
                                sx={{
                                  height: 28,
                                  fontSize: "11px",
                                  fontWeight: "500",
                                  ...getStatusColor(chef.verified),
                                  border: "1px solid",
                                  "& .MuiOutlinedInput-notchedOutline": {
                                    border: "none",
                                  },
                                  "& .MuiSelect-select": {
                                    padding: "4px 8px !important",
                                  },
                                }}
                              >
                                <MenuItem
                                  value="verified"
                                  sx={{ fontSize: "11px" }}
                                >
                                  Verified
                                </MenuItem>
                                <MenuItem
                                  value="unverified"
                                  sx={{ fontSize: "11px" }}
                                >
                                  Unverified
                                </MenuItem>
                              </Select>
                            </FormControl>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={0.5}>
                              <IconButton
                                onClick={() => handleView(chef.id)}
                                sx={{
                                  color: "#3a86ff",
                                  "&:hover": { backgroundColor: "#F5F5F5" },
                                  padding: "4px",
                                }}
                                size="small"
                              >
                                <Visibility sx={{ fontSize: 16 }} />
                              </IconButton>
                              {/* <IconButton
                                onClick={() => handleDelete(chef.id)}
                                sx={{
                                  color: "#d00000",
                                  "&:hover": { backgroundColor: "#F5F5F5" },
                                  padding: "4px",
                                }}
                                size="small"
                              >
                                <Delete sx={{ fontSize: 16 }} />
                              </IconButton> */}
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))}
                    {filteredChefs.length === 0 && (
                      <TableRow>
                        <TableCell
                          colSpan={7}
                          sx={{ textAlign: "center", py: 4 }}
                        >
                          <Typography variant="body1" color="textSecondary">
                            No unverified chefs found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          ) : (
            /* Mobile Card View */
            <Box sx={{ p: 2 }}>
              {filteredChefs
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((chef) => (
                  <ChefCard key={chef.id} chef={chef} />
                ))}
              {filteredChefs.length === 0 && (
                <Box sx={{ textAlign: "center", py: 4 }}>
                  <Typography variant="body1" color="textSecondary">
                    No unverified chefs found
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {/* Custom Pagination */}
          {/* <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              p: 2,
              borderTop: "1px solid #E0E0E0",
              backgroundColor: "#FAFAFA",
              flexDirection: isMobile ? "column" : "row",
              gap: isMobile ? 2 : 0,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                flexWrap: "wrap",
              }}
            >
              {[1, 2, 3, 4].map((pageNum) => (
                <Button
                  key={pageNum}
                  variant={page + 1 === pageNum ? "contained" : "text"}
                  onClick={() => setPage(pageNum - 1)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: 1,
                    fontSize: "14px",
                    ...(page + 1 === pageNum
                      ? {
                          backgroundColor: "#1976D2",
                          color: "white",
                          "&:hover": {
                            backgroundColor: "#1565C0",
                          },
                        }
                      : {
                          color: "#666",
                          "&:hover": {
                            backgroundColor: "#F5F5F5",
                          },
                        }),
                  }}
                >
                  {pageNum}
                </Button>
              ))}
              <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
                ...
              </Typography>
            </Box>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                flexWrap: "wrap",
              }}
            >
              <Typography variant="body2" sx={{ color: "#666" }}>
                Go to page
              </Typography>
              <TextField
                size="small"
                type="number"
                value={page + 1}
                onChange={(e) =>
                  setPage(Math.max(0, Number(e.target.value) - 1))
                }
                sx={{
                  width: 60,
                  "& .MuiOutlinedInput-root": {
                    height: 32,
                    "& fieldset": {
                      borderColor: "#E0E0E0",
                    },
                  },
                }}
                inputProps={{
                  min: 1,
                  max: Math.ceil(filteredChefs.length / rowsPerPage),
                }}
              />
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography variant="body2" color="#666">
                  Show
                </Typography>
                <FormControl size="small" sx={{ minWidth: 80 }}>
                  <Select
                    value={rowsPerPage}
                    onChange={handleChangeRowsPerPage}
                    displayEmpty
                    sx={{
                      backgroundColor: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E0E0E0",
                      },
                    }}
                  >
                    <MenuItem value={5}>5 Row</MenuItem>
                    <MenuItem value={8}>8 Row</MenuItem>
                    <MenuItem value={10}>10 Row</MenuItem>
                    <MenuItem value={25}>25 Row</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Box>
          </Box> */}
          {filteredChefs.length > 0 && (
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                p: 2,
                borderTop: "1px solid #E0E0E0",
                backgroundColor: "#FAFAFA",
                flexDirection: isMobile ? "column" : "row",
                gap: isMobile ? 2 : 0,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  flexWrap: "wrap",
                }}
              >
                {Array.from(
                  { length: Math.min(4, totalPages) },
                  (_, i) => i + 1
                ).map((pageNum) => (
                  <Button
                    key={pageNum}
                    variant={page + 1 === pageNum ? "contained" : "text"}
                    onClick={() => setPage(pageNum - 1)}
                    sx={{
                      minWidth: 32,
                      height: 32,
                      borderRadius: 1,
                      fontSize: "14px",
                      ...(page + 1 === pageNum
                        ? {
                            backgroundColor: "#1976D2",
                            color: "white",
                            "&:hover": {
                              backgroundColor: "#1565C0",
                            },
                          }
                        : {
                            color: "#666",
                            "&:hover": {
                              backgroundColor: "#F5F5F5",
                            },
                          }),
                    }}
                  >
                    {pageNum}
                  </Button>
                ))}
                {totalPages > 4 && (
                  <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
                    ...
                  </Typography>
                )}
              </Box>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  flexWrap: "wrap",
                }}
              >
                <Typography variant="body2" sx={{ color: "#666" }}>
                  Go to page
                </Typography>
                <TextField
                  size="small"
                  type="number"
                  value={page + 1}
                  onChange={(e) =>
                    setPage(
                      Math.max(
                        0,
                        Math.min(totalPages - 1, Number(e.target.value) - 1)
                      )
                    )
                  }
                  sx={{
                    width: 60,
                    "& .MuiOutlinedInput-root": {
                      height: 32,
                      "& fieldset": {
                        borderColor: "#E0E0E0",
                      },
                    },
                  }}
                  inputProps={{
                    min: 1,
                    max: totalPages,
                  }}
                />
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography variant="body2" color="#666">
                    Show
                  </Typography>
                  <FormControl size="small" sx={{ minWidth: 80 }}>
                    <Select
                      value={rowsPerPage}
                      onChange={handleChangeRowsPerPage}
                      displayEmpty
                      sx={{
                        backgroundColor: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E0E0E0",
                        },
                      }}
                    >
                      <MenuItem value={5}>5 Row</MenuItem>
                      <MenuItem value={8}>8 Row</MenuItem>
                      <MenuItem value={10}>10 Row</MenuItem>
                      <MenuItem value={25}>25 Row</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Box>
            </Box>
          )}
        </Paper>
      </Container>
    </LocalizationProvider>
  );
};

export default AllUnverifiedChefsPage;
