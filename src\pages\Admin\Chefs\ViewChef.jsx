import { useState } from "react";
import {
  Box,
  Paper,
  Typography,
  Container,
  Avatar,
  Tab,
  Tabs,
  Button,
  Grid,
  useMediaQuery,
  useTheme,
  Stack,
} from "@mui/material";
import { ArrowBack, Person, Event, Payment, AccountBalance } from "@mui/icons-material";
import VerifiedIcon from '@mui/icons-material/Verified';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import { chefData } from '../../../Dummydata/chefdata';
import { useNavigate, useParams } from "react-router-dom";
import InformationTab from '../../../components/Admin/Chefs/InformationTab'
import BookingTab from '../../../components/Admin/Chefs/BookingTab'
import PaymentTab from '../../../components/Admin/Chefs/PaymentTab'
import WithdrawalTab from '../../../components/Admin/Chefs/WithdrawalTab'

// Main Chef Profile Component
const ChefProfilePage = () => {
  const { chefId } = useParams();
  const navigate = useNavigate();
  const [chef, setChef] = useState(chefData.find(c => c.id === parseInt(chefId)) || {});
  const [currentTab, setCurrentTab] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const isExtraSmall = useMediaQuery(theme.breakpoints.down(480));

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleChefUpdate = (updatedChef) => {
    setChef(updatedChef);
    console.log('Chef updated:', updatedChef);
  };

  const handleBack = () => {
    console.log('Navigate back to chef list');
    navigate('/system/chefs/all');
  };

  const getInitials = (name) => {
    console.log(name);
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <Container 
      maxWidth="xl" 
      sx={{ 
        py: { xs: 1, sm: 1 },
        px: { xs: 1, sm: 2, md: 3 }
      }}
    >
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: { xs: 2, md: 3 },
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 1, sm: 0 }
      }}>
        <Button
          onClick={handleBack}
          startIcon={<ArrowBack />}
          size={isSmallScreen ? "small" : "medium"}
          sx={{
            mr: { sm: 2 },
            color: '#6C5CE7',
            fontSize: { xs: '0.8rem', sm: '0.875rem' },
            alignSelf: { xs: 'flex-start', sm: 'center' },
            '&:hover': { backgroundColor: '#F8F7FF' }
          }}
        >
          BACK
        </Button>
        <Typography 
          variant={isSmallScreen ? "h5" : "h4"} 
          sx={{ 
            fontWeight: '600', 
            color: '#333',
            fontSize: { xs: '1.2rem', sm: '1.5rem', md: '1.6rem' },
            textAlign: { xs: 'center', sm: 'left' },
            wordBreak: 'break-word'
          }}
        >
          Details of {chef.name}
        </Typography>
      </Box>

      <Grid container spacing={{ xs: 2, md: 3 }}>
        {/* Left Side - Profile Info */}
        <Grid item xs={12} md={4} lg={3}>
          <Paper sx={{ 
            p: { xs: 2, sm: 3 }, 
            border: '1px solid #E0E0E0', 
            borderRadius: 2 
          }}>
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Avatar
                src={chef.image}
                sx={{
                  width: { xs: 80, sm: 100, md: 120 },
                  height: { xs: 80, sm: 100, md: 120 },
                  mx: 'auto',
                  mb: 2,
                  fontSize: { xs: '1.2rem', sm: '1.5rem', md: '2rem' },
                  backgroundColor: '#1e3a8a',
                  color: 'white'
                }}
              >
                {getInitials(chef.name)}
              </Avatar>
              <Typography 
                variant={isSmallScreen ? "h6" : "h5"} 
                sx={{ 
                  fontWeight: '600', 
                  mb: 1,
                  fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.2rem' },
                  wordBreak: 'break-word'
                }}
              >
                {chef.name}
              </Typography>
              <Typography 
                variant="body1" 
                sx={{ 
                  fontWeight: '400', 
                  mb: 1, 
                  color: '#666',
                  fontSize: { xs: '0.875rem', sm: '0.8rem' },
                  wordBreak: 'break-all'
                }}
              >
                {chef.email}
              </Typography>
              <Typography 
                variant="body2" 
                color="textSecondary" 
                sx={{ 
                  mb: 2,
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  wordBreak: 'break-word'
                }}
              >
                {chef.location}
              </Typography>
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center',
                mb: 2 
              }}>
                {chef.verified ? 
                  <VerifiedIcon sx={{ color: "green", fontSize: { xs: 20, sm: 24 } }} /> : 
                  <NewReleasesIcon sx={{ color: "red", fontSize: { xs: 20, sm: 24 } }} />
                }
              </Box>
              <Typography 
                variant="body2" 
                color="textSecondary" 
                sx={{ 
                  mb: 1, 
                  fontWeight: '600', 
                  color: 'blue',
                  fontSize: { xs: '0.8rem', sm: '0.875rem' }
                }}
              >
                {chef.hourlyRate ? `$${chef.hourlyRate}` : 'N/A'}
              </Typography>
            </Box>

            {/* Wallet Cards */}
            <Stack spacing={2}>
              <Paper
                sx={{
                  p: { xs: 1.5, sm: 2 },
                  background: 'linear-gradient(135deg, #0EA5E9 0%, #0284C7 100%)',
                  color: 'white',
                  borderRadius: 2
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box
                    sx={{
                      width: { xs: 20, sm: 24 },
                      height: { xs: 14, sm: 16 },
                      backgroundColor: '#FCD34D',
                      borderRadius: 1,
                      mr: 1
                    }}
                  />
                  <Typography 
                    variant="body2"
                    sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                  >
                    Main Wallet
                  </Typography>
                </Box>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    opacity: 0.9,
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  USD
                </Typography>
                <Typography 
                  variant={isSmallScreen ? "h6" : "h5"} 
                  sx={{ 
                    fontWeight: '600',
                    fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.5rem' }
                  }}
                >
                  $0
                </Typography>
              </Paper>

              <Paper
                sx={{
                  p: { xs: 1.5, sm: 2 },
                  background: 'linear-gradient(135deg, #1E3A8A 0%, #1E40AF 100%)',
                  color: 'white',
                  borderRadius: 2
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box
                    sx={{
                      width: { xs: 20, sm: 24 },
                      height: { xs: 14, sm: 16 },
                      backgroundColor: '#FCD34D',
                      borderRadius: 1,
                      mr: 1
                    }}
                  />
                  <Typography 
                    variant="body2"
                    sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                  >
                    Profit Wallet
                  </Typography>
                </Box>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    opacity: 0.9,
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  USD
                </Typography>
                <Typography 
                  variant={isSmallScreen ? "h6" : "h5"} 
                  sx={{ 
                    fontWeight: '600',
                    fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.5rem' }
                  }}
                >
                  $0
                </Typography>
              </Paper>
            </Stack>
          </Paper>
        </Grid>

        {/* Right Side - Tabs Content */}
        <Grid item xs={12} md={8} lg={9}>
          <Paper sx={{ 
            border: '1px solid #E0E0E0', 
            borderRadius: 2, 
            overflow: 'hidden' 
          }}>
            {/* Tabs Header */}
            <Box sx={{ borderBottom: '1px solid #E0E0E0' }}>
              <Tabs
                value={currentTab}
                onChange={handleTabChange}
                variant={isMobile ? "scrollable" : "fullWidth"}
                scrollButtons="auto"
                allowScrollButtonsMobile
                sx={{
                  '& .MuiTab-root': {
                    textTransform: 'none',
                    fontWeight: '500',
                    color: '#666',
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    minWidth: { xs: 'auto', sm: 160 },
                    padding: { xs: '8px 12px', sm: '12px 16px' },
                    '&.Mui-selected': {
                      color: '#6C5CE7',
                      backgroundColor: '#F8F7FF'
                    }
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: '#6C5CE7'
                  },
                  '& .MuiTab-iconWrapper': {
                    fontSize: { xs: '1rem', sm: '1.2rem' }
                  }
                }}
              >
                <Tab
                  icon={<Person />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Information"
                  sx={{ 
                    minHeight: { xs: 48, sm: 64 },
                    '& .MuiTab-iconWrapper': {
                      marginBottom: isExtraSmall ? '4px' : 0,
                      marginRight: isExtraSmall ? 0 : '8px'
                    }
                  }}
                />
                <Tab
                  icon={<Event />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Bookings"
                  sx={{ 
                    minHeight: { xs: 48, sm: 64 },
                    '& .MuiTab-iconWrapper': {
                      marginBottom: isExtraSmall ? '4px' : 0,
                      marginRight: isExtraSmall ? 0 : '8px'
                    }
                  }}
                />
                <Tab
                  icon={<Payment />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Payments"
                  sx={{ 
                    minHeight: { xs: 48, sm: 64 },
                    '& .MuiTab-iconWrapper': {
                      marginBottom: isExtraSmall ? '4px' : 0,
                      marginRight: isExtraSmall ? 0 : '8px'
                    }
                  }}
                />
                <Tab
                  icon={<AccountBalance />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Withdrawals"
                  sx={{ 
                    minHeight: { xs: 48, sm: 64 },
                    '& .MuiTab-iconWrapper': {
                      marginBottom: isExtraSmall ? '4px' : 0,
                      marginRight: isExtraSmall ? 0 : '8px'
                    }
                  }}
                />
              </Tabs>
            </Box>

            {/* Tab Content */}
            <Box sx={{ 
              p: { xs: 1, sm: 2, md: 3 },
              minHeight: { xs: '300px', sm: '400px' }
            }}>
              {currentTab === 0 && (
                <InformationTab chef={chef} onUpdate={handleChefUpdate} />
              )}
              {currentTab === 1 && <BookingTab bookings={chef.bookings} />}
              {currentTab === 2 && <PaymentTab payments={chef.payments} />}
              {currentTab === 3 && <WithdrawalTab withdrawals={chef.withdrawals} />}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ChefProfilePage;