import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Typography,
  Chip,
  Stack,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  CircularProgress,
  Alert,
} from "@mui/material";
import { Visibility, Delete, Search, Warning, Refresh } from "@mui/icons-material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { useNavigate } from "react-router-dom";
import { getAllCustomers } from "../../../app/service/customer.service"; // Import the API function

const AllCustomersPage = () => {
  const navigate = useNavigate();
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [membershipFilter, setMembershipFilter] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);

  // Loading and error states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Delete confirmation dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  const fetchCustomers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getAllCustomers();

      if (response.responseCode === 1000 && response.data && response.data.customers) {
         const customersArray = response.data.customers;
        // Transform API data to match your existing UI structure
        console.log("Response Data:", response.data);
        const transformedCustomers = customersArray.map((customer) => ({
          id: customer.id,
          name: customer.name || 'N/A',
          email: customer.email || 'N/A',
          phone: customer.phone || 'N/A',
          username: customer.username || 'N/A',
          joinDate: customer.dateCreated,
          membershipPlan: customer.subscriptionDetails.planName ? customer.subscriptionDetails.planName : 'No Plan',
          // Add any other fields you might need
          originalData: customer // Keep original data for reference
        }));
        
        setCustomers(transformedCustomers);
        setFilteredCustomers(transformedCustomers);
      } else {
        setCustomers([]);
        setFilteredCustomers([]);
        setError("No customers available");
      }
    } catch (error) {
      setError("Failed to fetch customers. Please try again.");
      setCustomers([]);
      setFilteredCustomers([]);
      console.error('Error fetching customers:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch customers on component mount
  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  // Filter customers based on search term, date range, and membership plan
  useEffect(() => {
    let filtered = customers;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (customer) =>
          customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          customer.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          customer.phone.includes(searchTerm)
      );
    }

    // Date range filter
    if (startDate || endDate) {
      filtered = filtered.filter((customer) => {
        const joinDate = new Date(customer.joinDate);
        const start = startDate ? new Date(startDate) : new Date("1900-01-01");
        const end = endDate ? new Date(endDate) : new Date("2100-12-31");
        return joinDate >= start && joinDate <= end;
      });
    }

    // Membership plan filter
    if (membershipFilter) {
      filtered = filtered.filter((customer) => 
        customer.membershipPlan === membershipFilter
      );
    }

    setFilteredCustomers(filtered);
    setPage(0); // Reset page when filtering
  }, [searchTerm, startDate, endDate, membershipFilter, customers]);

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleView = (customerId) => {
    console.log("View customer:", customerId);
    navigate(`/system/customers/view/${customerId}`);
  };

  const handleDeleteClick = (customer) => {
    setCustomerToDelete(customer);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (customerToDelete) {
      console.log("Delete customer:", customerToDelete.id);
      // Here you would typically call a delete API
      // For now, we'll just remove from local state
      const updatedCustomers = customers.filter(customer => customer.id !== customerToDelete.id);
      setCustomers(updatedCustomers);
      setFilteredCustomers(updatedCustomers);
    }
    setDeleteDialogOpen(false);
    setCustomerToDelete(null);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setCustomerToDelete(null);
  };

  const handleRefresh = () => {
    fetchCustomers();
  };

  const getMembershipColor = (plan) => {
    switch (plan) {
      case "01 Month":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      case "06 Month":
        return {
          backgroundColor: "#F3E5F5",
          color: "#7B1FA2",
          borderColor: "#CE93D8",
        };
      case "12 Month":
        return {
          backgroundColor: "#E0F2F1",
          color: "#00796B",
          borderColor: "#80CBC4",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      })
    );
  };

  const getMembershipPlans = () => {
    const plans = [...new Set(customers.map(customer => customer.membershipPlan))];
    return plans;
  };

  // Calculate total pages
  const totalPages = Math.ceil(filteredCustomers.length / rowsPerPage);

  // Mobile Card View Component
  const CustomerCard = ({ customer }) => (
    <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
            {customer.name}
          </Typography>
          <Chip
            label={customer.membershipPlan}
            size="small"
            sx={{
              ...getMembershipColor(customer.membershipPlan),
              border: "1px solid",
              fontWeight: "500",
              fontSize: "11px",
            }}
          />
        </Box>
        
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Email:</strong> {customer.email}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Phone:</strong> {customer.phone}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Username:</strong> {customer.username}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
          <strong>Join Date:</strong> {(customer.joinDate)}
        </Typography>

        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <IconButton
            onClick={() => handleView(customer.id)}
            sx={{
              color: "#3a86ff",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Visibility sx={{ fontSize: 18 }} />
          </IconButton>
          {/* <IconButton
            onClick={() => handleDeleteClick(customer)}
            sx={{
              color: "#d00000",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Delete sx={{ fontSize: 18 }} />
          </IconButton> */}
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="xl" sx={{ py: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography
            variant="h4"
            sx={{ color: "#333", fontWeight: "600", fontSize: isMobile ? '24px' : '32px' }}
          >
            All Customers
          </Typography>
          
          <Button
            variant="outlined"
            onClick={handleRefresh}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={16} /> : <Refresh />}
            sx={{
              borderColor: '#E0E0E0',
              color: '#666',
              '&:hover': {
                borderColor: '#1976D2',
                backgroundColor: '#F5F5F5',
              },
            }}
          >
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert 
            severity="error" 
            sx={{ mb: 2 }}
            action={
              <Button color="inherit" size="small" onClick={handleRefresh}>
                Retry
              </Button>
            }
          >
            {error}
          </Alert>
        )}

        {/* Search and Filters Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            p: 2,
            mb: 2,
          }}
        >
          <Grid container spacing={2}>
            {/* Search Bar */}
            <Grid item xs={12} md={6} lg={4}>
              <TextField
                fullWidth
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={loading}
                InputProps={{
                  startAdornment: <Search sx={{ color: '#666', mr: 1 }} />,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E0E0E0',
                    },
                  },
                }}
              />
            </Grid>

            {/* Start Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                disabled={loading}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E0E0E0',
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* End Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                disabled={loading}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E0E0E0',
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* Membership Plan Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <FormControl fullWidth disabled={loading}>
                <InputLabel>Membership Plan</InputLabel>
                <Select
                  value={membershipFilter}
                  label="Membership Plan"
                  onChange={(e) => setMembershipFilter(e.target.value)}
                  sx={{
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#E0E0E0',
                    },
                  }}
                >
                  <MenuItem value="">All Plans</MenuItem>
                  {getMembershipPlans().map((plan) => (
                    <MenuItem key={plan} value={plan}>
                      {plan}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Clear Filters Button */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <Button
                fullWidth
                variant="outlined"
                disabled={loading}
                onClick={() => {
                  setSearchTerm("");
                  setStartDate(null);
                  setEndDate(null);
                  setMembershipFilter("");
                }}
                sx={{
                  height: '48px',
                  borderColor: '#E0E0E0',
                  color: '#666',
                  '&:hover': {
                    borderColor: '#1976D2',
                    backgroundColor: '#F5F5F5',
                  },
                }}
              >
                Clear Filters
              </Button>
            </Grid>

            {/* Results count */}
            <Grid item xs={12} sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2" sx={{ color: '#666' }}>
                {loading ? 'Loading...' : `${filteredCustomers.length} customer${filteredCustomers.length !== 1 ? 's' : ''} found`}
              </Typography>
            </Grid>
          </Grid>
        </Paper>

        {/* Table Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            overflow: "hidden",
          }}
        >
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
              <CircularProgress />
              <Typography variant="body1" sx={{ ml: 2, color: '#666' }}>
                Loading customers...
              </Typography>
            </Box>
          ) : (
            <>
              {/* Desktop/Tablet Table View */}
              {!isMobile ? (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
                          <TableCell
                            sx={{
                              color: "#666",
                              fontWeight: "600",
                              fontSize: "12px",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                              borderBottom: "1px solid #E0E0E0",
                              display: isTablet ? 'none' : 'table-cell',
                            }}
                          >
                            Date Created
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#666",
                              fontWeight: "600",
                              fontSize: "12px",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                              borderBottom: "1px solid #E0E0E0",
                            }}
                          >
                            Name
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#666",
                              fontWeight: "600",
                              fontSize: "12px",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                              borderBottom: "1px solid #E0E0E0",
                            }}
                          >
                            Email
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#666",
                              fontWeight: "600",
                              fontSize: "12px",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                              borderBottom: "1px solid #E0E0E0",
                              display: isTablet ? 'none' : 'table-cell',
                            }}
                          >
                            Phone Number
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#666",
                              fontWeight: "600",
                              fontSize: "12px",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                              borderBottom: "1px solid #E0E0E0",
                            }}
                          >
                            Membership Plan
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#666",
                              fontWeight: "600",
                              fontSize: "12px",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                              borderBottom: "1px solid #E0E0E0",
                              display: isTablet ? 'none' : 'table-cell',
                            }}
                          >
                            Username
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#666",
                              fontWeight: "600",
                              fontSize: "12px",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                              borderBottom: "1px solid #E0E0E0",
                            }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {filteredCustomers
                          .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                          .map((customer, index) => (
                            <TableRow
                              key={customer.id}
                              hover
                              sx={{
                                backgroundColor: "#fff",
                                "&:hover": {
                                  backgroundColor: "#F8F9FA",
                                },
                                borderBottom: "1px solid #F0F0F0",
                              }}
                            >
                              <TableCell
                                sx={{
                                  color: "#333",
                                  fontSize: "13px",
                                  fontWeight: "400",
                                  display: isTablet ? 'none' : 'table-cell',
                                }}
                              >
                                {formatDateTime(customer.joinDate)}
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "#333",
                                  fontSize: "13px",
                                  fontWeight: "500",
                                }}
                              >
                                {customer.name}
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "#333",
                                  fontSize: "13px",
                                }}
                              >
                                {customer.email}
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "#333",
                                  fontSize: "13px",
                                  display: isTablet ? 'none' : 'table-cell',
                                }}
                              >
                                {customer.phone}
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={customer.membershipPlan}
                                  size="small"
                                  sx={{
                                    ...getMembershipColor(customer.membershipPlan),
                                    border: "1px solid",
                                    fontWeight: "500",
                                    fontSize: "11px",
                                  }}
                                />
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "#333",
                                  fontSize: "13px",
                                  display: isTablet ? 'none' : 'table-cell',
                                }}
                              >
                                {customer.username}
                              </TableCell>
                              <TableCell>
                                <Stack direction="row" spacing={0.5}>
                                  <IconButton
                                    onClick={() => handleView(customer.id)}
                                    sx={{
                                      color: "#3a86ff",
                                      "&:hover": { backgroundColor: "#F5F5F5" },
                                      padding: "4px",
                                    }}
                                    size="small"
                                  >
                                    <Visibility sx={{ fontSize: 16 }} />
                                  </IconButton>
                                  {/* <IconButton
                                    onClick={() => handleDeleteClick(customer)}
                                    sx={{
                                      color: "#d00000",
                                      "&:hover": { backgroundColor: "#F5F5F5" },
                                      padding: "4px",
                                    }}
                                    size="small"
                                  >
                                    <Delete sx={{ fontSize: 16 }} />
                                  </IconButton> */}
                                </Stack>
                              </TableCell>
                            </TableRow>
                          ))}
                        {filteredCustomers.length === 0 && !loading && (
                          <TableRow>
                            <TableCell colSpan={7} sx={{ textAlign: "center", py: 4 }}>
                              <Typography variant="body1" color="textSecondary">
                                {error ? "Failed to load customers" : "No customers found"}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              ) : (
                /* Mobile Card View */
                <Box sx={{ p: 2 }}>
                  {filteredCustomers
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((customer) => (
                      <CustomerCard key={customer.id} customer={customer} />
                    ))}
                  {filteredCustomers.length === 0 && !loading && (
                    <Box sx={{ textAlign: "center", py: 4 }}>
                      <Typography variant="body1" color="textSecondary">
                        {error ? "Failed to load customers" : "No customers found"}
                      </Typography>
                    </Box>
                  )}
                </Box>
              )}
            </>
          )}

          {/* Custom Pagination */}
          {filteredCustomers.length > 0 && (
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                p: 2,
                borderTop: "1px solid #E0E0E0",
                backgroundColor: "#FAFAFA",
                flexDirection: isMobile ? 'column' : 'row',
                gap: isMobile ? 2 : 0,
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
                {Array.from({ length: Math.min(4, totalPages) }, (_, i) => i + 1).map((pageNum) => (
                  <Button
                    key={pageNum}
                    variant={page + 1 === pageNum ? "contained" : "text"}
                    onClick={() => setPage(pageNum - 1)}
                    sx={{
                      minWidth: 32,
                      height: 32,
                      borderRadius: 1,
                      fontSize: "14px",
                      ...(page + 1 === pageNum
                        ? {
                            backgroundColor: "#1976D2",
                            color: "white",
                            "&:hover": {
                              backgroundColor: "#1565C0",
                            },
                          }
                        : {
                            color: "#666",
                            "&:hover": {
                              backgroundColor: "#F5F5F5",
                            },
                          }),
                    }}
                  >
                    {pageNum}
                  </Button>
                ))}
                {totalPages > 4 && (
                  <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
                    ...
                  </Typography>
                )}
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
                <Typography variant="body2" sx={{ color: "#666" }}>
                  Go to page
                </Typography>
                <TextField
                  size="small"
                  type="number"
                  value={page + 1}
                  onChange={(e) =>
                    setPage(Math.max(0, Math.min(totalPages - 1, Number(e.target.value) - 1)))
                  }
                  sx={{
                    width: 60,
                    "& .MuiOutlinedInput-root": {
                      height: 32,
                      "& fieldset": {
                        borderColor: "#E0E0E0",
                      },
                    },
                  }}
                  inputProps={{
                    min: 1,
                    max: totalPages,
                  }}
                />
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography variant="body2" color="#666">
                    Show
                  </Typography>
                  <FormControl size="small" sx={{ minWidth: 80 }}>
                    <Select
                      value={rowsPerPage}
                      onChange={handleChangeRowsPerPage}
                      displayEmpty
                      sx={{
                        backgroundColor: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E0E0E0",
                        },
                      }}
                    >
                      <MenuItem value={5}>5 Row</MenuItem>
                      <MenuItem value={8}>8 Row</MenuItem>
                      <MenuItem value={10}>10 Row</MenuItem>
                      <MenuItem value={25}>25 Row</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Box>
            </Box>
          )}
        </Paper>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={handleDeleteCancel}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 2,
              border: "1px solid #E0E0E0",
            }
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              color: "#d32f2f",
              fontWeight: "600",
              pb: 1,
            }}
          >
            <Warning sx={{ color: "#d32f2f" }} />
            Confirm Delete
          </DialogTitle>
          <DialogContent sx={{ pt: 1 }}>
            <DialogContentText sx={{ color: "#333", fontSize: "16px" }}>
              Are you sure you want to delete the customer{" "}
              <strong>"{customerToDelete?.name}"</strong>?
            </DialogContentText>
            <DialogContentText sx={{ color: "#666", mt: 1, fontSize: "14px" }}>
              This action cannot be undone. All data associated with this customer will be permanently removed.
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ p: 2, pt: 1, gap: 1 }}>
            <Button
              onClick={handleDeleteCancel}
              variant="outlined"
              sx={{
                borderColor: "#E0E0E0",
                color: "#666",
                "&:hover": {
                  borderColor: "#1976D2",
                  backgroundColor: "#F5F5F5",
                },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteConfirm}
              variant="contained"
              sx={{
                backgroundColor: "#d32f2f",
                color: "white",
                "&:hover": {
                  backgroundColor: "#b71c1c",
                },
              }}
            >
              Delete Customer
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default AllCustomersPage;