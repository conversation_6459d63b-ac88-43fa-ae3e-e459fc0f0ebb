import { useState } from "react";
import {
  Box,
  Paper,
  Typography,
  Container,
  Avatar,
  Tab,
  Tabs,
  Button,
  Grid,
  useMediaQuery,
  useTheme,
  Stack,
} from "@mui/material";
import { ArrowBack, Person, Event, Payment, Subscriptions } from "@mui/icons-material";
import VerifiedIcon from '@mui/icons-material/Verified';
import PersonIcon from '@mui/icons-material/Person';
import { customerData } from '../../../Dummydata/customerData';
import { useNavigate, useParams } from "react-router-dom";
import InformationTab from '../../../components/Admin/Customer/InformationTab';
import BookingTab from '../../../components/Admin/Customer/BookingTab'
import PaymentTab from '../../../components/Admin/Customer/PaymentTab';
import SubscriptionTab from '../../../components/Admin/Customer/SubscriptionTab'

// Main Customer Profile Component
const CustomerProfilePage = () => {
  const { customerId } = useParams();
  const navigate = useNavigate();
  const [customer, setCustomer] = useState(customerData.find(c => c.id === parseInt(customerId)) || {});
  const [currentTab, setCurrentTab] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const isExtraSmall = useMediaQuery(theme.breakpoints.down(480));

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleCustomerUpdate = (updatedCustomer) => {
    setCustomer(updatedCustomer);
    console.log('Customer updated:', updatedCustomer);
  };

  const handleBack = () => {
    console.log('Navigate back to customer list');
    navigate('/system/customers');
  };

  const getInitials = (name) => {
    console.log(name);
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getMembershipColor = (plan) => {
    switch (plan) {
      case "1 Month":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      case "3 Month":
        return {
          backgroundColor: "#F3E5F5",
          color: "#7B1FA2",
          borderColor: "#CE93D8",
        };
      case "6 Month":
        return {
          backgroundColor: "#E0F2F1",
          color: "#00796B",
          borderColor: "#80CBC4",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <Container 
      maxWidth="xl" 
      sx={{ 
        py: { xs: 1, sm: 1 },
        px: { xs: 1, sm: 2, md: 3 }
      }}
    >
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: { xs: 2, md: 3 },
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 1, sm: 0 }
      }}>
        <Button
          onClick={handleBack}
          startIcon={<ArrowBack />}
          size={isSmallScreen ? "small" : "medium"}
          sx={{
            mr: { sm: 2 },
            color: '#6C5CE7',
            fontSize: { xs: '0.8rem', sm: '0.875rem' },
            alignSelf: { xs: 'flex-start', sm: 'center' },
            '&:hover': { backgroundColor: '#F8F7FF' }
          }}
        >
          BACK
        </Button>
        <Typography 
          variant={isSmallScreen ? "h5" : "h4"} 
          sx={{ 
            fontWeight: '600', 
            color: '#333',
            fontSize: { xs: '1.2rem', sm: '1.5rem', md: '1.6rem' },
            textAlign: { xs: 'center', sm: 'left' },
            wordBreak: 'break-word'
          }}
        >
          Details of {customer.name}
        </Typography>
      </Box>

      <Grid container spacing={{ xs: 2, md: 3 }}>
        {/* Left Side - Profile Info */}
        <Grid item xs={12} md={4} lg={3}>
          <Paper sx={{ 
            p: { xs: 2, sm: 3 }, 
            border: '1px solid #E0E0E0', 
            borderRadius: 2 
          }}>
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Avatar
                sx={{
                  width: { xs: 80, sm: 100, md: 120 },
                  height: { xs: 80, sm: 100, md: 120 },
                  mx: 'auto',
                  mb: 2,
                  fontSize: { xs: '1.2rem', sm: '1.5rem', md: '2rem' },
                  backgroundColor: '#1976D2',
                  color: 'white'
                }}
              >
                {getInitials(customer.name)}
              </Avatar>
              <Typography 
                variant={isSmallScreen ? "h6" : "h5"} 
                sx={{ 
                  fontWeight: '600', 
                  mb: 1,
                  fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.2rem' },
                  wordBreak: 'break-word'
                }}
              >
                {customer.name}
              </Typography>
              <Typography 
                variant="body1" 
                sx={{ 
                  fontWeight: '400', 
                  mb: 1, 
                  color: '#666',
                  fontSize: { xs: '0.875rem', sm: '0.8rem' },
                  wordBreak: 'break-all'
                }}
              >
                {customer.email}
              </Typography>
              <Typography 
                variant="body2" 
                color="textSecondary" 
                sx={{ 
                  mb: 1,
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  wordBreak: 'break-word'
                }}
              >
                {customer.phone}
              </Typography>
              <Typography 
                variant="body2" 
                color="textSecondary" 
                sx={{ 
                  mb: 2,
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  wordBreak: 'break-word'
                }}
              >
                @{customer.username}
              </Typography>
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center',
                mb: 2 
              }}>
                <PersonIcon sx={{ color: "#1976D2", fontSize: { xs: 20, sm: 24 } }} />
              </Box>
            </Box>

            {/* Customer Info Cards */}
            <Stack spacing={2}>
              <Paper
                sx={{
                  p: { xs: 1.5, sm: 2 },
                  background: 'linear-gradient(135deg, #4CAF50 0%, #45A049 100%)',
                  color: 'white',
                  borderRadius: 2
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <VerifiedIcon sx={{ fontSize: { xs: 16, sm: 20 }, mr: 1 }} />
                  <Typography 
                    variant="body2"
                    sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                  >
                    Membership Plan
                  </Typography>
                </Box>
                <Typography 
                  variant={isSmallScreen ? "h6" : "h5"} 
                  sx={{ 
                    fontWeight: '600',
                    fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.5rem' }
                  }}
                >
                  {customer.membershipPlan}
                </Typography>
              </Paper>

              <Paper
                sx={{
                  p: { xs: 1.5, sm: 2 },
                  background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
                  color: 'white',
                  borderRadius: 2
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Event sx={{ fontSize: { xs: 16, sm: 20 }, mr: 1 }} />
                  <Typography 
                    variant="body2"
                    sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                  >
                    Join Date
                  </Typography>
                </Box>
                <Typography 
                  variant="body1" 
                  sx={{ 
                    fontWeight: '600',
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }}
                >
                  {formatDate(customer.joinDate)}
                </Typography>
              </Paper>

              {/* Membership Status Chip */}
              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <Box
                  sx={{
                    ...getMembershipColor(customer.membershipPlan),
                    px: 2,
                    py: 1,
                    borderRadius: 2,
                    border: '1px solid',
                    fontWeight: '600',
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    textAlign: 'center'
                  }}
                >
                  Active Member
                </Box>
              </Box>
            </Stack>
          </Paper>
        </Grid>

        {/* Right Side - Tabs Content */}
        <Grid item xs={12} md={8} lg={9}>
          <Paper sx={{ 
            border: '1px solid #E0E0E0', 
            borderRadius: 2, 
            overflow: 'hidden' 
          }}>
            {/* Tabs Header */}
            <Box sx={{ borderBottom: '1px solid #E0E0E0' }}>
              <Tabs
                value={currentTab}
                onChange={handleTabChange}
                variant={isMobile ? "scrollable" : "fullWidth"}
                scrollButtons="auto"
                allowScrollButtonsMobile
                sx={{
                  '& .MuiTab-root': {
                    textTransform: 'none',
                    fontWeight: '500',
                    color: '#666',
                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                    minWidth: { xs: 'auto', sm: 160 },
                    padding: { xs: '8px 12px', sm: '12px 16px' },
                    '&.Mui-selected': {
                      color: '#6C5CE7',
                      backgroundColor: '#F8F7FF'
                    }
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: '#6C5CE7'
                  },
                  '& .MuiTab-iconWrapper': {
                    fontSize: { xs: '1rem', sm: '1.2rem' }
                  }
                }}
              >
                <Tab
                  icon={<Person />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Information"
                  sx={{ 
                    minHeight: { xs: 48, sm: 64 },
                    '& .MuiTab-iconWrapper': {
                      marginBottom: isExtraSmall ? '4px' : 0,
                      marginRight: isExtraSmall ? 0 : '8px'
                    }
                  }}
                />
                <Tab
                  icon={<Event />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Bookings"
                  sx={{ 
                    minHeight: { xs: 48, sm: 64 },
                    '& .MuiTab-iconWrapper': {
                      marginBottom: isExtraSmall ? '4px' : 0,
                      marginRight: isExtraSmall ? 0 : '8px'
                    }
                  }}
                />
                <Tab
                  icon={<Payment />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Payments"
                  sx={{ 
                    minHeight: { xs: 48, sm: 64 },
                    '& .MuiTab-iconWrapper': {
                      marginBottom: isExtraSmall ? '4px' : 0,
                      marginRight: isExtraSmall ? 0 : '8px'
                    }
                  }}
                />
                <Tab
                  icon={<Subscriptions />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Subscriptions"
                  sx={{ 
                    minHeight: { xs: 48, sm: 64 },
                    '& .MuiTab-iconWrapper': {
                      marginBottom: isExtraSmall ? '4px' : 0,
                      marginRight: isExtraSmall ? 0 : '8px'
                    }
                  }}
                />
              </Tabs>
            </Box>

            {/* Tab Content */}
            <Box sx={{ 
              p: { xs: 1, sm: 2, md: 3 },
              minHeight: { xs: '300px', sm: '400px' }
            }}>
              {currentTab === 0 && (
                <InformationTab customer={customer} onUpdate={handleCustomerUpdate} />
              )}
              {currentTab === 1 && <BookingTab bookings={customer.bookings} />}
              {currentTab === 2 && <PaymentTab payments={customer.payments} />}
              {currentTab === 3 && <SubscriptionTab subscription={customer.subscription} />}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default CustomerProfilePage;