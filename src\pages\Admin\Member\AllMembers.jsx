import { useState, useEffect } from "react";
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import DeleteIcon from "@mui/icons-material/Delete"; // Import Delete Icon
import Swal from "sweetalert2"; // Import SweetAlert2
import ReusableTable from "../../../components/Common/table/ReusableTable";
import ReusableTextField from "../../../components/Common/TextField/TextField";
import { deleteMember, getAllMembers } from "../../../app/service/member.serivce";

export default function AllMembers() {
  const [error, setError] = useState(null);
  const [members, setMembers] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterValue, setFilterValue] = useState("");

  // Available status options for the filter
  const filterOptions = ["Paid", "Non-Paid"];

  // Define your columns
  const columns = [
    { id: "barcode", label: "BARCODE" },
    { id: "firstName", label: "FIRST NAME" },
    { id: "lastName", label: "LAST NAME" },
    { id: "phoneNumber", label: "PHONE NUMBER" },
  ];

  const fetchAllMembers = async () => {
    try {
      const response = await getAllMembers();
      if (response?.data?.allMembers) {
        const formattedMembers = response.data.allMembers.map((member) => ({
          id: member.id, // Include the member ID for delete functionality
          barcode: member.barcode,
          firstName: member.member_first_name || "N/A",
          lastName: member.member_last_name || "N/A",
          phoneNumber: member.mobile_number || "N/A",
          status: member.is_paid ? "Paid" : "Non-Paid", // Map status to Paid/Non-Paid
        }));
        setMembers(formattedMembers);
      } else {
        setMembers([]);
      }
    } catch (err) {
      setError("Failed to fetch members. Please try again later.");
      console.error("Error fetching members:", err);
    }
  };

  useEffect(() => {
    fetchAllMembers();
  }, []);

  // Handle search change
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  // Handle filter change
  const handleFilterChange = (event) => {
    setFilterValue(event.target.value);
  };

  // Handle delete action
  const handleDelete = async (row) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await deleteMember({ memberId: row.id });
          if (response.data.status === 1) {
            Swal.fire("Deleted!", "The member has been deleted.", "success");
            fetchAllMembers(); // Refresh the table data
          } else {
            Swal.fire("Error!", "Failed to delete the member.", "error");
          }
        } catch (error) {
          Swal.fire("Error!", "An error occurred while deleting the member.", "error");
          console.error("Error deleting member:", error);
        }
      }
    });
  };

  // Filter data based on search query and filter value
  const filteredData = members.filter((row) => {
    const matchesSearch = searchQuery
      ? Object.values(row).some(
          (value) =>
            value &&
            value.toString().toLowerCase().includes(searchQuery.toLowerCase())
        )
      : true;

    const matchesFilter = filterValue
      ? row.status?.toLowerCase() === filterValue.toLowerCase()
      : true;

    return matchesSearch && matchesFilter;
  });

  return (
    <Box sx={{}}>
      <Box sx={{ display: "flex", gap: 2, marginBottom: 2 }}>
        <FormControl sx={{ minWidth: "30%" }}>
          <InputLabel id="filter-label">Filter By Status</InputLabel>
          <Select
            sx={{
              backgroundColor: "#FFFFFF",
              borderRadius: "10px",
              "& .MuiOutlinedInput-notchedOutline": {
                borderRadius: "10px",
              },
              "& .MuiSelect-select": {
                fontSize: "13px",
                fontFamily: "'Poppins', sans-serif",
                padding: "15px 14px",
              },
              "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderColor: "#000",
              },
              "&:hover .MuiOutlinedInput-notchedOutline": {
                borderColor: "#000",
              },
            }}
            labelId="filter-label"
            value={filterValue}
            onChange={handleFilterChange}
            label="Filter By Status"
            IconComponent={KeyboardArrowDownIcon}
          >
            <MenuItem value="">All</MenuItem>
            {filterOptions.map((option) => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <ReusableTextField
          label="Search"
          value={searchQuery}
          onChange={handleSearchChange}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <ReusableTable
        columns={columns}
        data={filteredData}
        showStatus={true}
        statusColumn="status"
        showActions={true}
        title="Members"
        searchEnabled={false}
        filterEnabled={false}
        paginationEnabled={true}
        rowHeight={53}
        actionButtons={[
          {
            icon: <DeleteIcon fontSize="small" sx={{ color: "#d32f2f" }} />,
            onClick: handleDelete,
          },
        ]}
      />
    </Box>
  );
}