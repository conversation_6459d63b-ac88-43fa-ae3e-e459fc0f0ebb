import React, { useState, useRef, useEffect } from "react";
import { Box, Grid, Card, Avatar, Typography, Chip } from "@mui/material";
import ReusableTextField from "../../../components/Common/TextField/TextField";
import ReusableTable from "../../../components/Common/table/ReusableTable";

const user = {
  name: "<PERSON>",
  age: "25 Yrs",
  nic: "200084566259",
  paymentStatus: "Success",
  avatar: "https://i.pravatar.cc/100",
};

const attendanceTablecolumns = [
  { id: "date", label: "DATE" },
  { id: "inTime", label: "IN TIME" },
  { id: "outTime", label: "OUT TIME" },
];

const measurementsTablecolumns = [
  { id: "date", label: "DATE" },
  { id: "height", label: "HEIGHT (cm)" },
  { id: "weight", label: "WEIGHT (kg)" },
];

const attendanceData = Array(5).fill({
  date: "03/25",
  inTime: "7.00PM",
  outTime: "8.00PM",
});
const measurementData = Array(5).fill({
  date: "03/25",
  height: "165",
  weight: "55",
});

const MemberProfile = () => {

  return (
    <Box>
      {/* User Info Card */}
      <Card sx={{ p: 3, mt: 3, mb: 3, boxShadow: 2, borderRadius: "12px" }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3} display="flex" justifyContent="center">
            <Avatar
              src={user.avatar}
              sx={{ width: 120, height: 120, boxShadow: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={9} container spacing={1}>
            <Grid item xs={12}>
              <Typography variant="h5" fontWeight="600">
                {user.name}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body1">
                <strong>Age:</strong> {user.age}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body1">
                <strong>NIC:</strong> {user.nic}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} display="flex" alignItems="center">
              <Typography variant="body1" sx={{ mr: 1 }}>
                <strong>Payment Status:</strong>
              </Typography>
              <Chip
                label={user.paymentStatus}
                color="success"
                sx={{
                  fontSize: "14px",
                  backgroundColor: "#A0FFA0",
                  color: "#065F46",
                  fontWeight: "500",
                }}
              />
            </Grid>
          </Grid>
        </Grid>
      </Card>

      {/* Tables Section */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <ReusableTable
            columns={attendanceTablecolumns}
            data={attendanceData}
            showActions={false}
            title="Attendance"
            searchEnabled
            filterEnabled
            paginationEnabled
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <ReusableTable
            columns={measurementsTablecolumns}
            data={measurementData}
            showActions={false}
            title="Measurements"
            searchEnabled
            filterEnabled
            paginationEnabled
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default MemberProfile;
