import { useState } from "react";
import {
  <PERSON>,
  <PERSON>per,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  StepConnector,
  <PERSON><PERSON>k<PERSON>,
  <PERSON><PERSON>,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import PersonalInforStepper from "../../../components/Admin/Member/PersonalInfoStepper";
import MedicalInfoStepper from "../../../components/Admin/Member/MedicalInfoStepper";
import PaymentStepper from "../../../components/Admin/Member/PaymentStepper";
import ReusableButton from "../../../components/Common/Buttons/ReusableButton";
import { useSelector } from "react-redux";
import { saveMember } from "../../../app/service/member.serivce";
import { apiPostFormData } from "../../../api/apiManager";

// Custom Styled components for black color
const BlackStepConnector = styled(StepConnector)(({ theme }) => ({
  "&.MuiStepConnector-root": {
    top: 10,
    left: "calc(-50% + 20px)",
    right: "calc(50% + 20px)",
  },
  "& .MuiStepConnector-line": {
    borderColor: "#141414", // Black color for connector line
  },
}));

const BlackStepIconRoot = styled("div")(({ theme, ownerState }) => ({
  backgroundColor: ownerState.active
    ? "#141414"
    : ownerState.completed
      ? "#141414"
      : "#E0E0E0",
  zIndex: 1,
  color: "#FFFFFF",
  width: 30,
  height: 30,
  display: "flex",
  borderRadius: "50%",
  justifyContent: "center",
  alignItems: "center",
}));

function BlackStepIcon(props) {
  const { active, completed, className } = props;

  return (
    <BlackStepIconRoot ownerState={{ active, completed }} className={className}>
      {completed ? "✓" : props.icon}
    </BlackStepIconRoot>
  );
}
const resizeImage = async (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        const canvas = document.createElement("canvas");
        const MAX_WIDTH = 800;
        const MAX_HEIGHT = 800;
        let width = img.width;
        let height = img.height;

        if (width > height) {
          if (width > MAX_WIDTH) {
            height = Math.round((height * MAX_WIDTH) / width);
            width = MAX_WIDTH;
          }
        } else {
          if (height > MAX_HEIGHT) {
            width = Math.round((width * MAX_HEIGHT) / height);
            height = MAX_HEIGHT;
          }
        }

        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            const optimizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(optimizedFile);
          },
          file.type,
          0.8
        );
      };
    };
  });
};

export default function MemberRegistrationStepper() {
  const [activeStep, setActiveStep] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const steps = ["Personal Information", "Medical And Emergency", "Payments"];
  const userDetails = useSelector((state) => state.auth);
  const [memberData, setMemberData] = useState({
    // Personal info
    userTypeId: 3,
    barcode: "",
    title: "",
    memberFirstName: "",
    memberLastName: "",
    nic: "",
    age: "",
    mobileNumber: "",
    whatsappNumber: "",
    dateOfBirth: "",
    gender: "",
    houseNo: "",
    addressLineOne: "",
    addressLineTwo: "",
    city: "",
    state: "",
    country: "",
    dateOfJoining: "",
    email: "",
    profileImage: null,
    selectedImage: null, // For the file upload
    branchId: userDetails?.gym_user?.branch_id || "",

    // Medical info
    memberHeightWeight: {
      height: "",
      weight: "",
    },
    memberAllergies: [],
    memberMedicalConditions: [],
    specialNotes: [],
    memberParentDetails: [],
    memberEmegency: {
      emergencyContactName: "",
      emergencyContactNumber: "",
    },

    // Payment info
    membershipPricePlanId: null,
  });

  const handleNext = async () => {
    if (activeStep < steps.length - 1) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    } else {
      if (!memberData.membershipPricePlanId) {
        console.error("Please select a payment plan");
        // Show error message to user
        return;
      }

      // Final step - Save data
      try {
        // First upload image if selected
        if (memberData.profileImage) {
          const optimizedImage = await resizeImage(memberData.profileImage);
          const imageFormData = new FormData();
          imageFormData.append("file", optimizedImage);

          try {
            const imageResponse = await apiPostFormData(
              "/api/member/save-member-image",
              imageFormData
            );

            const imageName = imageResponse.data.data.filenames[0];
            // Update member data with image name
            setMemberData((prev) => ({
              ...prev,
              profileImage: imageName,
            }));

            // Now save the member with the image name
            const payload = {
              ...memberData,
              profileImage: imageName,
            };

            // Remove selectedImage from payload as it's not needed in API
            delete payload.selectedImage;

            const response = await saveMember(payload);
            if (parseInt(response.responseCode) === 1000) {
              console.log("Status", response.data.status);
              if (parseInt(response.data.status) === 0) {
                setActiveStep(0);
                setMemberData({
                  userTypeId: 3,
                  barcode: "",
                  title: "",
                  memberFirstName: "",
                  memberLastName: "",
                  nic: "",
                  age: "",
                  mobileNumber: "",
                  whatsappNumber: "",
                  dateOfBirth: "",
                  gender: "",
                  houseNo: "",
                  addressLineOne: "",
                  addressLineTwo: "",
                  city: "",
                  state: "",
                  country: "",
                  dateOfJoining: "",
                  email: "",
                  profileImage: null,
                  selectedImage: null,
                  branchId: userDetails?.gym_user?.branch_id || "",
                  memberHeightWeight: { height: "", weight: "" },
                  memberAllergies: [],
                  memberMedicalConditions: [],
                  specialNotes: [],
                  memberParentDetails: [],
                  memberEmegency: {
                    emergencyContactName: "",
                    emergencyContactNumber: "",
                  },
                  membershipPricePlanId: null,
                });
                showSnackbar(response.message, "success");
              } else {
                showSnackbar(response.message, "error");
              }
            } else {
              if (parseInt(response.data.status) === 0) {
                showSnackbar(response.message, "error");
              } else {
                showSnackbar(response.message, "error");
              }
            }
            // Handle success - maybe show a success message or redirect
          } catch (error) {
            console.error("Error saving member image:", error);
            // Handle error
          }
        } else {
          // No image selected, just save member data
          try {
            // Create a copy of memberData without selectedImage
            const payload = { ...memberData };
            delete payload.selectedImage;
            console.log("payload", payload);
            const response = await saveMember(payload);
            if (parseInt(response.responseCode) === 1000) {
              console.log("Status", response.data.status);
              if (parseInt(response.data.status) === 0) {
                setActiveStep(0);
                setMemberData({
                  userTypeId: 3,
                  barcode: "",
                  title: "",
                  memberFirstName: "",
                  memberLastName: "",
                  nic: "",
                  age: "",
                  mobileNumber: "",
                  whatsappNumber: "",
                  dateOfBirth: "",
                  gender: "",
                  houseNo: "",
                  addressLineOne: "",
                  addressLineTwo: "",
                  city: "",
                  state: "",
                  country: "",
                  dateOfJoining: "",
                  email: "",
                  profileImage: null,
                  selectedImage: null,
                  branchId: userDetails?.gym_user?.branch_id || "",
                  memberHeightWeight: { height: "", weight: "" },
                  memberAllergies: [],
                  memberMedicalConditions: [],
                  specialNotes: [],
                  memberParentDetails: [],
                  memberEmegency: {
                    emergencyContactName: "",
                    emergencyContactNumber: "",
                  },
                  membershipPricePlanId: null,
                });
                showSnackbar(response.message, "success");
              } else {
                showSnackbar(response.message, "error");
              }
            } else {
              if (parseInt(response.data.status) === 0) {
                showSnackbar(response.message, "error");
              } else {
                showSnackbar(response.message, "error");
              }
            }
            // console.log("Member saved successfully:", response);
            // Handle success
          } catch (error) {
            console.error("Error saving member:", error);
            // Handle error
          }
        }
      } catch (error) {
        console.error("Error in save process:", error);
      }
    }
  };

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    }
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <PersonalInforStepper
            memberData={memberData}
            setMemberData={setMemberData}
          />
        );
      case 1:
        return (
          <MedicalInfoStepper
            memberData={memberData}
            setMemberData={setMemberData}
          />
        );
      case 2:
        return (
          <PaymentStepper
            memberData={memberData}
            setMemberData={setMemberData}
          />
        );
      default:
        return <Typography>Unknown Step</Typography>;
    }
  };

  return (
    <Box>
      <Stepper
        activeStep={activeStep}
        alternativeLabel
        connector={<BlackStepConnector />}
      >
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel
              StepIconComponent={BlackStepIcon}
              sx={{
                "& .MuiStepLabel-label": {
                  color: "#141414", // Black color for step labels
                  "&.Mui-active": {
                    color: "#141414", // Black color when step is active
                  },
                  "&.Mui-completed": {
                    color: "#141414", // Black color when step is completed
                  },
                },
              }}
            >
              {label}
            </StepLabel>
          </Step>
        ))}
      </Stepper>

      <Box sx={{ mt: 3, mb: 2 }}>{renderStepContent(activeStep)}</Box>

      <Box sx={{ display: "flex", justifyContent: "end", mt: 2, gap: 2 }}>
        <ReusableButton
          text={"Back"}
          disabled={activeStep === 0} // 🔹 Disables Back button on first step
          onClick={handleBack}
        />
        <ReusableButton
          text={activeStep === steps.length - 1 ? "Save" : "Next"}
          onClick={handleNext}
        />
      </Box>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}
