import React, { useState, useEffect, useMemo } from "react";
import {
  Box,
  Typography,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Button,
  Alert,
  Snackbar,
} from "@mui/material";
import ReusableTextField from "../../../components/Common/TextField/TextField";
import {
  getActiveMembershipByBarcode,
  getAllActiveMembershipPlans,
  assignMembershipPlan,
} from "../../../app/service/member.serivce";

export default function MembershipAssign() {
  const [barcode, setBarcode] = useState("");
  const [student, setStudent] = useState(null);
  const [membershipPlans, setMembershipPlans] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState("");
  const [activeMembership, setActiveMembership] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  // Fetch all active membership plans
  const fetchMembershipPlans = async () => {
    try {
      const response = await getAllActiveMembershipPlans();
      if (response.responseCode === 1000) {
        const formattedPlans = response.data.activeMembershipPlans.map(
          (plan) => ({
            id: plan.id,
            name: plan.membership_plan_name,
          })
        );
        setMembershipPlans(formattedPlans);
      } else {
        showSnackbar(response.message, "error");
      }
    } catch (error) {
      console.error("Error fetching membership plans:", error);
      showSnackbar(error.message, "error");
    }
  };

  useEffect(() => {
    fetchMembershipPlans();
  }, []);

  const handleBarcodeChange = (event) => {
    setBarcode(event.target.value);
  };

  const handleBarcodeKeyDown = async (event) => {
    if (event.key === "Enter") {
      try {
        const response = await getActiveMembershipByBarcode(barcode);
        if (response.responseCode === 1000) {
          const data = response.data.activeMembership;

          // Set student details
          setStudent({
            id: data.member_id, // Add member ID for API call
            name: data.member_name,
            profileImage: data.profile_photo,
          });

          // Check if there is an active membership
          if (data.is_active_membership) {
            setActiveMembership({
              planName: data.membership_plan,
              price: data.membership_plan_price,
              duration: data.membership_duration,
            });
          } else {
            setActiveMembership(null); // No active membership
          }
        } else {
          showSnackbar(response.message, "error");
        }
      } catch (error) {
        console.error("Error fetching membership details:", error);
        showSnackbar(error.message, "error");
      }
    }
  };

  const handlePlanChange = (event) => {
    setSelectedPlan(event.target.value);
  };

  const handleSave = async () => {
    if (!student || !selectedPlan) {
      showSnackbar(
        "Please select a membership plan or ensure student details are loaded.",
        "error"
      );
      return;
    }

    try {
      const response = await assignMembershipPlan(student.id, selectedPlan);
      console.log("Response code:", response.responseCode);
      if (parseInt(response.responseCode) === 1000) {
        showSnackbar("Membership plan assigned successfully!", "success");
        setSelectedPlan("");
        setStudent(null);
        setActiveMembership(null);
        setBarcode("");
      } else {
        showSnackbar(
          response.message || "Failed to assign membership plan.",
          "error"
        );
      }
    } catch (error) {
      console.error("Error assigning membership plan:", error);
      showSnackbar(
        "An error occurred while assigning the membership plan.",
        "error"
      );
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbar({ ...snackbar, open: false });
  };

  const profileImageUrl = useMemo(() => {
    const VITE_BASE_URL = import.meta.env.VITE_BASE_URL;
    return (
      student?.profileImage &&
      `${VITE_BASE_URL}/api/member/member-images/${student?.profileImage}`
    );
  }, [student]);

  return (
    <Box
      sx={{
        margin: "0 auto",
        padding: "20px",
        backgroundColor: "#fff",
        borderRadius: "10px",
        boxShadow: "0px 3px 6px rgba(0,0,0,0.1)",
      }}
    >
      <Typography variant="h5" sx={{ mb: 4, fontWeight: 600, color: "#333" }}>
        Assign Membership
      </Typography>

      {/* Barcode Input */}
      <ReusableTextField
        label="Scan or Enter Barcode"
        value={barcode}
        onChange={handleBarcodeChange}
        onKeyDown={handleBarcodeKeyDown}
        fullWidth
        sx={{ mb: 4 }}
      />

      {/* Display Student Info */}
      {student && (
        <Paper
          elevation={1}
          sx={{
            p: 3,
            mb: 4,
            borderRadius: 2,
            display: "flex",
            alignItems: "center",
            gap: 3,
            backgroundColor: "#f8f9fa",
          }}
        >
          <Avatar
            src={profileImageUrl}
            alt={student.name}
            sx={{
              width: 80,
              height: 80,
              bgcolor: "#F4F4F4",
              fontSize: "2rem",
              boxShadow: "0px 3px 8px rgba(0,0,0,0.1)",
            }}
          >
            {student.name.charAt(0)}
          </Avatar>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {student.name}
          </Typography>
        </Paper>
      )}

      {/* Active Membership Details */}
      {activeMembership ? (
        <Paper
          elevation={1}
          sx={{
            p: 3,
            mb: 4,
            borderRadius: 2,
            backgroundColor: "#f8f9fa",
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
            Active Membership Plan
          </Typography>
          <Typography fontSize={"14px"}>
            Plan Name: {activeMembership.planName}
          </Typography>
          <Typography fontSize={"14px"}>
            Price: Rs. {activeMembership.price}
          </Typography>
          <Typography fontSize={"14px"}>
            Duration: {activeMembership.duration} Months
          </Typography>
          <Typography
            sx={{
              mt: 2,
              fontWeight: 500,
              fontSize: "0.9rem",
              color: "red",
              textAlign: "center",
            }}
          >
            You have Active Membership Plan already
          </Typography>
        </Paper>
      ) : (
        /* Membership Plan Selection */
        student && (
          <Box sx={{ mb: 4 }}>
            <FormControl fullWidth variant="outlined">
              <InputLabel id="membership-plan-label">
                Membership Plan
              </InputLabel>
              <Select
                labelId="membership-plan-label"
                id="membership-plan"
                value={selectedPlan}
                onChange={handlePlanChange}
                label="Membership Plan"
                sx={{ borderRadius: 1 }}
              >
                <MenuItem value="">Select Plan</MenuItem>
                {membershipPlans.map((plan) => (
                  <MenuItem key={plan.id} value={plan.id}>
                    {plan.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        )
      )}

      {/* Save Button */}
      {!activeMembership && student && (
        <Button
          variant="contained"
          color="primary"
          fullWidth
          onClick={handleSave}
          sx={{
            fontWeight: 600,
            py: 1.5,
            borderRadius: 1.5,
            backgroundColor: "#000",
            "&:hover": {
              backgroundColor: "#333",
            },
          }}
        >
          Save
        </Button>
      )}

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}
