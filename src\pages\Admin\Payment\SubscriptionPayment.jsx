import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Typography,
  Chip,
  Stack,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
  CircularProgress,
  Alert,
} from "@mui/material";
import { Visibility, Delete, Search } from "@mui/icons-material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import PaymentViewDialog from "../../Admin/Payment/ViewSubcriptionPayment";
import { getAllSubcriptionPayment } from "../../../app/service/payment.service";

const AllSubscriptionPaymentPage = () => {
  const [payments, setPayments] = useState([]);
  const [filteredPayments, setFilteredPayments] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [subscriptionFilter, setSubscriptionFilter] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);

  // Loading and error states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  const fetchPayments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getAllSubcriptionPayment();
      console.log("API Response:", response);

      if (
        response.responseCode === 1000 &&
        response.data &&
        response.data.customerSubcsriptionPayments
      ) {
        const paymentArray = response.data.customerSubcsriptionPayments;
        console.log("Response Data:", response.data.customerSubcsriptionPayments);

        // Transform API data to match your existing UI structure
        const transformedPayments = paymentArray.map((payment) => ({
          id: payment.id,
          customerName: payment.customerName || "N/A",
          subscriptionPlan: payment.subscriptionPlan || "N/A",
          amount: payment.paymentAmount || 0,
          paymentDate: payment.paymentDate || new Date().toISOString(),
          paymentStatus: payment.status || "Pending",
          originalData: payment, // Keep original data for reference
        }));

        setPayments(transformedPayments);
        setFilteredPayments(transformedPayments);
      } else {
        setPayments([]);
        setFilteredPayments([]);
        setError("No payments available");
      }
    } catch (error) {
      setError("Failed to fetch payments. Please try again.");
      setPayments([]);
      setFilteredPayments([]);
      console.error("Error fetching payments:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch payments on component mount
  useEffect(() => {
    fetchPayments();
  }, [fetchPayments]);

  // Filter payments based on search term, date range, and subscription plan
  useEffect(() => {
    let filtered = payments;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (payment) =>
          payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          payment.subscriptionPlan.toLowerCase().includes(searchTerm.toLowerCase()) ||
          payment.amount.toString().includes(searchTerm) ||
          payment.paymentStatus.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Date range filter
    if (startDate || endDate) {
      filtered = filtered.filter((payment) => {
        const paymentDate = new Date(payment.paymentDate);
        const start = startDate ? new Date(startDate) : new Date("1900-01-01");
        const end = endDate ? new Date(endDate) : new Date("2100-12-31");
        return paymentDate >= start && paymentDate <= end;
      });
    }

    // Subscription plan filter
    if (subscriptionFilter) {
      filtered = filtered.filter((payment) => 
        payment.subscriptionPlan === subscriptionFilter
      );
    }

    setFilteredPayments(filtered);
    setPage(0); // Reset page when filtering
  }, [searchTerm, startDate, endDate, subscriptionFilter, payments]);

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleView = (paymentId) => {
    const payment = payments.find(p => p.id === paymentId);
    setSelectedPayment(payment);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedPayment(null);
  };

  const handleDelete = (paymentId) => {
    console.log("Delete payment:", paymentId);
    // Add delete functionality here
  };

  const getSubscriptionColor = (plan) => {
    switch (plan) {
      case "01 Month":
      case "1 Month":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      case "06 Month":
      case "6 Month":
        return {
          backgroundColor: "#F3E5F5",
          color: "#7B1FA2",
          borderColor: "#CE93D8",
        };
      case "12 Month" :
        return {
          backgroundColor: "#E0F2F1",
          color: "#00796B",
          borderColor: "#80CBC4",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case "SUCCESS":
        return {
          backgroundColor: "#E8F5E8",
          color: "#2E7D32",
          borderColor: "#A5D6A7",
        };
      case "PENDING":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFCC02",
        };
      case "Failed":
        return {
          backgroundColor: "#FFEBEE",
          color: "#C62828",
          borderColor: "#EF9A9A",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      })
    );
  };

  const getSubscriptionPlans = () => {
    const plans = [...new Set(payments.map(payment => payment.subscriptionPlan))];
    return plans;
  };

  const formatCurrency = (amount) => {
    return `Rs ${amount.toFixed(2)}`;
  };

  // Calculate total pages
  const totalPages = Math.ceil(filteredPayments.length / rowsPerPage);

  // Mobile Card View Component
  const PaymentCard = ({ payment }) => (
    <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
            {payment.customerName}
          </Typography>
          <Chip
            label={payment.paymentStatus}
            size="small"
            sx={{
              ...getPaymentStatusColor(payment.paymentStatus),
              border: "1px solid",
              fontWeight: "500",
              fontSize: "11px",
            }}
          />
        </Box>
        
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Payment Date:</strong> {formatDateTime(payment.paymentDate)}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Subscription Plan:</strong>
          <Chip
            label={payment.subscriptionPlan}
            size="small"
            sx={{
              ...getSubscriptionColor(payment.subscriptionPlan),
              border: "1px solid",
              fontWeight: "500",
              fontSize: "10px",
              ml: 1,
            }}
          />
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
          <strong>Amount:</strong> {formatCurrency(payment.amount)}
        </Typography>

        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <IconButton
            onClick={() => handleView(payment.id)}
            sx={{
              color: "#3a86ff",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Visibility sx={{ fontSize: 18 }} />
          </IconButton>
          {/* <IconButton
            onClick={() => handleDelete(payment.id)}
            sx={{
              color: "#d00000",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Delete sx={{ fontSize: 18 }} />
          </IconButton> */}
        </Stack>
      </CardContent>
    </Card>
  );

  // Loading component
  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Container>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="xl" sx={{ py: 1 }}>
        <Typography
          variant="h4"
          sx={{ mb: 3, color: "#333", fontWeight: "600", fontSize: isMobile ? '24px' : '32px' }}
        >
          All Subscription Payments
        </Typography>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Search and Filters Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            p: 2,
            mb: 2,
          }}
        >
          <Grid container spacing={2}>
            {/* Search Bar */}
            <Grid item xs={12} md={6} lg={4}>
              <TextField
                fullWidth
                placeholder="Search payments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ color: '#666', mr: 1 }} />,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E0E0E0',
                    },
                  },
                }}
              />
            </Grid>

            {/* Start Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E0E0E0',
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* End Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E0E0E0',
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* Subscription Plan Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <FormControl fullWidth>
                <InputLabel>Subscription Plan</InputLabel>
                <Select
                  value={subscriptionFilter}
                  label="Subscription Plan"
                  onChange={(e) => setSubscriptionFilter(e.target.value)}
                  sx={{
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#E0E0E0',
                    },
                  }}
                >
                  <MenuItem value="">All Plans</MenuItem>
                  {getSubscriptionPlans().map((plan) => (
                    <MenuItem key={plan} value={plan}>
                      {plan}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Clear Filters Button */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm("");
                  setStartDate(null);
                  setEndDate(null);
                  setSubscriptionFilter("");
                }}
                sx={{
                  height: '48px',
                  borderColor: '#E0E0E0',
                  color: '#666',
                  '&:hover': {
                    borderColor: '#1976D2',
                    backgroundColor: '#F5F5F5',
                  },
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Table Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            overflow: "hidden",
          }}
        >
          {/* Desktop/Tablet Table View */}
          {!isMobile ? (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Payment Date
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Subscription Plan
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Amount
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Customer Name
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Payment Status
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredPayments
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((payment, index) => (
                        <TableRow
                          key={payment.id}
                          hover
                          sx={{
                            backgroundColor: "#fff",
                            "&:hover": {
                              backgroundColor: "#F8F9FA",
                            },
                            borderBottom: "1px solid #F0F0F0",
                          }}
                        >
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "400",
                            }}
                          >
                            {formatDateTime(payment.paymentDate)}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={payment.subscriptionPlan}
                              size="small"
                              sx={{
                                ...getSubscriptionColor(payment.subscriptionPlan),
                                border: "1px solid",
                                fontWeight: "500",
                                fontSize: "11px",
                              }}
                            />
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "500",
                            }}
                          >
                            {formatCurrency(payment.amount)}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "500",
                            }}
                          >
                            {payment.customerName}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={payment.paymentStatus}
                              size="small"
                              sx={{
                                ...getPaymentStatusColor(payment.paymentStatus),
                                border: "1px solid",
                                fontWeight: "500",
                                fontSize: "11px",
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={0.5}>
                              <IconButton
                                onClick={() => handleView(payment.id)}
                                sx={{
                                  color: "#3a86ff",
                                  "&:hover": { backgroundColor: "#F5F5F5" },
                                  padding: "4px",
                                }}
                                size="small"
                              >
                                <Visibility sx={{ fontSize: 16 }} />
                              </IconButton>
                              {/* <IconButton
                                onClick={() => handleDelete(payment.id)}
                                sx={{
                                  color: "#d00000",
                                  "&:hover": { backgroundColor: "#F5F5F5" },
                                  padding: "4px",
                                }}
                                size="small"
                              >
                                <Delete sx={{ fontSize: 16 }} />
                              </IconButton> */}
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))}
                    {filteredPayments.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={6} sx={{ textAlign: "center", py: 4 }}>
                          <Typography variant="body1" color="textSecondary">
                            No payments found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          ) : (
            /* Mobile Card View */
            <Box sx={{ p: 2 }}>
              {filteredPayments
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((payment) => (
                  <PaymentCard key={payment.id} payment={payment} />
                ))}
              {filteredPayments.length === 0 && (
                <Box sx={{ textAlign: "center", py: 4 }}>
                  <Typography variant="body1" color="textSecondary">
                    No payments found
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {/* Custom Pagination */}
          {filteredPayments.length > 0 && (
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                p: 2,
                borderTop: "1px solid #E0E0E0",
                backgroundColor: "#FAFAFA",
                flexDirection: isMobile ? 'column' : 'row',
                gap: isMobile ? 2 : 0,
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
                {Array.from(
                  { length: Math.min(4, totalPages) },
                  (_, i) => i + 1
                ).map((pageNum) => (
                  <Button
                    key={pageNum}
                    variant={page + 1 === pageNum ? "contained" : "text"}
                    onClick={() => setPage(pageNum - 1)}
                    sx={{
                      minWidth: 32,
                      height: 32,
                      borderRadius: 1,
                      fontSize: "14px",
                      ...(page + 1 === pageNum
                        ? {
                            backgroundColor: "#1976D2",
                            color: "white",
                            "&:hover": {
                              backgroundColor: "#1565C0",
                            },
                          }
                        : {
                            color: "#666",
                            "&:hover": {
                              backgroundColor: "#F5F5F5",
                            },
                          }),
                    }}
                  >
                    {pageNum}
                  </Button>
                ))}
                {totalPages > 4 && (
                  <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
                    ...
                  </Typography>
                )}
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
                <Typography variant="body2" sx={{ color: "#666" }}>
                  Go to page
                </Typography>
                <TextField
                  size="small"
                  type="number"
                  value={page + 1}
                  onChange={(e) =>
                    setPage(
                      Math.max(
                        0,
                        Math.min(totalPages - 1, Number(e.target.value) - 1)
                      )
                    )
                  }
                  sx={{
                    width: 60,
                    "& .MuiOutlinedInput-root": {
                      height: 32,
                      "& fieldset": {
                        borderColor: "#E0E0E0",
                      },
                    },
                  }}
                  inputProps={{
                    min: 1,
                    max: totalPages,
                  }}
                />
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography variant="body2" color="#666">
                    Show
                  </Typography>
                  <FormControl size="small" sx={{ minWidth: 80 }}>
                    <Select
                      value={rowsPerPage}
                      onChange={handleChangeRowsPerPage}
                      displayEmpty
                      sx={{
                        backgroundColor: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E0E0E0",
                        },
                      }}
                    >
                      <MenuItem value={5}>5 Row</MenuItem>
                      <MenuItem value={8}>8 Row</MenuItem>
                      <MenuItem value={10}>10 Row</MenuItem>
                      <MenuItem value={25}>25 Row</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Box>
            </Box>
          )}
        </Paper>

        {/* Payment View Dialog */}
        <PaymentViewDialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          payment={selectedPayment}
        />
      </Container>
    </LocalizationProvider>
  );
};

export default AllSubscriptionPaymentPage;