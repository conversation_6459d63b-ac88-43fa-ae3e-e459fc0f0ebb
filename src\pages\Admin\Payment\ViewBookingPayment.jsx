import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Grid,
  Divider,
  Chip,
  Card,
  CardContent,
  IconButton,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { Close, Person, Payment, CalendarToday } from "@mui/icons-material";
import RestaurantMenuIcon from '@mui/icons-material/RestaurantMenu';

const BookingPaymentDetailsDialog = ({ open, onClose, payment }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (!payment) return null;

  const getStatusColor = (status) => {
    switch (status) {
      case "Completed":
        return {
          backgroundColor: "#E8F5E8",
          color: "#2E7D32",
          borderColor: "#A5D6A7",
        };
      case "Pending":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFCC02",
        };
      case "Failed":
        return {
          backgroundColor: "#FFEBEE",
          color: "#D32F2F",
          borderColor: "#EF9A9A",
        };
      case "Refunded":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }),
      time: date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }),
    };
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const { date, time } = formatDateTime(payment.paymentDate);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      fullScreen={isMobile}
      PaperProps={{
        sx: {
          borderRadius: isMobile ? 0 : 2,
          maxHeight: isMobile ? "100vh" : "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          pb: 1,
          borderBottom: "1px solid #E0E0E0",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h5" sx={{ fontWeight: "600", color: "#333" }}>
          Payment Details
        </Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3 }}>
          {/* Payment Summary */}
          <Card sx={{ mb: 3, border: "1px solid #E0E0E0" }}>
            <CardContent>
              <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
                <Typography variant="h4" sx={{ fontWeight: "700", color: "#1976D2" }}>
                  {formatCurrency(payment.amount)}
                </Typography>
                <Chip
                  label={payment.paymentStatus}
                  sx={{
                    ...getStatusColor(payment.paymentStatus),
                    border: "1px solid",
                    fontWeight: "600",
                    fontSize: "14px",
                    height: "32px",
                  }}
                />
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <CalendarToday sx={{ fontSize: 16, color: "#666" }} />
                <Typography variant="body1" sx={{ color: "#666" }}>
                  {date} at {time}
                </Typography>
              </Box>
            </CardContent>
          </Card>

          <Grid container spacing={3}>
            {/* Customer Information */}
            <Grid item xs={12} md={6}>
              <Card sx={{ height: "100%", border: "1px solid #E0E0E0" }}>
                <CardContent>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                    <Person sx={{ mr: 1, color: "#1976D2" }} />
                    <Typography variant="h6" sx={{ fontWeight: "600", color: "#333" }}>
                      Customer Information
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Box sx={{ space: 2 }}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                        Name
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: "500", color: "#333" }}>
                        {payment.customer[0].name}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                        Email
                      </Typography>
                      <Typography variant="body1" sx={{ color: "#333" }}>
                        {payment.customer[0].email}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                        Phone
                      </Typography>
                      <Typography variant="body1" sx={{ color: "#333" }}>
                        {payment.customer[0].phone}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Chef Information */}
            <Grid item xs={12} md={6}>
              <Card sx={{ height: "100%", border: "1px solid #E0E0E0" }}>
                <CardContent>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                    <RestaurantMenuIcon sx={{ mr: 1, color: "#1976D2" }} />
                    <Typography variant="h6" sx={{ fontWeight: "600", color: "#333" }}>
                      Chef Information
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Box sx={{ space: 2 }}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                        Name
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: "500", color: "#333" }}>
                        {payment.chef[0].name}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                        Email
                      </Typography>
                      <Typography variant="body1" sx={{ color: "#333" }}>
                        {payment.chef[0].email}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                        Phone
                      </Typography>
                      <Typography variant="body1" sx={{ color: "#333" }}>
                        {payment.chef[0].phone}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                        Hourly Rate
                      </Typography>
                      <Typography variant="body1" sx={{ color: "#333" }}>
                        ${payment.chef[0].hourlyRate}/hour
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                        Location
                      </Typography>
                      <Typography variant="body1" sx={{ color: "#333" }}>
                        {payment.chef[0].location}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Payment Information */}
          <Card sx={{ mt: 3, border: "1px solid #E0E0E0" }}>
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <Payment sx={{ mr: 1, color: "#1976D2" }} />
                <Typography variant="h6" sx={{ fontWeight: "600", color: "#333" }}>
                  Payment Information
                </Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                      Payment ID
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: "500", color: "#333" }}>
                      #{payment.id.toString().padStart(6, '0')}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                      Amount
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: "500", color: "#333" }}>
                      {formatCurrency(payment.amount)}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                      Payment Date
                    </Typography>
                    <Typography variant="body1" sx={{ color: "#333" }}>
                      {date}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ color: "#666", mb: 0.5 }}>
                      Payment Time
                    </Typography>
                    <Typography variant="body1" sx={{ color: "#333" }}>
                      {time}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          p: 3,
          borderTop: "1px solid #E0E0E0",
          justifyContent: "flex-end",
        }}
      >
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            borderColor: "#E0E0E0",
            color: "#666",
            "&:hover": {
              borderColor: "#1976D2",
              backgroundColor: "#F5F5F5",
            },
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BookingPaymentDetailsDialog;