import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Divider,
  Grid,
  Card,
  CardContent,
  useMediaQuery,
  useTheme,
  IconButton,
} from "@mui/material";
import { Close, Person, Email, Phone, Payment, CalendarToday } from "@mui/icons-material";

const PaymentViewDialog = ({ open, onClose, payment }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

  if (!payment) return null;

  const getSubscriptionColor = (plan) => {
    switch (plan) {
      case "1 Month":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      case "3 Month":
        return {
          backgroundColor: "#F3E5F5",
          color: "#7B1FA2",
          borderColor: "#CE93D8",
        };
      case "6 Month":
        return {
          backgroundColor: "#E0F2F1",
          color: "#00796B",
          borderColor: "#80CBC4",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case "Completed":
        return {
          backgroundColor: "#E8F5E8",
          color: "#2E7D32",
          borderColor: "#A5D6A7",
        };
      case "Pending":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFCC02",
        };
      case "Failed":
        return {
          backgroundColor: "#FFEBEE",
          color: "#C62828",
          borderColor: "#EF9A9A",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }),
      time: date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }),
    };
  };

  const formatCurrency = (amount) => {
    return `$${amount.toFixed(2)}`;
  };

  const dateTime = formatDateTime(payment.paymentDate);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen={fullScreen}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: fullScreen ? 0 : 3,
          minHeight: fullScreen ? "100vh" : "auto",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 1,
          borderBottom: "1px solid #E0E0E0",
          backgroundColor: "#FAFAFA",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            fontWeight: "600",
            color: "#333",
            fontSize: isMobile ? "18px" : "24px",
          }}
        >
          Payment Details
        </Typography>
        <IconButton
          onClick={onClose}
          sx={{
            color: "#666",
            "&:hover": { backgroundColor: "#F5F5F5" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3 }}>
          {/* Payment Status and Amount Section */}
          <Card
            sx={{
              mb: 3,
              border: "1px solid #E0E0E0",
              borderRadius: 2,
              backgroundColor: "#FAFAFA",
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  flexDirection: isMobile ? "column" : "row",
                  gap: 2,
                }}
              >
                <Box sx={{ textAlign: isMobile ? "center" : "left" }}>
                  <Typography
                    variant="h3"
                    sx={{
                      fontWeight: "700",
                      color: "#1976D2",
                      fontSize: isMobile ? "28px" : "36px",
                      mb: 1,
                    }}
                  >
                    {formatCurrency(payment.amount)}
                  </Typography>
                  <Chip
                    label={payment.subscriptionPlan}
                    sx={{
                      ...getSubscriptionColor(payment.subscriptionPlan),
                      border: "1px solid",
                      fontWeight: "600",
                      fontSize: "12px",
                      px: 1,
                    }}
                  />
                </Box>
                <Chip
                  label={payment.paymentStatus}
                  size="large"
                  sx={{
                    ...getPaymentStatusColor(payment.paymentStatus),
                    border: "1px solid",
                    fontWeight: "600",
                    fontSize: "14px",
                    px: 2,
                    py: 1,
                    height: "auto",
                  }}
                />
              </Box>
            </CardContent>
          </Card>

          {/* Customer Information */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: 2,
                  height: "100%",
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      mb: 2,
                    }}
                  >
                    <Person
                      sx={{
                        color: "#1976D2",
                        mr: 1,
                        fontSize: 20,
                      }}
                    />
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: "600",
                        color: "#333",
                        fontSize: "16px",
                      }}
                    >
                      Customer Information
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#666",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        mb: 0.5,
                      }}
                    >
                      Full Name
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: "#333",
                        fontWeight: "500",
                        fontSize: "15px",
                      }}
                    >
                      {payment.customerName}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#666",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        mb: 0.5,
                      }}
                    >
                      Email Address
                    </Typography>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <Email sx={{ color: "#666", mr: 1, fontSize: 16 }} />
                      <Typography
                        variant="body1"
                        sx={{
                          color: "#1976D2",
                          fontSize: "14px",
                          wordBreak: "break-all",
                        }}
                      >
                        {payment.email}
                      </Typography>
                    </Box>
                  </Box>

                  <Box>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#666",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        mb: 0.5,
                      }}
                    >
                      Phone Number
                    </Typography>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <Phone sx={{ color: "#666", mr: 1, fontSize: 16 }} />
                      <Typography
                        variant="body1"
                        sx={{
                          color: "#333",
                          fontWeight: "500",
                          fontSize: "14px",
                        }}
                      >
                        {payment.phoneNumber}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: 2,
                  height: "100%",
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      mb: 2,
                    }}
                  >
                    <Payment
                      sx={{
                        color: "#1976D2",
                        mr: 1,
                        fontSize: 20,
                      }}
                    />
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: "600",
                        color: "#333",
                        fontSize: "16px",
                      }}
                    >
                      Payment Information
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />

                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#666",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        mb: 0.5,
                      }}
                    >
                      Payment ID
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: "#333",
                        fontWeight: "500",
                        fontSize: "15px",
                      }}
                    >
                      #{payment.id.toString().padStart(6, '0')}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#666",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        mb: 0.5,
                      }}
                    >
                      Payment Date
                    </Typography>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <CalendarToday sx={{ color: "#666", mr: 1, fontSize: 16 }} />
                      <Typography
                        variant="body1"
                        sx={{
                          color: "#333",
                          fontWeight: "500",
                          fontSize: "14px",
                        }}
                      >
                        {dateTime.date}
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#666",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        mb: 0.5,
                      }}
                    >
                      Payment Time
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: "#333",
                        fontWeight: "500",
                        fontSize: "14px",
                      }}
                    >
                      {dateTime.time}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#666",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        mb: 0.5,
                      }}
                    >
                      Amount Paid
                    </Typography>
                    <Typography
                      variant="h5"
                      sx={{
                        color: "#2E7D32",
                        fontWeight: "700",
                        fontSize: "20px",
                      }}
                    >
                      {formatCurrency(payment.amount)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          px: 3,
          py: 2,
          borderTop: "1px solid #E0E0E0",
          backgroundColor: "#FAFAFA",
          justifyContent: "center",
        }}
      >
        <Button
          onClick={onClose}
          variant="contained"
          sx={{
            px: 4,
            py: 1,
            borderRadius: 2,
            textTransform: "none",
            fontWeight: "600",
            fontSize: "14px",
            backgroundColor: "#1976D2",
            "&:hover": {
              backgroundColor: "#1565C0",
            },
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentViewDialog;