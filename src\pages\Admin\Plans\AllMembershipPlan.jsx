import React, { useState, useEffect } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Typography,
  Chip,
  Stack,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  CircularProgress,
  Alert,
} from "@mui/material";
import { Edit, Delete, Search, Warning, Refresh } from "@mui/icons-material";
import EditMembershipPlanDialog from "../../Admin/Plans/EditMembershipPlan"; // Import the dialog component
import { useCallback } from "react";
import { getAllSubscriptions } from "../../../app/service/subscription.service";

const AllMembershipPlansPage = () => {
  const [plans, setPlans] = useState([]);
  const [filteredPlans, setFilteredPlans] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);
  
  // Dialog state
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  
  // Delete confirmation dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [planToDelete, setPlanToDelete] = useState(null);

  // Loading and error states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  const fetchSubscriptions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getAllSubscriptions();

      if (response.responseCode === 1000 && response.data) {
        // Transform API data to match your existing UI structure
        const transformedPlans = response.data.map((plan) => ({
          id: plan.id,
          planName: plan.plan_name,
          price: parseFloat(plan.plan_amount),
          duration: `${plan.plan_duration} Month${plan.plan_duration > 1 ? "s" : ""}`,
          // Add any other fields you might need
          originalData: plan // Keep original data for reference
        }));
        
        setPlans(transformedPlans);
        setFilteredPlans(transformedPlans);
      } else {
        setPlans([]);
        setFilteredPlans([]);
        setError("No subscription plans available");
      }
    } catch (error) {
      setError("Failed to fetch subscription plans. Please try again.");
      setPlans([]);
      setFilteredPlans([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch subscriptions on component mount
  useEffect(() => {
    fetchSubscriptions();
  }, [fetchSubscriptions]);

  // Filter plans based on search term
  useEffect(() => {
    let filtered = plans;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (plan) =>
          plan.planName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          plan.duration.toLowerCase().includes(searchTerm.toLowerCase()) ||
          plan.price.toString().includes(searchTerm)
      );
    }

    setFilteredPlans(filtered);
    setPage(0); // Reset page when filtering
  }, [searchTerm, plans]);

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleEdit = (plan) => {
    console.log("Edit plan:", plan);
    setSelectedPlan(plan);
    setDialogOpen(true);
  };

  const handleDeleteClick = (plan) => {
    setPlanToDelete(plan);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (planToDelete) {
      console.log("Delete plan:", planToDelete.id);
      // Here you would typically call a delete API
      // For now, we'll just remove from local state
      const updatedPlans = plans.filter(plan => plan.id !== planToDelete.id);
      setPlans(updatedPlans);
      setFilteredPlans(updatedPlans);
    }
    setDeleteDialogOpen(false);
    setPlanToDelete(null);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setPlanToDelete(null);
  };

  const handleSavePlan = async (updatedPlan) => {
    console.log("Saving plan:", updatedPlan);
    
    // Update the plan in the plans array
    const updatedPlans = plans.map(plan => 
      plan.id === updatedPlan.id ? updatedPlan : plan
    );
    
    setPlans(updatedPlans);
    setFilteredPlans(updatedPlans);
    
    // Close the dialog
    setDialogOpen(false);
    setSelectedPlan(null);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedPlan(null);
  };

  const handleRefresh = () => {
    fetchSubscriptions();
  };

  const getDurationColor = (duration) => {
    switch (duration) {
      case "1 Month":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      case "3 Months":
        return {
          backgroundColor: "#F3E5F5",
          color: "#7B1FA2",
          borderColor: "#CE93D8",
        };
      case "6 Months":
        return {
          backgroundColor: "#E0F2F1",
          color: "#00796B",
          borderColor: "#80CBC4",
        };
      case "12 Months":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFCC02",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatPrice = (price) => {
    return `$${price.toFixed(2)}`;
  };

  // Calculate total pages
  const totalPages = Math.ceil(filteredPlans.length / rowsPerPage);

  // Mobile Card View Component
  const PlanCard = ({ plan }) => (
    <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
            {plan.planName}
          </Typography>
          <Chip
            label={plan.duration}
            size="small"
            sx={{
              ...getDurationColor(plan.duration),
              border: "1px solid",
              fontWeight: "500",
              fontSize: "11px",
            }}
          />
        </Box>
        
        <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
          <strong>Price:</strong> {formatPrice(plan.price)}
        </Typography>

        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <IconButton
            onClick={() => handleEdit(plan)}
            sx={{
              color: "#3a86ff",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Edit sx={{ fontSize: 18 }} />
          </IconButton>
          <IconButton
            onClick={() => handleDeleteClick(plan)}
            sx={{
              color: "#d00000",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Delete sx={{ fontSize: 18 }} />
          </IconButton>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="xl" sx={{ py: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography
          variant="h4"
          sx={{ color: "#333", fontWeight: "600", fontSize: isMobile ? '24px' : '32px' }}
        >
          All Membership Plans
        </Typography>
        
        <Button
          variant="outlined"
          onClick={handleRefresh}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={16} /> : <Refresh />}
          sx={{
            borderColor: '#E0E0E0',
            color: '#666',
            '&:hover': {
              borderColor: '#1976D2',
              backgroundColor: '#F5F5F5',
            },
          }}
        >
          {loading ? 'Loading...' : 'Refresh'}
        </Button>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 2 }}
          action={
            <Button color="inherit" size="small" onClick={handleRefresh}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Search Section */}
      <Paper
        elevation={1}
        sx={{
          border: "1px solid #E0E0E0",
          borderRadius: 2,
          p: 2,
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          {/* Search Bar */}
          <Grid item xs={12} md={6} lg={4}>
            <TextField
              fullWidth
              placeholder="Search membership plans..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              disabled={loading}
              InputProps={{
                startAdornment: <Search sx={{ color: '#666', mr: 1 }} />,
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: '#E0E0E0',
                  },
                },
              }}
            />
          </Grid>

          {/* Clear Search Button */}
          <Grid item xs={12} sm={6} md={3} lg={2}>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => setSearchTerm("")}
              disabled={loading || !searchTerm}
              sx={{
                height: '48px',
                borderColor: '#E0E0E0',
                color: '#666',
                '&:hover': {
                  borderColor: '#1976D2',
                  backgroundColor: '#F5F5F5',
                },
              }}
            >
              Clear Search
            </Button>
          </Grid>

          {/* Results count */}
          <Grid item xs={12} md={3} lg={6} sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body2" sx={{ color: '#666' }}>
              {loading ? 'Loading...' : `${filteredPlans.length} plan${filteredPlans.length !== 1 ? 's' : ''} found`}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Table Section */}
      <Paper
        elevation={1}
        sx={{
          border: "1px solid #E0E0E0",
          borderRadius: 2,
          overflow: "hidden",
        }}
      >
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
            <CircularProgress />
            <Typography variant="body1" sx={{ ml: 2, color: '#666' }}>
              Loading membership plans...
            </Typography>
          </Box>
        ) : (
          <>
            {/* Desktop/Tablet Table View */}
            {!isMobile ? (
              <>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
                        <TableCell
                          sx={{
                            color: "#666",
                            fontWeight: "600",
                            fontSize: "12px",
                            textTransform: "uppercase",
                            letterSpacing: "0.5px",
                            borderBottom: "1px solid #E0E0E0",
                          }}
                        >
                          Plan Name
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#666",
                            fontWeight: "600",
                            fontSize: "12px",
                            textTransform: "uppercase",
                            letterSpacing: "0.5px",
                            borderBottom: "1px solid #E0E0E0",
                          }}
                        >
                          Duration
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#666",
                            fontWeight: "600",
                            fontSize: "12px",
                            textTransform: "uppercase",
                            letterSpacing: "0.5px",
                            borderBottom: "1px solid #E0E0E0",
                          }}
                        >
                          Price
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#666",
                            fontWeight: "600",
                            fontSize: "12px",
                            textTransform: "uppercase",
                            letterSpacing: "0.5px",
                            borderBottom: "1px solid #E0E0E0",
                          }}
                        >
                          Actions
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredPlans
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((plan, index) => (
                          <TableRow
                            key={plan.id}
                            hover
                            sx={{
                              backgroundColor: "#fff",
                              "&:hover": {
                                backgroundColor: "#F8F9FA",
                              },
                              borderBottom: "1px solid #F0F0F0",
                            }}
                          >
                            <TableCell
                              sx={{
                                color: "#333",
                                fontSize: "13px",
                                fontWeight: "500",
                              }}
                            >
                              {plan.planName}
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={plan.duration}
                                size="small"
                                sx={{
                                  ...getDurationColor(plan.duration),
                                  border: "1px solid",
                                  fontWeight: "500",
                                  fontSize: "11px",
                                }}
                              />
                            </TableCell>
                            <TableCell
                              sx={{
                                color: "#333",
                                fontSize: "13px",
                                fontWeight: "600",
                              }}
                            >
                              {formatPrice(plan.price)}
                            </TableCell>
                            <TableCell>
                              <Stack direction="row" spacing={0.5}>
                                <IconButton
                                  onClick={() => handleEdit(plan)}
                                  sx={{
                                    color: "#3a86ff",
                                    "&:hover": { backgroundColor: "#F5F5F5" },
                                    padding: "4px",
                                  }}
                                  size="small"
                                >
                                  <Edit sx={{ fontSize: 16 }} />
                                </IconButton>
                                <IconButton
                                  onClick={() => handleDeleteClick(plan)}
                                  sx={{
                                    color: "#d00000",
                                    "&:hover": { backgroundColor: "#F5F5F5" },
                                    padding: "4px",
                                  }}
                                  size="small"
                                >
                                  <Delete sx={{ fontSize: 16 }} />
                                </IconButton>
                              </Stack>
                            </TableCell>
                          </TableRow>
                        ))}
                      {filteredPlans.length === 0 && !loading && (
                        <TableRow>
                          <TableCell colSpan={4} sx={{ textAlign: "center", py: 4 }}>
                            <Typography variant="body1" color="textSecondary">
                              {error ? "Failed to load membership plans" : "No membership plans found"}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            ) : (
              /* Mobile Card View */
              <Box sx={{ p: 2 }}>
                {filteredPlans
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((plan) => (
                    <PlanCard key={plan.id} plan={plan} />
                  ))}
                {filteredPlans.length === 0 && !loading && (
                  <Box sx={{ textAlign: "center", py: 4 }}>
                    <Typography variant="body1" color="textSecondary">
                      {error ? "Failed to load membership plans" : "No membership plans found"}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}
          </>
        )}

        {/* Custom Pagination */}
        {filteredPlans.length > 0 && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              p: 2,
              borderTop: "1px solid #E0E0E0",
              backgroundColor: "#FAFAFA",
              flexDirection: isMobile ? 'column' : 'row',
              gap: isMobile ? 2 : 0,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
              {Array.from({ length: Math.min(4, totalPages) }, (_, i) => i + 1).map((pageNum) => (
                <Button
                  key={pageNum}
                  variant={page + 1 === pageNum ? "contained" : "text"}
                  onClick={() => setPage(pageNum - 1)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: 1,
                    fontSize: "14px",
                    ...(page + 1 === pageNum
                      ? {
                          backgroundColor: "#1976D2",
                          color: "white",
                          "&:hover": {
                            backgroundColor: "#1565C0",
                          },
                        }
                      : {
                          color: "#666",
                          "&:hover": {
                            backgroundColor: "#F5F5F5",
                          },
                        }),
                  }}
                >
                  {pageNum}
                </Button>
              ))}
              {totalPages > 4 && (
                <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
                  ...
                </Typography>
              )}
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
              <Typography variant="body2" sx={{ color: "#666" }}>
                Go to page
              </Typography>
              <TextField
                size="small"
                type="number"
                value={page + 1}
                onChange={(e) =>
                  setPage(Math.max(0, Math.min(totalPages - 1, Number(e.target.value) - 1)))
                }
                sx={{
                  width: 60,
                  "& .MuiOutlinedInput-root": {
                    height: 32,
                    "& fieldset": {
                      borderColor: "#E0E0E0",
                    },
                  },
                }}
                inputProps={{
                  min: 1,
                  max: totalPages,
                }}
              />
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography variant="body2" color="#666">
                  Show
                </Typography>
                <FormControl size="small" sx={{ minWidth: 80 }}>
                  <Select
                    value={rowsPerPage}
                    onChange={handleChangeRowsPerPage}
                    displayEmpty
                    sx={{
                      backgroundColor: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E0E0E0",
                      },
                    }}
                  >
                    <MenuItem value={5}>5 Row</MenuItem>
                    <MenuItem value={8}>8 Row</MenuItem>
                    <MenuItem value={10}>10 Row</MenuItem>
                    <MenuItem value={25}>25 Row</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Box>
          </Box>
        )}
      </Paper>

      {/* Edit Dialog */}
      <EditMembershipPlanDialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        planData={selectedPlan}
        onSave={handleSavePlan}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            border: "1px solid #E0E0E0",
          }
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 1,
            color: "#d32f2f",
            fontWeight: "600",
            pb: 1,
          }}
        >
          <Warning sx={{ color: "#d32f2f" }} />
          Confirm Delete
        </DialogTitle>
        <DialogContent sx={{ pt: 1 }}>
          <DialogContentText sx={{ color: "#333", fontSize: "16px" }}>
            Are you sure you want to delete the membership plan{" "}
            <strong>"{planToDelete?.planName}"</strong>?
          </DialogContentText>
          <DialogContentText sx={{ color: "#666", mt: 1, fontSize: "14px" }}>
            This action cannot be undone. All data associated with this plan will be permanently removed.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 1, gap: 1 }}>
          <Button
            onClick={handleDeleteCancel}
            variant="outlined"
            sx={{
              borderColor: "#E0E0E0",
              color: "#666",
              "&:hover": {
                borderColor: "#1976D2",
                backgroundColor: "#F5F5F5",
              },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            variant="contained"
            sx={{
              backgroundColor: "#d32f2f",
              color: "white",
              "&:hover": {
                backgroundColor: "#b71c1c",
              },
            }}
          >
            Delete Plan
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AllMembershipPlansPage;