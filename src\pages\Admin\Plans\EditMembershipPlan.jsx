import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  Box,
  Typography,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Divider,
  Alert,
  InputAdornment,
  Snackbar,
} from "@mui/material";
import { Close, Save, Cancel, CheckCircle, Error } from "@mui/icons-material";

const EditMembershipPlanDialog = ({ open, onClose, planData, onSave }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));

  // Form state
  const [formData, setFormData] = useState({
    planName: "",
    duration: "",
    price: "",
  });

  // Validation state
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Snackbar state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success", // 'success' | 'error' | 'warning' | 'info'
  });

  // Initialize form data when planData changes
  useEffect(() => {
    if (planData) {
      setFormData({
        planName: planData.planName || "",
        duration: planData.duration || "",
        price: planData.price || "",
      });
      setErrors({});
    }
  }, [planData]);

  // Handle input changes
  const handleInputChange = (field) => (event) => {
    const value = event.target.value;
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.planName.trim()) {
      newErrors.planName = "Plan name is required";
    }

    if (!formData.duration) {
      newErrors.duration = "Duration is required";
    }

    if (
      !formData.price ||
      isNaN(formData.price) ||
      parseFloat(formData.price) <= 0
    ) {
      newErrors.price = "Valid price is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Show snackbar
  const showSnackbar = (message, severity = "success") => {
    setSnackbar({
      open: true,
      message,
      severity,
    });
  };

  // Handle snackbar close
  const handleSnackbarClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      showSnackbar("Please fix the errors before saving", "error");
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare the data for submission
      const updatedPlan = {
        ...planData,
        ...formData,
        price: parseFloat(formData.price),
      };

      // Call the onSave callback
      await onSave(updatedPlan);

      // Show success message
      showSnackbar("Membership plan updated successfully!", "success");

      // Close dialog on success after a short delay
      setTimeout(() => {
        onClose();
      }, 1500);

    } catch (error) {
      console.error("Error saving membership plan:", error);
      
      // Show error message
      const errorMessage = error.message || "Failed to update membership plan. Please try again.";
      showSnackbar(errorMessage, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({
        planName: "",
        duration: "",
        price: "",
      });
      setErrors({});
      setSnackbar({ open: false, message: "", severity: "success" });
      onClose();
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        fullScreen={isMobile}
        PaperProps={{
          sx: {
            borderRadius: isMobile ? 0 : 2,
            maxHeight: isMobile ? "100vh" : "90vh",
          },
        }}
      >
        {/* Dialog Header */}
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            borderBottom: "1px solid #E0E0E0",
            pb: 2,
            px: isMobile ? 2 : 3,
          }}
        >
          <Typography
            variant="h5"
            sx={{
              fontWeight: "600",
              color: "#333",
              fontSize: isMobile ? "20px" : "24px",
            }}
          >
            Edit Membership Plan
          </Typography>
          <IconButton
            onClick={handleClose}
            disabled={isSubmitting}
            sx={{
              color: "#666",
              "&:hover": { backgroundColor: "#F5F5F5" },
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>

        {/* Dialog Content */}
        <DialogContent
          sx={{
            px: isMobile ? 2 : 3,
            py: 3,
            backgroundColor: "#FAFAFA",
          }}
        >
          <Box component="form" noValidate>
            <Grid container spacing={3} mt={1}>

              {/* Plan Name */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Plan Name"
                  value={formData.planName}
                  onChange={handleInputChange("planName")}
                  error={!!errors.planName}
                  helperText={errors.planName}
                  disabled={isSubmitting}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      backgroundColor: "white",
                      "& fieldset": {
                        borderColor: "#E0E0E0",
                      },
                    },
                  }}
                />
              </Grid>

              {/* Duration */}
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.duration}>
                  <InputLabel>Duration</InputLabel>
                  <Select
                    value={formData.duration}
                    onChange={handleInputChange("duration")}
                    label="Duration"
                    disabled={isSubmitting}
                    sx={{
                      backgroundColor: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E0E0E0",
                      },
                    }}
                  >
                    <MenuItem value="1 Month">1 Month</MenuItem>
                    <MenuItem value="3 Months">3 Months</MenuItem>
                    <MenuItem value="6 Months">6 Months</MenuItem>
                    <MenuItem value="12 Months">12 Months</MenuItem>
                  </Select>
                  {errors.duration && (
                    <Typography
                      variant="caption"
                      color="error"
                      sx={{ mt: 0.5, mx: 1.75 }}
                    >
                      {errors.duration}
                    </Typography>
                  )}
                </FormControl>
              </Grid>

              {/* Price */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Price"
                  type="number"
                  value={formData.price}
                  onChange={handleInputChange("price")}
                  error={!!errors.price}
                  helperText={errors.price}
                  disabled={isSubmitting}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">$</InputAdornment>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      backgroundColor: "white",
                      "& fieldset": {
                        borderColor: "#E0E0E0",
                      },
                    },
                  }}
                />
              </Grid>

              {/* Alert for form errors */}
              {Object.keys(errors).length > 0 && (
                <Grid item xs={12}>
                  <Alert severity="error" sx={{ mt: 1 }}>
                    Please fix the errors above before saving.
                  </Alert>
                </Grid>
              )}
            </Grid>
          </Box>
        </DialogContent>

        {/* Dialog Actions */}
        <DialogActions
          sx={{
            borderTop: "1px solid #E0E0E0",
            px: isMobile ? 2 : 3,
            py: 2,
            backgroundColor: "white",
            flexDirection: isMobile ? "column" : "row",
            gap: isMobile ? 1 : 0,
          }}
        >
          <Button
            onClick={handleClose}
            disabled={isSubmitting}
            startIcon={<Cancel />}
            sx={{
              color: "#666",
              borderColor: "#E0E0E0",
              "&:hover": {
                borderColor: "#1976D2",
                backgroundColor: "#F5F5F5",
              },
              width: isMobile ? "100%" : "auto",
              order: isMobile ? 2 : 1,
            }}
            variant="outlined"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            startIcon={<Save />}
            variant="contained"
            sx={{
              backgroundColor: "#1976D2",
              color: "white",
              "&:hover": {
                backgroundColor: "#1565C0",
              },
              "&:disabled": {
                backgroundColor: "#CCCCCC",
              },
              width: isMobile ? "100%" : "auto",
              order: isMobile ? 1 : 2,
              ml: isMobile ? 0 : 1,
            }}
          >
            {isSubmitting ? "Saving..." : "Save Changes"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
          sx={{
            width: '100%',
            '& .MuiAlert-icon': {
              alignItems: 'center',
            },
          }}
          icon={
            snackbar.severity === 'success' ? <CheckCircle /> : 
            snackbar.severity === 'error' ? <Error /> : undefined
          }
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default EditMembershipPlanDialog;