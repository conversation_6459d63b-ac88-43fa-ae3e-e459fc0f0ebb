import React, { useState, useCallback } from "react";
import {
  Box,
  Paper,
  TextField,
  Typography,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Snackbar,
  Alert,
  InputAdornment,
} from "@mui/material";
import { Save, Cancel } from "@mui/icons-material";
import { saveSubscription } from "../../../app/service/subscription.service";
import { parse } from "date-fns";

const NewMembershipPlanPage = () => {
  const [formData, setFormData] = useState({
    planName: "",
    duration: "",
    price: "",
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success", // success, error, warning, info
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const isTablet = useMediaQuery(theme.breakpoints.down("lg"));

  const durationOptions = [
    { value: "1 Month", label: "1 Month" },
    { value: "3 Months", label: "3 Months" },
    { value: "6 Months", label: "6 Months" },
    { value: "12 Months", label: "12 Months" },
  ];

  const handleInputChange = (field) => (event) => {
    const value = event.target.value;
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateForm = useCallback(() => {
    const newErrors = {};

    if (!formData.planName.trim()) {
      newErrors.planName = "Membership plan name is required";
    } else if (formData.planName.trim().length < 3) {
      newErrors.planName = "Plan name must be at least 3 characters";
    }

    if (!formData.duration) {
      newErrors.duration = "Duration is required";
    }

    if (!formData.price.trim()) {
      newErrors.price = "Price is required";
    } else if (isNaN(formData.price) || parseFloat(formData.price) <= 0) {
      newErrors.price = "Please enter a valid price greater than 0";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData.planName, formData.duration, formData.price]);

  const handleSave = useCallback(async () => {
    if (validateForm()) {
      setLoading(true); // Add this line
      try {
        const payload = {
          subscriptionName: formData.planName,
          subscriptionPrice: parseFloat(formData.price),
          subscriptionDuration: parseInt(formData.duration),
        };

        const response = await saveSubscription(payload);
        console.log("response",response)
        if (parseInt(response.responseCode) === 1000) {
          console.log("Subscription saved successfully:", response);

          setSnackbar({
            open: true,
            message: "Membership plan created successfully!",
            severity: "success",
          });

          setTimeout(() => {
            setFormData({
              planName: "",
              duration: "",
              price: "",
            });
          }, 1000);
        } else if (parseInt(response.responseCode) === 1001) {
          console.error("Error saving subscription:", response.error);
          setSnackbar({
            open: true,
            message: "Failed to create membership plan. Please try again.",
            severity: "error",
          });
        }
      } catch (error) {
        console.error("Error saving subscription:", error);
        setSnackbar({
          open: true,
          message: "Failed to create membership plan. Please try again.",
          severity: "error",
        });
      } finally {
        setLoading(false); // Add this line
      }
    } else {
      setSnackbar({
        open: true,
        message: "Please fix the errors in the form",
        severity: "error",
      });
    }
  }, [formData, validateForm]);

  const handleCancel = () => {
    setFormData({
      planName: "",
      duration: "",
      price: "",
    });
    setErrors({});
    setSnackbar({
      open: true,
      message: "Form cleared",
      severity: "info",
    });
  };

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  const getDurationColor = (duration) => {
    switch (duration) {
      case "1 Month":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      case "3 Months":
        return {
          backgroundColor: "#F3E5F5",
          color: "#7B1FA2",
          borderColor: "#CE93D8",
        };
      case "6 Months":
        return {
          backgroundColor: "#E0F2F1",
          color: "#00796B",
          borderColor: "#80CBC4",
        };
      case "12 Months":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFCC02",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 1 }}>
      <Typography
        variant="h4"
        sx={{
          mb: 3,
          color: "#333",
          fontWeight: "600",
          fontSize: isMobile ? "24px" : "32px",
        }}
      >
        Create New Membership Plan
      </Typography>

      {/* Form Section */}
      <Paper
        elevation={1}
        sx={{
          border: "1px solid #E0E0E0",
          borderRadius: 2,
          p: isMobile ? 2 : 4,
          mb: 2,
        }}
      >
        <Grid container spacing={3}>
          {/* Plan Name Field */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Membership Plan Name"
              placeholder="Enter membership plan name"
              value={formData.planName}
              onChange={handleInputChange("planName")}
              error={!!errors.planName}
              helperText={errors.planName}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "#E0E0E0", // default
                  },
                  "&:hover fieldset": {
                    borderColor: "#1976D2", // hover
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#666", // active/focused
                  },
                },
                "& .MuiInputLabel-root": {
                  color: "#666", // default label color
                  fontSize: "14px",
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#666", // focused label color
                },
                "& .MuiInputLabel-root:hover": {
                  color: "#1976D2", // hover label color (optional)
                },
              }}
            />
          </Grid>

          {/* Duration Field */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Duration"
              placeholder="Enter duration"
              type="number"
              value={formData.duration}
              onChange={handleInputChange("duration")}
              error={!!errors.duration}
              helperText={errors.duration}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "#E0E0E0", // default
                  },
                  "&:hover fieldset": {
                    borderColor: "#1976D2", // hover
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#666", // active/focused
                  },
                },
                "& .MuiInputLabel-root": {
                  color: "#666", // default label color
                  fontSize: "14px",
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#666", // focused label color
                },
                "& .MuiInputLabel-root:hover": {
                  color: "#1976D2", // hover label color (optional)
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Price"
              placeholder="Enter price"
              type="number"
              value={formData.price}
              onChange={handleInputChange("price")}
              error={!!errors.price}
              helperText={errors.price}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">Rs</InputAdornment>
                ),
                inputProps: {
                  min: 0,
                  step: "0.01",
                },
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "#E0E0E0", // default
                  },
                  "&:hover fieldset": {
                    borderColor: "#1976D2", // hover
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#666", // active/focused
                  },
                },
                "& .MuiInputLabel-root": {
                  color: "#666", // default label color
                  fontSize: "14px",
                },
                "& .MuiInputLabel-root.Mui-focused": {
                  color: "#666", // focused label color
                },
                "& .MuiInputLabel-root:hover": {
                  color: "#1976D2", // hover label color (optional)
                },
              }}
            />
          </Grid>

          {/* Action Buttons */}
          <Grid item xs={12}>
            <Box
              sx={{
                display: "flex",
                gap: 2,
                justifyContent: isMobile ? "stretch" : "flex-start",
                flexDirection: isMobile ? "column" : "row",
                mt: 2,
              }}
            >
              <Button
                variant="contained"
                onClick={handleSave}
                startIcon={<Save />}
                disabled={loading} // Add this line
                sx={{
                  backgroundColor: "#1976D2",
                  color: "white",
                  fontWeight: "500",
                  px: 4,
                  py: 1.5,
                  fontSize: "14px",
                  textTransform: "none",
                  "&:hover": {
                    backgroundColor: "#1565C0",
                  },
                  "&:disabled": {
                    backgroundColor: "#E0E0E0",
                    color: "#999",
                  },
                }}
              >
                {loading ? "Saving..." : "Save Membership Plan"}{" "}
                {/* Update this line */}
              </Button>

              <Button
                variant="outlined"
                onClick={handleCancel}
                startIcon={<Cancel />}
                sx={{
                  borderColor: "#E0E0E0",
                  color: "#666",
                  fontWeight: "500",
                  px: 4,
                  py: 1.5,
                  fontSize: "14px",
                  textTransform: "none",
                  "&:hover": {
                    borderColor: "#1976D2",
                    backgroundColor: "#F5F5F5",
                  },
                }}
              >
                Cancel
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Preview Section */}
      {(formData.planName || formData.duration || formData.price) && (
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            p: isMobile ? 2 : 3,
            backgroundColor: "#FAFAFA",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              mb: 2,
              color: "#333",
              fontWeight: "500",
              fontSize: "16px",
            }}
          >
            Preview
          </Typography>

          <Box
            sx={{
              p: 2,
              backgroundColor: "white",
              borderRadius: 1,
              border: "1px solid #E0E0E0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "flex-start",
                mb: 1,
              }}
            >
              <Typography
                variant="body1"
                sx={{ fontWeight: "500", color: "#333" }}
              >
                {formData.planName || "Plan Name"}
              </Typography>
              {formData.duration && (
                <Box
                  sx={{
                    px: 1.5,
                    py: 0.5,
                    borderRadius: 1,
                    fontSize: "12px",
                    fontWeight: "500",
                    ...getDurationColor(formData.duration),
                    border: "1px solid",
                  }}
                >
                  {formData.duration}
                </Box>
              )}
            </Box>
            <Typography variant="body2" sx={{ color: "#666" }}>
              <strong>Price:</strong> Rs {formData.price || "0.00"}
            </Typography>
          </Box>
        </Paper>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default NewMembershipPlanPage;
