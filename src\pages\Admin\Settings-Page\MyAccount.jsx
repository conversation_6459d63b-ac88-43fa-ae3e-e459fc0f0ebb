import React, { useState } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Avatar,
  IconButton,
  Divider,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Visibility,
  VisibilityOff,
  Person,
  Security,
  Email,
  Phone,
  Lock,
  Shield
} from '@mui/icons-material';

export default function ProfilePage() {
  const [editMode, setEditMode] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });
  
  const [profileData, setProfileData] = useState({
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    username: 'johns<PERSON>',
    email: '<EMAIL>',
    userType: 'Admin',
    phone: '******-0123',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [originalData, setOriginalData] = useState({ ...profileData });

  const handleInputChange = (field) => (event) => {
    setProfileData({
      ...profileData,
      [field]: event.target.value
    });
  };

  const handleEdit = () => {
    setOriginalData({ ...profileData });
    setEditMode(true);
  };

  const handleCancel = () => {
    setProfileData({ ...originalData });
    setEditMode(false);
    setAlert({ show: false, message: '', severity: 'success' });
  };

  const handleSave = () => {
    // Basic validation
    if (!profileData.firstName || !profileData.lastName || !profileData.email) {
      setAlert({
        show: true,
        message: 'Please fill in all required fields.',
        severity: 'error'
      });
      return;
    }

    // Password validation if changing password
    if (profileData.newPassword || profileData.confirmPassword) {
      if (!profileData.currentPassword) {
        setAlert({
          show: true,
          message: 'Current password is required to change password.',
          severity: 'error'
        });
        return;
      }
      if (profileData.newPassword !== profileData.confirmPassword) {
        setAlert({
          show: true,
          message: 'New password and confirm password do not match.',
          severity: 'error'
        });
        return;
      }
      if (profileData.newPassword.length < 6) {
        setAlert({
          show: true,
          message: 'New password must be at least 6 characters long.',
          severity: 'error'
        });
        return;
      }
    }

    // Simulate save
    setEditMode(false);
    setAlert({
      show: true,
      message: 'Profile updated successfully!',
      severity: 'success'
    });

    // Clear password fields after successful save
    setProfileData({
      ...profileData,
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });

    setTimeout(() => {
      setAlert({ show: false, message: '', severity: 'success' });
    }, 3000);
  };

  const getInitials = (firstName, lastName) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#333' }}>
          My Profile
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
          Manage your personal information and security settings
        </Typography>
      </Box>

      {alert.show && (
        <Alert 
          severity={alert.severity} 
          sx={{ mb: 3 }}
          onClose={() => setAlert({ show: false, message: '', severity: 'success' })}
        >
          {alert.message}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Profile Information Card */}
        <Grid item xs={12} md={8}>
          <Card elevation={2}>
            <CardHeader
              avatar={
                <Avatar
                  sx={{
                    width: 80,
                    height: 80,
                    bgcolor: '#1976d2',
                    fontSize: '2rem',
                    fontWeight: 'bold'
                  }}
                >
                  {getInitials(profileData.firstName, profileData.lastName)}
                </Avatar>
              }
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography variant="h5" component="h2">
                    {profileData.firstName} {profileData.lastName}
                  </Typography>
                  <Chip 
                    label={profileData.userType} 
                    color="secondary" 
                    size="small"
                    icon={<Person />}
                  />
                </Box>
              }
              subheader={`@${profileData.username}`}
              action={
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {!editMode ? (
                    <Button
                      variant="contained"
                      startIcon={<EditIcon />}
                      onClick={handleEdit}
                      sx={{ minWidth: 120, backgroundColor: '#1976d2', color: '#fff', '&:hover': { backgroundColor: '#115293' } }}
                    >
                      Edit Profile
                    </Button>
                  ) : (
                    <>
                      <Button
                        variant="outlined"
                        startIcon={<CancelIcon />}
                        onClick={handleCancel}
                        sx={{ minWidth: 100, backgroundColor: '#f5f5f5', color: '#333', border: "none", '&:hover': { backgroundColor: '#ddd', border: "none" } }}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="contained"
                        startIcon={<SaveIcon />}
                        onClick={handleSave}
                        sx={{ minWidth: 100, backgroundColor: '#1976d2', color: '#fff',  '&:hover': { backgroundColor: '#115293' } }}
                      >
                        Save
                      </Button>
                    </>
                  )}
                </Box>
              }
              sx={{ pb: 2 }}
            />
            
            <CardContent sx={{ pt: 0 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="First Name"
                    value={profileData.firstName}
                    onChange={handleInputChange('firstName')}
                    disabled={!editMode}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Person color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    value={profileData.lastName}
                    onChange={handleInputChange('lastName')}
                    disabled={!editMode}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Person color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Username"
                    value={profileData.username}
                    onChange={handleInputChange('username')}
                    disabled={!editMode}
                    required
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth disabled={!editMode}>
                    <InputLabel>User Type</InputLabel>
                    <Select
                      value={profileData.userType}
                      label="User Type"
                      onChange={handleInputChange('userType')}
                    >
                      <MenuItem value="Admin">Admin</MenuItem>
                      <MenuItem value="Manager">Manager</MenuItem>
                      <MenuItem value="User">User</MenuItem>
                      <MenuItem value="Viewer">Viewer</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    value={profileData.email}
                    onChange={handleInputChange('email')}
                    disabled={!editMode}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Phone Number"
                    value={profileData.phone}
                    onChange={handleInputChange('phone')}
                    disabled={!editMode}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Phone color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Security Information Card */}
        <Grid item xs={12} md={4}>
          <Card elevation={2}>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Security color="primary" />
                  <Typography variant="h6">Security Information</Typography>
                </Box>
              }
            />
            <CardContent>
              {editMode && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                    Change Password
                  </Typography>
                  
                  <TextField
                    fullWidth
                    label="Current Password"
                    type={showCurrentPassword ? 'text' : 'password'}
                    value={profileData.currentPassword}
                    onChange={handleInputChange('currentPassword')}
                    sx={{ mb: 2 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                            edge="end"
                          >
                            {showCurrentPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                  
                  <TextField
                    fullWidth
                    label="New Password"
                    type={showNewPassword ? 'text' : 'password'}
                    value={profileData.newPassword}
                    onChange={handleInputChange('newPassword')}
                    sx={{ mb: 2 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            edge="end"
                          >
                            {showNewPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                  
                  <TextField
                    fullWidth
                    label="Confirm New Password"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={profileData.confirmPassword}
                    onChange={handleInputChange('confirmPassword')}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            edge="end"
                          >
                            {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </Box>
              )}
              
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}