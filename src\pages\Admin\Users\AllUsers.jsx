// import React, { useState, useEffect } from "react";
// import {
//   Box,
//   Paper,
//   Table,
//   TableBody,
//   TableCell,
//   TableContainer,
//   TableHead,
//   TableRow,
//   TextField,
//   IconButton,
//   Typography,
//   Chip,
//   Stack,
//   Container,
//   Grid,
//   Button,
//   Select,
//   MenuItem,
//   FormControl,
//   useMediaQuery,
//   useTheme,
//   Card,
//   CardContent,
// } from "@mui/material";
// import { Visibility, Delete, Search } from "@mui/icons-material";
// import { userData } from "../../../Dummydata/userData";
// import { useNavigate } from "react-router-dom";

// const AllUsersPage = () => {
//   const navigate = useNavigate();
//   const [users, setUsers] = useState([]);
//   const [filteredUsers, setFilteredUsers] = useState([]);
//   const [searchTerm, setSearchTerm] = useState("");
//   const [page, setPage] = useState(0);
//   const [rowsPerPage, setRowsPerPage] = useState(8);

//   const theme = useTheme();
//   const isMobile = useMediaQuery(theme.breakpoints.down('md'));
//   const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

//   // Load user data on component mount
//   useEffect(() => {
//     setUsers(userData);
//     setFilteredUsers(userData);
//   }, []);

//   // Filter users based on search term
//   useEffect(() => {
//     let filtered = users;

//     // Search filter
//     if (searchTerm) {
//       filtered = filtered.filter(
//         (user) =>
//           user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
//           user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
//           user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
//           user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
//           user.phoneNumber.includes(searchTerm) ||
//           user.role.toLowerCase().includes(searchTerm.toLowerCase())
//       );
//     }

//     setFilteredUsers(filtered);
//     setPage(0); // Reset page when filtering
//   }, [searchTerm, users]);

//   const handleChangeRowsPerPage = (event) => {
//     setRowsPerPage(parseInt(event.target.value, 10));
//     setPage(0);
//   };

//   const handleView = (userId) => {
//     // console.log("View user:", userId);
//     navigate(`/system/users/view/${userId}`);
//     // Add view functionality here
//   };

//   const handleDelete = (userId) => {
//     console.log("Delete user:", userId);
//     // Add delete functionality here
//   };

//   const getRoleColor = (role) => {
//     switch (role) {
//       case "Admin":
//         return {
//           backgroundColor: "#FFE8E8",
//           color: "#D32F2F",
//           borderColor: "#FFCDD2",
//         };
//       case "User":
//         return {
//           backgroundColor: "#E3F2FD",
//           color: "#1976D2",
//           borderColor: "#BBDEFB",
//         };
//       default:
//         return {
//           backgroundColor: "#F5F5F5",
//           color: "#666",
//           borderColor: "#E0E0E0",
//         };
//     }
//   };

//   const formatDateTime = (dateString) => {
//     const date = new Date(dateString);
//     return date.toLocaleDateString("en-US", {
//       year: "numeric",
//       month: "short",
//       day: "numeric",
//     });
//   };

//   const getFullName = (user) => {
//     return `${user.firstName} ${user.lastName}`;
//   };

//   // Mobile Card View Component
//   const UserCard = ({ user }) => (
//     <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
//       <CardContent sx={{ p: 2 }}>
//         <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
//           <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
//             {getFullName(user)}
//           </Typography>
//           <Chip
//             label={user.role}
//             size="small"
//             sx={{
//               ...getRoleColor(user.role),
//               border: "1px solid",
//               fontWeight: "500",
//               fontSize: "11px",
//             }}
//           />
//         </Box>
        
//         <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
//           <strong>Email:</strong> {user.email}
//         </Typography>
//         <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
//           <strong>Phone:</strong> {user.phoneNumber}
//         </Typography>
//         <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
//           <strong>Username:</strong> {user.username}
//         </Typography>

//         <Stack direction="row" spacing={1} justifyContent="flex-end">
//           <IconButton
//             onClick={() => handleView(user.id)}
//             sx={{
//               color: "#3a86ff",
//               "&:hover": { backgroundColor: "#F5F5F5" },
//               padding: "8px",
//             }}
//             size="small"
//           >
//             <Visibility sx={{ fontSize: 18 }} />
//           </IconButton>
//           <IconButton
//             onClick={() => handleDelete(user.id)}
//             sx={{
//               color: "#d00000",
//               "&:hover": { backgroundColor: "#F5F5F5" },
//               padding: "8px",
//             }}
//             size="small"
//           >
//             <Delete sx={{ fontSize: 18 }} />
//           </IconButton>
//         </Stack>
//       </CardContent>
//     </Card>
//   );

//   return (
//     <Container maxWidth="xl" sx={{ py: 1 }}>
//       <Typography
//         variant="h4"
//         sx={{ mb: 3, color: "#333", fontWeight: "600", fontSize: isMobile ? '24px' : '32px' }}
//       >
//         All Users
//       </Typography>

//       {/* Search Section */}
//       <Paper
//         elevation={1}
//         sx={{
//           border: "1px solid #E0E0E0",
//           borderRadius: 2,
//           p: 2,
//           mb: 2,
//         }}
//       >
//         <Grid container spacing={2}>
//           {/* Search Bar */}
//           <Grid item xs={12} md={6} lg={4}>
//             <TextField
//               fullWidth
//               placeholder="Search users..."
//               value={searchTerm}
//               onChange={(e) => setSearchTerm(e.target.value)}
//               InputProps={{
//                 startAdornment: <Search sx={{ color: '#666', mr: 1 }} />,
//               }}
//               sx={{
//                 '& .MuiOutlinedInput-root': {
//                   '& fieldset': {
//                     borderColor: '#E0E0E0',
//                   },
//                 },
//               }}
//             />
//           </Grid>

//           {/* Clear Search Button */}
//           <Grid item xs={12} sm={6} md={3} lg={2}>
//             <Button
//               fullWidth
//               variant="outlined"
//               onClick={() => setSearchTerm("")}
//               sx={{
//                 height: '48px',
//                 borderColor: '#E0E0E0',
//                 color: '#666',
//                 '&:hover': {
//                   borderColor: '#1976D2',
//                   backgroundColor: '#F5F5F5',
//                 },
//               }}
//             >
//               Clear Search
//             </Button>
//           </Grid>
//         </Grid>
//       </Paper>

//       {/* Table Section */}
//       <Paper
//         elevation={1}
//         sx={{
//           border: "1px solid #E0E0E0",
//           borderRadius: 2,
//           overflow: "hidden",
//         }}
//       >
//         {/* Desktop/Tablet Table View */}
//         {!isMobile ? (
//           <>
//             <TableContainer>
//               <Table>
//                 <TableHead>
//                   <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
//                     <TableCell
//                       sx={{
//                         color: "#666",
//                         fontWeight: "600",
//                         fontSize: "12px",
//                         textTransform: "uppercase",
//                         letterSpacing: "0.5px",
//                         borderBottom: "1px solid #E0E0E0",
//                       }}
//                     >
//                       Name
//                     </TableCell>
//                     <TableCell
//                       sx={{
//                         color: "#666",
//                         fontWeight: "600",
//                         fontSize: "12px",
//                         textTransform: "uppercase",
//                         letterSpacing: "0.5px",
//                         borderBottom: "1px solid #E0E0E0",
//                       }}
//                     >
//                       Email
//                     </TableCell>
//                     <TableCell
//                       sx={{
//                         color: "#666",
//                         fontWeight: "600",
//                         fontSize: "12px",
//                         textTransform: "uppercase",
//                         letterSpacing: "0.5px",
//                         borderBottom: "1px solid #E0E0E0",
//                         display: isTablet ? 'none' : 'table-cell',
//                       }}
//                     >
//                       Phone Number
//                     </TableCell>
//                     <TableCell
//                       sx={{
//                         color: "#666",
//                         fontWeight: "600",
//                         fontSize: "12px",
//                         textTransform: "uppercase",
//                         letterSpacing: "0.5px",
//                         borderBottom: "1px solid #E0E0E0",
//                         display: isTablet ? 'none' : 'table-cell',
//                       }}
//                     >
//                       Username
//                     </TableCell>
//                     <TableCell
//                       sx={{
//                         color: "#666",
//                         fontWeight: "600",
//                         fontSize: "12px",
//                         textTransform: "uppercase",
//                         letterSpacing: "0.5px",
//                         borderBottom: "1px solid #E0E0E0",
//                       }}
//                     >
//                       User Type
//                     </TableCell>
//                     <TableCell
//                       sx={{
//                         color: "#666",
//                         fontWeight: "600",
//                         fontSize: "12px",
//                         textTransform: "uppercase",
//                         letterSpacing: "0.5px",
//                         borderBottom: "1px solid #E0E0E0",
//                       }}
//                     >
//                       Actions
//                     </TableCell>
//                   </TableRow>
//                 </TableHead>
//                 <TableBody>
//                   {filteredUsers
//                     .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
//                     .map((user, index) => (
//                       <TableRow
//                         key={user.id}
//                         hover
//                         sx={{
//                           backgroundColor: "#fff",
//                           "&:hover": {
//                             backgroundColor: "#F8F9FA",
//                           },
//                           borderBottom: "1px solid #F0F0F0",
//                         }}
//                       >
//                         <TableCell
//                           sx={{
//                             color: "#333",
//                             fontSize: "13px",
//                             fontWeight: "500",
//                           }}
//                         >
//                           {getFullName(user)}
//                         </TableCell>
//                         <TableCell
//                           sx={{
//                             color: "#333",
//                             fontSize: "13px",
//                           }}
//                         >
//                           {user.email}
//                         </TableCell>
//                         <TableCell
//                           sx={{
//                             color: "#333",
//                             fontSize: "13px",
//                             display: isTablet ? 'none' : 'table-cell',
//                           }}
//                         >
//                           {user.phoneNumber}
//                         </TableCell>
//                         <TableCell
//                           sx={{
//                             color: "#333",
//                             fontSize: "13px",
//                             display: isTablet ? 'none' : 'table-cell',
//                           }}
//                         >
//                           {user.username}
//                         </TableCell>
//                         <TableCell>
//                           <Chip
//                             label={user.role}
//                             size="small"
//                             sx={{
//                               ...getRoleColor(user.role),
//                               border: "1px solid",
//                               fontWeight: "500",
//                               fontSize: "11px",
//                             }}
//                           />
//                         </TableCell>
//                         <TableCell>
//                           <Stack direction="row" spacing={0.5}>
//                             <IconButton
//                               onClick={() => handleView(user.id)}
//                               sx={{
//                                 color: "#3a86ff",
//                                 "&:hover": { backgroundColor: "#F5F5F5" },
//                                 padding: "4px",
//                               }}
//                               size="small"
//                             >
//                               <Visibility sx={{ fontSize: 16 }} />
//                             </IconButton>
//                             <IconButton
//                               onClick={() => handleDelete(user.id)}
//                               sx={{
//                                 color: "#d00000",
//                                 "&:hover": { backgroundColor: "#F5F5F5" },
//                                 padding: "4px",
//                               }}
//                               size="small"
//                             >
//                               <Delete sx={{ fontSize: 16 }} />
//                             </IconButton>
//                           </Stack>
//                         </TableCell>
//                       </TableRow>
//                     ))}
//                   {filteredUsers.length === 0 && (
//                     <TableRow>
//                       <TableCell colSpan={6} sx={{ textAlign: "center", py: 4 }}>
//                         <Typography variant="body1" color="textSecondary">
//                           No users found
//                         </Typography>
//                       </TableCell>
//                     </TableRow>
//                   )}
//                 </TableBody>
//               </Table>
//             </TableContainer>
//           </>
//         ) : (
//           /* Mobile Card View */
//           <Box sx={{ p: 2 }}>
//             {filteredUsers
//               .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
//               .map((user) => (
//                 <UserCard key={user.id} user={user} />
//               ))}
//             {filteredUsers.length === 0 && (
//               <Box sx={{ textAlign: "center", py: 4 }}>
//                 <Typography variant="body1" color="textSecondary">
//                   No users found
//                 </Typography>
//               </Box>
//             )}
//           </Box>
//         )}

//         {/* Custom Pagination */}
//         <Box
//           sx={{
//             display: "flex",
//             justifyContent: "space-between",
//             alignItems: "center",
//             p: 2,
//             borderTop: "1px solid #E0E0E0",
//             backgroundColor: "#FAFAFA",
//             flexDirection: isMobile ? 'column' : 'row',
//             gap: isMobile ? 2 : 0,
//           }}
//         >
//           <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
//             {[1, 2, 3, 4].map((pageNum) => (
//               <Button
//                 key={pageNum}
//                 variant={page + 1 === pageNum ? "contained" : "text"}
//                 onClick={() => setPage(pageNum - 1)}
//                 sx={{
//                   minWidth: 32,
//                   height: 32,
//                   borderRadius: 1,
//                   fontSize: "14px",
//                   ...(page + 1 === pageNum
//                     ? {
//                         backgroundColor: "#1976D2",
//                         color: "white",
//                         "&:hover": {
//                           backgroundColor: "#1565C0",
//                         },
//                       }
//                     : {
//                         color: "#666",
//                         "&:hover": {
//                           backgroundColor: "#F5F5F5",
//                         },
//                       }),
//                 }}
//               >
//                 {pageNum}
//               </Button>
//             ))}
//             <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
//               ...
//             </Typography>
//           </Box>
//           <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
//             <Typography variant="body2" sx={{ color: "#666" }}>
//               Go to page
//             </Typography>
//             <TextField
//               size="small"
//               type="number"
//               value={page + 1}
//               onChange={(e) =>
//                 setPage(Math.max(0, Number(e.target.value) - 1))
//               }
//               sx={{
//                 width: 60,
//                 "& .MuiOutlinedInput-root": {
//                   height: 32,
//                   "& fieldset": {
//                     borderColor: "#E0E0E0",
//                   },
//                 },
//               }}
//               inputProps={{
//                 min: 1,
//                 max: Math.ceil(filteredUsers.length / rowsPerPage),
//               }}
//             />
//             <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
//               <Typography variant="body2" color="#666">
//                 Show
//               </Typography>
//               <FormControl size="small" sx={{ minWidth: 80 }}>
//                 <Select
//                   value={rowsPerPage}
//                   onChange={handleChangeRowsPerPage}
//                   displayEmpty
//                   sx={{
//                     backgroundColor: "white",
//                     "& .MuiOutlinedInput-notchedOutline": {
//                       borderColor: "#E0E0E0",
//                     },
//                   }}
//                 >
//                   <MenuItem value={5}>5 Row</MenuItem>
//                   <MenuItem value={8}>8 Row</MenuItem>
//                   <MenuItem value={10}>10 Row</MenuItem>
//                   <MenuItem value={25}>25 Row</MenuItem>
//                 </Select>
//               </FormControl>
//             </Box>
//           </Box>
//         </Box>
//       </Paper>
//     </Container>
//   );
// };

// export default AllUsersPage;


import React, { useState, useEffect } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Typography,
  Chip,
  Stack,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { Visibility, Delete, Search } from "@mui/icons-material";
import { userData } from "../../../Dummydata/userData";
import { useNavigate } from "react-router-dom";
import EditUserDialog from "../../Admin/Users/<USER>"; // Import the dialog component

const AllUsersPage = () => {
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);

  // Dialog states
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);

  // Snackbar states
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success", // success, error, warning, info
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  // Load user data on component mount
  useEffect(() => {
    setUsers(userData);
    setFilteredUsers(userData);
  }, []);

  // Filter users based on search term
  useEffect(() => {
    let filtered = users;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (user) =>
          user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.phoneNumber.includes(searchTerm) ||
          user.role.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredUsers(filtered);
    setPage(0); // Reset page when filtering
  }, [searchTerm, users]);

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleView = (userId) => {
    const user = users.find((u) => u.id === userId);
    if (user) {
      setSelectedUser(user);
      setEditDialogOpen(true);
    }
  };

  const handleEditDialogClose = () => {
    setEditDialogOpen(false);
    setSelectedUser(null);
  };

  const handleSaveUser = (updatedUser) => {
    try {
      // Update the user in the users array
      const updatedUsers = users.map((user) =>
        user.id === updatedUser.id ? updatedUser : user
      );
      setUsers(updatedUsers);
      
      // Close dialog
      setEditDialogOpen(false);
      setSelectedUser(null);
      
      // Show success snackbar
      setSnackbar({
        open: true,
        message: "User updated successfully!",
        severity: "success",
      });
    } catch (error) {
      // Show error snackbar
      setSnackbar({
        open: true,
        message: "Failed to update user. Please try again.",
        severity: "error",
      });
    }
  };

  const handleDeleteClick = (userId) => {
    const user = users.find((u) => u.id === userId);
    if (user) {
      setUserToDelete(user);
      setDeleteDialogOpen(true);
    }
  };

  const handleDeleteConfirm = () => {
    try {
      // Remove user from the users array
      const updatedUsers = users.filter((user) => user.id !== userToDelete.id);
      setUsers(updatedUsers);
      
      // Close dialog
      setDeleteDialogOpen(false);
      setUserToDelete(null);
      
      // Show success snackbar
      setSnackbar({
        open: true,
        message: "User deleted successfully!",
        severity: "success",
      });
    } catch (error) {
      // Show error snackbar
      setSnackbar({
        open: true,
        message: "Failed to delete user. Please try again.",
        severity: "error",
      });
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  const handleSnackbarClose = () => {
    setSnackbar((prev) => ({
      ...prev,
      open: false,
    }));
  };

  const getRoleColor = (role) => {
    switch (role) {
      case "Admin":
        return {
          backgroundColor: "#FFE8E8",
          color: "#D32F2F",
          borderColor: "#FFCDD2",
        };
      case "User":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getFullName = (user) => {
    return `${user.firstName} ${user.lastName}`;
  };

  // Mobile Card View Component
  const UserCard = ({ user }) => (
    <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
            {getFullName(user)}
          </Typography>
          <Chip
            label={user.role}
            size="small"
            sx={{
              ...getRoleColor(user.role),
              border: "1px solid",
              fontWeight: "500",
              fontSize: "11px",
            }}
          />
        </Box>
        
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Email:</strong> {user.email}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Phone:</strong> {user.phoneNumber}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
          <strong>Username:</strong> {user.username}
        </Typography>

        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <IconButton
            onClick={() => handleView(user.id)}
            sx={{
              color: "#3a86ff",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Visibility sx={{ fontSize: 18 }} />
          </IconButton>
          <IconButton
            onClick={() => handleDeleteClick(user.id)}
            sx={{
              color: "#d00000",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Delete sx={{ fontSize: 18 }} />
          </IconButton>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="xl" sx={{ py: 1 }}>
      <Typography
        variant="h4"
        sx={{ mb: 3, color: "#333", fontWeight: "600", fontSize: isMobile ? '24px' : '32px' }}
      >
        All Users
      </Typography>

      {/* Search Section */}
      <Paper
        elevation={1}
        sx={{
          border: "1px solid #E0E0E0",
          borderRadius: 2,
          p: 2,
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          {/* Search Bar */}
          <Grid item xs={12} md={6} lg={4}>
            <TextField
              fullWidth
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <Search sx={{ color: '#666', mr: 1 }} />,
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: '#E0E0E0',
                  },
                },
              }}
            />
          </Grid>

          {/* Clear Search Button */}
          <Grid item xs={12} sm={6} md={3} lg={2}>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => setSearchTerm("")}
              sx={{
                height: '48px',
                borderColor: '#E0E0E0',
                color: '#666',
                '&:hover': {
                  borderColor: '#1976D2',
                  backgroundColor: '#F5F5F5',
                },
              }}
            >
              Clear Search
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Table Section */}
      <Paper
        elevation={1}
        sx={{
          border: "1px solid #E0E0E0",
          borderRadius: 2,
          overflow: "hidden",
        }}
      >
        {/* Desktop/Tablet Table View */}
        {!isMobile ? (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                      }}
                    >
                      Name
                    </TableCell>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                      }}
                    >
                      Email
                    </TableCell>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                        display: isTablet ? 'none' : 'table-cell',
                      }}
                    >
                      Phone Number
                    </TableCell>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                        display: isTablet ? 'none' : 'table-cell',
                      }}
                    >
                      Username
                    </TableCell>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                      }}
                    >
                      User Type
                    </TableCell>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                      }}
                    >
                      Actions
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredUsers
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((user, index) => (
                      <TableRow
                        key={user.id}
                        hover
                        sx={{
                          backgroundColor: "#fff",
                          "&:hover": {
                            backgroundColor: "#F8F9FA",
                          },
                          borderBottom: "1px solid #F0F0F0",
                        }}
                      >
                        <TableCell
                          sx={{
                            color: "#333",
                            fontSize: "13px",
                            fontWeight: "500",
                          }}
                        >
                          {getFullName(user)}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#333",
                            fontSize: "13px",
                          }}
                        >
                          {user.email}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#333",
                            fontSize: "13px",
                            display: isTablet ? 'none' : 'table-cell',
                          }}
                        >
                          {user.phoneNumber}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#333",
                            fontSize: "13px",
                            display: isTablet ? 'none' : 'table-cell',
                          }}
                        >
                          {user.username}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={user.role}
                            size="small"
                            sx={{
                              ...getRoleColor(user.role),
                              border: "1px solid",
                              fontWeight: "500",
                              fontSize: "11px",
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={0.5}>
                            <IconButton
                              onClick={() => handleView(user.id)}
                              sx={{
                                color: "#3a86ff",
                                "&:hover": { backgroundColor: "#F5F5F5" },
                                padding: "4px",
                              }}
                              size="small"
                            >
                              <Visibility sx={{ fontSize: 16 }} />
                            </IconButton>
                            <IconButton
                              onClick={() => handleDeleteClick(user.id)}
                              sx={{
                                color: "#d00000",
                                "&:hover": { backgroundColor: "#F5F5F5" },
                                padding: "4px",
                              }}
                              size="small"
                            >
                              <Delete sx={{ fontSize: 16 }} />
                            </IconButton>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  {filteredUsers.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} sx={{ textAlign: "center", py: 4 }}>
                        <Typography variant="body1" color="textSecondary">
                          No users found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        ) : (
          /* Mobile Card View */
          <Box sx={{ p: 2 }}>
            {filteredUsers
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((user) => (
                <UserCard key={user.id} user={user} />
              ))}
            {filteredUsers.length === 0 && (
              <Box sx={{ textAlign: "center", py: 4 }}>
                <Typography variant="body1" color="textSecondary">
                  No users found
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {/* Custom Pagination */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            p: 2,
            borderTop: "1px solid #E0E0E0",
            backgroundColor: "#FAFAFA",
            flexDirection: isMobile ? 'column' : 'row',
            gap: isMobile ? 2 : 0,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
            {[1, 2, 3, 4].map((pageNum) => (
              <Button
                key={pageNum}
                variant={page + 1 === pageNum ? "contained" : "text"}
                onClick={() => setPage(pageNum - 1)}
                sx={{
                  minWidth: 32,
                  height: 32,
                  borderRadius: 1,
                  fontSize: "14px",
                  ...(page + 1 === pageNum
                    ? {
                        backgroundColor: "#1976D2",
                        color: "white",
                        "&:hover": {
                          backgroundColor: "#1565C0",
                        },
                      }
                    : {
                        color: "#666",
                        "&:hover": {
                          backgroundColor: "#F5F5F5",
                        },
                      }),
                }}
              >
                {pageNum}
              </Button>
            ))}
            <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
              ...
            </Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
            <Typography variant="body2" sx={{ color: "#666" }}>
              Go to page
            </Typography>
            <TextField
              size="small"
              type="number"
              value={page + 1}
              onChange={(e) =>
                setPage(Math.max(0, Number(e.target.value) - 1))
              }
              sx={{
                width: 60,
                "& .MuiOutlinedInput-root": {
                  height: 32,
                  "& fieldset": {
                    borderColor: "#E0E0E0",
                  },
                },
              }}
              inputProps={{
                min: 1,
                max: Math.ceil(filteredUsers.length / rowsPerPage),
              }}
            />
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography variant="body2" color="#666">
                Show
              </Typography>
              <FormControl size="small" sx={{ minWidth: 80 }}>
                <Select
                  value={rowsPerPage}
                  onChange={handleChangeRowsPerPage}
                  displayEmpty
                  sx={{
                    backgroundColor: "white",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#E0E0E0",
                    },
                  }}
                >
                  <MenuItem value={5}>5 Row</MenuItem>
                  <MenuItem value={8}>8 Row</MenuItem>
                  <MenuItem value={10}>10 Row</MenuItem>
                  <MenuItem value={25}>25 Row</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Box>
        </Box>
      </Paper>

      {/* Edit User Dialog */}
      <EditUserDialog
        open={editDialogOpen}
        onClose={handleEditDialogClose}
        user={selectedUser}
        onSave={handleSaveUser}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
          },
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: "600", color: "#333" }}>
            Confirm Delete
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ color: "#666" }}>
            Are you sure you want to delete user{" "}
            <strong>
              {userToDelete ? getFullName(userToDelete) : ""}
            </strong>
            ? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, gap: 1 }}>
          <Button
            onClick={handleDeleteCancel}
            variant="outlined"
            sx={{
              borderColor: "#E0E0E0",
              color: "#666",
              "&:hover": {
                borderColor: "#1976D2",
                backgroundColor: "#F5F5F5",
              },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            variant="contained"
            sx={{
              backgroundColor: "#d32f2f",
              "&:hover": {
                backgroundColor: "#b71c1c",
              },
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{
            width: "100%",
            borderRadius: 2,
            "& .MuiAlert-icon": {
              fontSize: "20px",
            },
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default AllUsersPage;