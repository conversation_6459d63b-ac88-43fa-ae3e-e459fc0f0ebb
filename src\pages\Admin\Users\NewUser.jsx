import React, { useState } from "react";
import {
  Box,
  Paper,
  TextField,
  Typography,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Snackbar,
  Alert,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Save, Cancel, Visibility, VisibilityOff, Person } from "@mui/icons-material";

const NewUserPage = () => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    userType: "",
    username: "",
    password: "",
  });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success", // success, error, warning, info
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const isTablet = useMediaQuery(theme.breakpoints.down("lg"));

  const userTypeOptions = [
    { value: "Admin", label: "Admin" },
    { value: "Manager", label: "Manager" },
    { value: "Employee", label: "Employee" },
    { value: "Customer", label: "Customer" },
  ];

  const handleInputChange = (field) => (event) => {
    const value = event.target.value;
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    } else if (formData.firstName.trim().length < 2) {
      newErrors.firstName = "First name must be at least 2 characters";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    } else if (formData.lastName.trim().length < 2) {
      newErrors.lastName = "Last name must be at least 2 characters";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = "Phone number is required";
    } else if (!/^\+?[\d\s\-()]{10,}$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = "Please enter a valid phone number";
    }

    if (!formData.userType) {
      newErrors.userType = "User type is required";
    }

    if (!formData.username.trim()) {
      newErrors.username = "Username is required";
    } else if (formData.username.trim().length < 3) {
      newErrors.username = "Username must be at least 3 characters";
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = "Username can only contain letters, numbers, and underscores";
    }

    if (!formData.password.trim()) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = "Password must contain at least one uppercase letter, one lowercase letter, and one number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateForm()) {
      // Simulate API call
      console.log("Saving user:", {
        ...formData,
        id: Date.now(), // Generate temporary ID
        createdAt: new Date().toISOString(),
      });

      setSnackbar({
        open: true,
        message: "User created successfully!",
        severity: "success",
      });

      // Reset form after successful save
      setTimeout(() => {
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          phoneNumber: "",
          userType: "",
          username: "",
          password: "",
        });
        setShowPassword(false);
      }, 1000);
    } else {
      setSnackbar({
        open: true,
        message: "Please fix the errors in the form",
        severity: "error",
      });
    }
  };

  const handleCancel = () => {
    setFormData({
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      userType: "",
      username: "",
      password: "",
    });
    setErrors({});
    setShowPassword(false);
    setSnackbar({
      open: true,
      message: "Form cleared",
      severity: "info",
    });
  };

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  const getUserTypeColor = (userType) => {
    switch (userType) {
      case "Admin":
        return {
          backgroundColor: "#FFEBEE",
          color: "#C62828",
          borderColor: "#FFCDD2",
        };
      case "Manager":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      case "Employee":
        return {
          backgroundColor: "#E0F2F1",
          color: "#00796B",
          borderColor: "#80CBC4",
        };
      case "Customer":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFCC02",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const textFieldSx = {
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        borderColor: "#E0E0E0", // default
      },
      "&:hover fieldset": {
        borderColor: "#1976D2", // hover
      },
      "&.Mui-focused fieldset": {
        borderColor: "#666", // active/focused
      },
    },
    "& .MuiInputLabel-root": {
      color: "#666", // default label color
      fontSize: "14px",
    },
    "& .MuiInputLabel-root.Mui-focused": {
      color: "#666", // focused label color
    },
    "& .MuiInputLabel-root:hover": {
      color: "#1976D2", // hover label color (optional)
    },
  };

  return (
    <Container maxWidth="xl" sx={{ py: 1 }}>
      <Typography
        variant="h4"
        sx={{
          mb: 3,
          color: "#333",
          fontWeight: "600",
          fontSize: isMobile ? "24px" : "32px",
        }}
      >
        Create New User
      </Typography>

      {/* Form Section */}
      <Paper
        elevation={1}
        sx={{
          border: "1px solid #E0E0E0",
          borderRadius: 2,
          p: isMobile ? 2 : 4,
          mb: 2,
        }}
      >
        <Grid container spacing={3}>
          {/* First Name Field */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="First Name"
              placeholder="Enter first name"
              value={formData.firstName}
              onChange={handleInputChange("firstName")}
              error={!!errors.firstName}
              helperText={errors.firstName}
              sx={textFieldSx}
            />
          </Grid>

          {/* Last Name Field */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Last Name"
              placeholder="Enter last name"
              value={formData.lastName}
              onChange={handleInputChange("lastName")}
              error={!!errors.lastName}
              helperText={errors.lastName}
              sx={textFieldSx}
            />
          </Grid>

          {/* Email Field */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Email"
              placeholder="Enter email address"
              type="email"
              value={formData.email}
              onChange={handleInputChange("email")}
              error={!!errors.email}
              helperText={errors.email}
              sx={textFieldSx}
            />
          </Grid>

          {/* Phone Number Field */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Phone Number"
              placeholder="Enter phone number"
              value={formData.phoneNumber}
              onChange={handleInputChange("phoneNumber")}
              error={!!errors.phoneNumber}
              helperText={errors.phoneNumber}
              sx={textFieldSx}
            />
          </Grid>

          {/* User Type Field */}
          <Grid item xs={12} md={6}>
            <FormControl fullWidth error={!!errors.userType}>
              <InputLabel
                sx={{
                  color: "#666",
                  fontSize: "14px",
                  "&.Mui-focused": {
                    color: "#666",
                  },
                }}
              >
                User Type
              </InputLabel>
              <Select
                value={formData.userType}
                onChange={handleInputChange("userType")}
                label="User Type"
                sx={{
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#E0E0E0",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#1976D2",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#666",
                  },
                }}
              >
                {userTypeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
              {errors.userType && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                  {errors.userType}
                </Typography>
              )}
            </FormControl>
          </Grid>

          {/* Username Field */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Username"
              placeholder="Enter username"
              value={formData.username}
              onChange={handleInputChange("username")}
              error={!!errors.username}
              helperText={errors.username}
              sx={textFieldSx}
            />
          </Grid>

          {/* Password Field */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Password"
              placeholder="Enter password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={handleInputChange("password")}
              error={!!errors.password}
              helperText={errors.password}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleClickShowPassword}
                      edge="end"
                      sx={{ color: "#666" }}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={textFieldSx}
            />
          </Grid>

          {/* Action Buttons */}
          <Grid item xs={12}>
            <Box
              sx={{
                display: "flex",
                gap: 2,
                justifyContent: isMobile ? "stretch" : "flex-start",
                flexDirection: isMobile ? "column" : "row",
                mt: 2,
              }}
            >
              <Button
                variant="contained"
                onClick={handleSave}
                startIcon={<Save />}
                sx={{
                  backgroundColor: "#1976D2",
                  color: "white",
                  fontWeight: "500",
                  px: 4,
                  py: 1.5,
                  fontSize: "14px",
                  textTransform: "none",
                  "&:hover": {
                    backgroundColor: "#1565C0",
                  },
                  "&:disabled": {
                    backgroundColor: "#E0E0E0",
                    color: "#999",
                  },
                }}
              >
                Save User
              </Button>

              <Button
                variant="outlined"
                onClick={handleCancel}
                startIcon={<Cancel />}
                sx={{
                  borderColor: "#E0E0E0",
                  color: "#666",
                  fontWeight: "500",
                  px: 4,
                  py: 1.5,
                  fontSize: "14px",
                  textTransform: "none",
                  "&:hover": {
                    borderColor: "#1976D2",
                    backgroundColor: "#F5F5F5",
                  },
                }}
              >
                Cancel
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Preview Section */}
      {(formData.firstName || formData.lastName || formData.email || formData.userType) && (
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            p: isMobile ? 2 : 3,
            backgroundColor: "#FAFAFA",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              mb: 2,
              color: "#333",
              fontWeight: "500",
              fontSize: "16px",
            }}
          >
            Preview
          </Typography>

          <Box
            sx={{
              p: 2,
              backgroundColor: "white",
              borderRadius: 1,
              border: "1px solid #E0E0E0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "flex-start",
                mb: 2,
                flexWrap: "wrap",
                gap: 1,
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Person sx={{ color: "#666", fontSize: "20px" }} />
                <Typography
                  variant="body1"
                  sx={{ fontWeight: "500", color: "#333" }}
                >
                  {formData.firstName || formData.lastName 
                    ? `${formData.firstName || ""} ${formData.lastName || ""}`.trim() || "Full Name"
                    : "Full Name"}
                </Typography>
              </Box>
              {formData.userType && (
                <Box
                  sx={{
                    px: 1.5,
                    py: 0.5,
                    borderRadius: 1,
                    fontSize: "12px",
                    fontWeight: "500",
                    ...getUserTypeColor(formData.userType),
                    border: "1px solid",
                  }}
                >
                  {formData.userType}
                </Box>
              )}
            </Box>
            
            <Grid container spacing={1}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" sx={{ color: "#666" }}>
                  <strong>Email:</strong> {formData.email || "<EMAIL>"}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" sx={{ color: "#666" }}>
                  <strong>Phone:</strong> {formData.phoneNumber || "Phone number"}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" sx={{ color: "#666" }}>
                  <strong>Username:</strong> {formData.username || "username"}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </Paper>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default NewUserPage;