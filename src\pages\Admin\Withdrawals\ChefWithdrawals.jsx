import React, { useState, useEffect } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Typography,
  Chip,
  Stack,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
} from "@mui/material";
import { Visibility, Check, Close, Search } from "@mui/icons-material";
import { chefWithdrawalData } from "../../../Dummydata/chefWithdrawalData";
import { useNavigate } from "react-router-dom";

const ChefWithdrawalsPage = () => {
  const navigate = useNavigate();
  const [withdrawals, setWithdrawals] = useState([]);
  const [filteredWithdrawals, setFilteredWithdrawals] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  // Load withdrawal data on component mount
  useEffect(() => {
    setWithdrawals(chefWithdrawalData);
    setFilteredWithdrawals(chefWithdrawalData);
  }, []);

  // Filter withdrawals based on search term, status, and date range
  useEffect(() => {
    let filtered = withdrawals;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (withdrawal) =>
          withdrawal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          withdrawal.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          withdrawal.amount.toString().includes(searchTerm) ||
          withdrawal.status.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter(
        (withdrawal) => withdrawal.status === statusFilter
      );
    }

    // Date range filter
    if (fromDate) {
      filtered = filtered.filter(
        (withdrawal) => new Date(withdrawal.withdrawalDate) >= new Date(fromDate)
      );
    }

    if (toDate) {
      filtered = filtered.filter(
        (withdrawal) => new Date(withdrawal.withdrawalDate) <= new Date(toDate)
      );
    }

    setFilteredWithdrawals(filtered);
    setPage(0); // Reset page when filtering
  }, [searchTerm, statusFilter, fromDate, toDate, withdrawals]);

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleView = (withdrawalId) => {
    navigate(`/system/withdrawals/view/${withdrawalId}`);
  };

  const handleApprove = (withdrawalId) => {
    console.log("Approve withdrawal:", withdrawalId);
    // Add approve functionality here
  };

  const handleReject = (withdrawalId) => {
    console.log("Reject withdrawal:", withdrawalId);
    // Add reject functionality here
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "Approved":
        return {
          backgroundColor: "#E8F5E8",
          color: "#2E7D32",
          borderColor: "#C8E6C9",
        };
      case "Pending":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFCC02",
        };
      case "Rejected":
        return {
          backgroundColor: "#FFE8E8",
          color: "#D32F2F",
          borderColor: "#FFCDD2",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatAmount = (amount) => {
    return `$${amount.toFixed(2)}`;
  };

  const clearAllFilters = () => {
    setSearchTerm("");
    setStatusFilter("");
    setFromDate("");
    setToDate("");
  };

  // Mobile Card View Component
  const WithdrawalCard = ({ withdrawal }) => (
    <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
            {withdrawal.name}
          </Typography>
          <Chip
            label={withdrawal.status}
            size="small"
            sx={{
              ...getStatusColor(withdrawal.status),
              border: "1px solid",
              fontWeight: "500",
              fontSize: "11px",
            }}
          />
        </Box>
        
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Date:</strong> {formatDateTime(withdrawal.withdrawalDate)}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Username:</strong> {withdrawal.username}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
          <strong>Amount:</strong> {formatAmount(withdrawal.amount)}
        </Typography>

        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <IconButton
            onClick={() => handleView(withdrawal.id)}
            sx={{
              color: "#3a86ff",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Visibility sx={{ fontSize: 18 }} />
          </IconButton>
          {withdrawal.status === "Pending" && (
            <>
              <IconButton
                onClick={() => handleApprove(withdrawal.id)}
                sx={{
                  color: "#2E7D32",
                  "&:hover": { backgroundColor: "#F5F5F5" },
                  padding: "8px",
                }}
                size="small"
              >
                <Check sx={{ fontSize: 18 }} />
              </IconButton>
              <IconButton
                onClick={() => handleReject(withdrawal.id)}
                sx={{
                  color: "#d00000",
                  "&:hover": { backgroundColor: "#F5F5F5" },
                  padding: "8px",
                }}
                size="small"
              >
                <Close sx={{ fontSize: 18 }} />
              </IconButton>
            </>
          )}
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="xl" sx={{ py: 1 }}>
      <Typography
        variant="h4"
        sx={{ mb: 3, color: "#333", fontWeight: "600", fontSize: isMobile ? '24px' : '32px' }}
      >
        Chef Withdrawals
      </Typography>

      {/* Filters Section */}
      <Paper
        elevation={1}
        sx={{
          border: "1px solid #E0E0E0",
          borderRadius: 2,
          p: 2,
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          {/* Search Bar */}
          <Grid item xs={12} md={6} lg={3}>
            <TextField
              fullWidth
              placeholder="Search withdrawals..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <Search sx={{ color: '#666', mr: 1 }} />,
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: '#E0E0E0',
                  },
                },
              }}
            />
          </Grid>

          {/* Status Filter */}
          <Grid item xs={12} sm={6} md={3} lg={2}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Status"
                sx={{
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#E0E0E0',
                  },
                }}
              >
                <MenuItem value="">All Status</MenuItem>
                <MenuItem value="Approved">Approved</MenuItem>
                <MenuItem value="Pending">Pending</MenuItem>
                <MenuItem value="Rejected">Rejected</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* From Date Filter */}
          <Grid item xs={12} sm={6} md={3} lg={2}>
            <TextField
              fullWidth
              label="From Date"
              type="date"
              value={fromDate}
              onChange={(e) => setFromDate(e.target.value)}
              InputLabelProps={{
                shrink: true,
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: '#E0E0E0',
                  },
                },
              }}
            />
          </Grid>

          {/* To Date Filter */}
          <Grid item xs={12} sm={6} md={3} lg={2}>
            <TextField
              fullWidth
              label="To Date"
              type="date"
              value={toDate}
              onChange={(e) => setToDate(e.target.value)}
              InputLabelProps={{
                shrink: true,
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: '#E0E0E0',
                  },
                },
              }}
            />
          </Grid>

          {/* Clear All Filters Button */}
          <Grid item xs={12} sm={6} md={3} lg={3}>
            <Button
              fullWidth
              variant="outlined"
              onClick={clearAllFilters}
              sx={{
                height: '56px',
                borderColor: '#E0E0E0',
                color: '#666',
                '&:hover': {
                  borderColor: '#1976D2',
                  backgroundColor: '#F5F5F5',
                },
              }}
            >
              Clear All Filters
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Table Section */}
      <Paper
        elevation={1}
        sx={{
          border: "1px solid #E0E0E0",
          borderRadius: 2,
          overflow: "hidden",
        }}
      >
        {/* Desktop/Tablet Table View */}
        {!isMobile ? (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                      }}
                    >
                      Withdrawal Date
                    </TableCell>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                      }}
                    >
                      Name
                    </TableCell>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                        display: isTablet ? 'none' : 'table-cell',
                      }}
                    >
                      Username
                    </TableCell>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                      }}
                    >
                      Amount
                    </TableCell>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                      }}
                    >
                      Status
                    </TableCell>
                    <TableCell
                      sx={{
                        color: "#666",
                        fontWeight: "600",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        borderBottom: "1px solid #E0E0E0",
                      }}
                    >
                      Actions
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredWithdrawals
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((withdrawal, index) => (
                      <TableRow
                        key={withdrawal.id}
                        hover
                        sx={{
                          backgroundColor: "#fff",
                          "&:hover": {
                            backgroundColor: "#F8F9FA",
                          },
                          borderBottom: "1px solid #F0F0F0",
                        }}
                      >
                        <TableCell
                          sx={{
                            color: "#333",
                            fontSize: "13px",
                            fontWeight: "500",
                          }}
                        >
                          {formatDateTime(withdrawal.withdrawalDate)}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#333",
                            fontSize: "13px",
                            fontWeight: "500",
                          }}
                        >
                          {withdrawal.name}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#333",
                            fontSize: "13px",
                            display: isTablet ? 'none' : 'table-cell',
                          }}
                        >
                          {withdrawal.username}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: "#333",
                            fontSize: "13px",
                            fontWeight: "600",
                          }}
                        >
                          {formatAmount(withdrawal.amount)}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={withdrawal.status}
                            size="small"
                            sx={{
                              ...getStatusColor(withdrawal.status),
                              border: "1px solid",
                              fontWeight: "500",
                              fontSize: "11px",
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={0.5}>
                            <IconButton
                              onClick={() => handleView(withdrawal.id)}
                              sx={{
                                color: "#3a86ff",
                                "&:hover": { backgroundColor: "#F5F5F5" },
                                padding: "4px",
                              }}
                              size="small"
                            >
                              <Visibility sx={{ fontSize: 16 }} />
                            </IconButton>
                            {withdrawal.status === "Pending" && (
                              <>
                                <IconButton
                                  onClick={() => handleApprove(withdrawal.id)}
                                  sx={{
                                    color: "#2E7D32",
                                    "&:hover": { backgroundColor: "#F5F5F5" },
                                    padding: "4px",
                                  }}
                                  size="small"
                                >
                                  <Check sx={{ fontSize: 16 }} />
                                </IconButton>
                                <IconButton
                                  onClick={() => handleReject(withdrawal.id)}
                                  sx={{
                                    color: "#d00000",
                                    "&:hover": { backgroundColor: "#F5F5F5" },
                                    padding: "4px",
                                  }}
                                  size="small"
                                >
                                  <Close sx={{ fontSize: 16 }} />
                                </IconButton>
                              </>
                            )}
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  {filteredWithdrawals.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} sx={{ textAlign: "center", py: 4 }}>
                        <Typography variant="body1" color="textSecondary">
                          No withdrawals found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        ) : (
          /* Mobile Card View */
          <Box sx={{ p: 2 }}>
            {filteredWithdrawals
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((withdrawal) => (
                <WithdrawalCard key={withdrawal.id} withdrawal={withdrawal} />
              ))}
            {filteredWithdrawals.length === 0 && (
              <Box sx={{ textAlign: "center", py: 4 }}>
                <Typography variant="body1" color="textSecondary">
                  No withdrawals found
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {/* Custom Pagination */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            p: 2,
            borderTop: "1px solid #E0E0E0",
            backgroundColor: "#FAFAFA",
            flexDirection: isMobile ? 'column' : 'row',
            gap: isMobile ? 2 : 0,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
            {[1, 2, 3, 4].map((pageNum) => (
              <Button
                key={pageNum}
                variant={page + 1 === pageNum ? "contained" : "text"}
                onClick={() => setPage(pageNum - 1)}
                sx={{
                  minWidth: 32,
                  height: 32,
                  borderRadius: 1,
                  fontSize: "14px",
                  ...(page + 1 === pageNum
                    ? {
                        backgroundColor: "#1976D2",
                        color: "white",
                        "&:hover": {
                          backgroundColor: "#1565C0",
                        },
                      }
                    : {
                        color: "#666",
                        "&:hover": {
                          backgroundColor: "#F5F5F5",
                        },
                      }),
                }}
              >
                {pageNum}
              </Button>
            ))}
            <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
              ...
            </Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
            <Typography variant="body2" sx={{ color: "#666" }}>
              Go to page
            </Typography>
            <TextField
              size="small"
              type="number"
              value={page + 1}
              onChange={(e) =>
                setPage(Math.max(0, Number(e.target.value) - 1))
              }
              sx={{
                width: 60,
                "& .MuiOutlinedInput-root": {
                  height: 32,
                  "& fieldset": {
                    borderColor: "#E0E0E0",
                  },
                },
              }}
              inputProps={{
                min: 1,
                max: Math.ceil(filteredWithdrawals.length / rowsPerPage),
              }}
            />
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography variant="body2" color="#666">
                Show
              </Typography>
              <FormControl size="small" sx={{ minWidth: 80 }}>
                <Select
                  value={rowsPerPage}
                  onChange={handleChangeRowsPerPage}
                  displayEmpty
                  sx={{
                    backgroundColor: "white",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#E0E0E0",
                    },
                  }}
                >
                  <MenuItem value={5}>5 Row</MenuItem>
                  <MenuItem value={8}>8 Row</MenuItem>
                  <MenuItem value={10}>10 Row</MenuItem>
                  <MenuItem value={25}>25 Row</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default ChefWithdrawalsPage;