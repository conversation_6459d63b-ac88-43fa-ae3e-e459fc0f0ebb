import React, { useState, useEffect } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Typography,
  Chip,
  Stack,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
  Divider,
} from "@mui/material";
import { Edit, Search, CloudUpload } from "@mui/icons-material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { withdrawalRequestsData } from "../../../Dummydata/withdrawaRequestData";

const AllWithdrawalRequestsPage = () => {
  const [withdrawalRequests, setWithdrawalRequests] = useState([]);
  const [filteredRequests, setFilteredRequests] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);
  const [editingRequest, setEditingRequest] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  // Load withdrawal requests data on component mount
  useEffect(() => {
    setWithdrawalRequests(withdrawalRequestsData);
    setFilteredRequests(withdrawalRequestsData);
  }, []);

  // Filter requests based on search term and date range
  useEffect(() => {
    let filtered = withdrawalRequests;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (request) =>
          request.chefName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          request.amount.toString().includes(searchTerm) ||
          request.requestStatus.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Date range filter
    if (startDate || endDate) {
      filtered = filtered.filter((request) => {
        const requestDate = new Date(request.date);
        const start = startDate ? new Date(startDate) : new Date("1900-01-01");
        const end = endDate ? new Date(endDate) : new Date("2100-12-31");
        return requestDate >= start && requestDate <= end;
      });
    }

    setFilteredRequests(filtered);
    setPage(0); // Reset page when filtering
  }, [searchTerm, startDate, endDate, withdrawalRequests]);

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleEdit = (requestId) => {
    const request = withdrawalRequests.find(req => req.id === requestId);
    setEditingRequest(request);
    setShowEditModal(true);
    setSelectedImage(null);
    setImagePreview(null);
  };

  const handleStatusChange = (requestId, newStatus) => {
    const updatedRequests = withdrawalRequests.map(request =>
      request.id === requestId
        ? { ...request, requestStatus: newStatus }
        : request
    );
    setWithdrawalRequests(updatedRequests);
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSaveChanges = () => {
    // Here you would typically save the changes to your backend
    console.log('Saving changes:', {
      requestId: editingRequest.id,
      status: editingRequest.requestStatus,
      image: selectedImage,
    });
    setShowEditModal(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "Pending":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFB74D",
        };
      case "Approved":
        return {
          backgroundColor: "#E8F5E8",
          color: "#2E7D32",
          borderColor: "#81C784",
        };
      case "Rejected":
        return {
          backgroundColor: "#FFEBEE",
          color: "#C62828",
          borderColor: "#E57373",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      }) +
      " " +
      date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    );
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Mobile Card View Component
  const WithdrawalCard = ({ request }) => (
    <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
            {request.chefName}
          </Typography>
          <Chip
            label={request.requestStatus}
            size="small"
            sx={{
              ...getStatusColor(request.requestStatus),
              border: "1px solid",
              fontWeight: "500",
              fontSize: "11px",
            }}
          />
        </Box>
        
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Amount:</strong> {formatCurrency(request.amount)}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Date:</strong> {formatDateTime(request.date)}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
          <strong>Bank Account:</strong> {request.bankAccount}
        </Typography>

        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <IconButton
            onClick={() => handleEdit(request.id)}
            sx={{
              color: "#3a86ff",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Edit sx={{ fontSize: 18 }} />
          </IconButton>
        </Stack>
      </CardContent>
    </Card>
  );

  // Edit Modal Component
  const EditModal = () => {
    if (!editingRequest) return null;

    return (
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1300,
          p: 2,
        }}
        onClick={() => setShowEditModal(false)}
      >
        <Paper
          sx={{
            p: 4,
            maxWidth: 600,
            width: '100%',
            maxHeight: '90vh',
            overflow: 'auto',
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <Typography variant="h5" sx={{ mb: 3, fontWeight: '600', color: '#333' }}>
            Withdrawal Request Details
          </Typography>
          
          {/* Request Details Section */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: '600', color: '#555' }}>
              Request Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ color: '#666', fontWeight: '500', mb: 0.5 }}>
                    Chef Name
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#333', fontWeight: '400' }}>
                    {editingRequest.chefName}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ color: '#666', fontWeight: '500', mb: 0.5 }}>
                    Amount
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#333', fontWeight: '500' }}>
                    {formatCurrency(editingRequest.amount)}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ color: '#666', fontWeight: '500', mb: 0.5 }}>
                    Date
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#333', fontWeight: '400' }}>
                    {formatDateTime(editingRequest.date)}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ color: '#666', fontWeight: '500', mb: 0.5 }}>
                    Bank Account
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#333', fontWeight: '400' }}>
                    {editingRequest.bankAccount}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ color: '#666', fontWeight: '500', mb: 0.5 }}>
                    Description
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#333', fontWeight: '400' }}>
                    {editingRequest.description}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Editable Section */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: '600', color: '#555' }}>
              Update Request
            </Typography>
            
            {/* Status Change */}
            <Box sx={{ mb: 3 }}>
              <FormControl fullWidth>
                <InputLabel>Request Status</InputLabel>
                <Select
                  value={editingRequest.requestStatus}
                  label="Request Status"
                  onChange={(e) => {
                    const newStatus = e.target.value;
                    setEditingRequest({ ...editingRequest, requestStatus: newStatus });
                  }}
                >
                  <MenuItem value="Pending">Pending</MenuItem>
                  <MenuItem value="Approved">Approved</MenuItem>
                  <MenuItem value="Rejected">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Box>

            {/* Image Upload */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" sx={{ color: '#666', fontWeight: '500', mb: 1 }}>
                Upload Supporting Document
              </Typography>
              <Box
                sx={{
                  border: '2px dashed #E0E0E0',
                  borderRadius: 2,
                  p: 3,
                  textAlign: 'center',
                  backgroundColor: '#FAFAFA',
                  cursor: 'pointer',
                  '&:hover': {
                    borderColor: '#1976D2',
                    backgroundColor: '#F5F5F5',
                  },
                }}
                onClick={() => document.getElementById('image-upload').click()}
              >
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  style={{ display: 'none' }}
                />
                
                {imagePreview ? (
                  <Box>
                    <img
                      src={imagePreview}
                      alt="Preview"
                      style={{
                        maxWidth: '100%',
                        maxHeight: '200px',
                        borderRadius: '8px',
                        marginBottom: '8px',
                      }}
                    />
                    <Typography variant="body2" sx={{ color: '#666' }}>
                      Click to change image
                    </Typography>
                  </Box>
                ) : (
                  <Box>
                    <CloudUpload sx={{ fontSize: 48, color: '#1976D2', mb: 1 }} />
                    <Typography variant="body1" sx={{ color: '#333', mb: 0.5 }}>
                      Click to upload image
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666' }}>
                      PNG, JPG, GIF up to 10MB
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          </Box>

          {/* Action Buttons */}
          <Stack direction="row" spacing={2} justifyContent="flex-end">
            <Button
              variant="outlined"
              onClick={() => setShowEditModal(false)}
              sx={{ px: 3 }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleSaveChanges}
              sx={{ px: 3 }}
            >
              Save Changes
            </Button>
          </Stack>
        </Paper>
      </Box>
    );
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="xl" sx={{ py: 1 }}>
        <Typography
          variant="h4"
          sx={{ mb: 3, color: "#333", fontWeight: "600", fontSize: isMobile ? '24px' : '32px' }}
        >
          All Withdrawal Requests
        </Typography>

        {/* Search and Filters Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            p: 2,
            mb: 2,
          }}
        >
          <Grid container spacing={2}>
            {/* Search Bar */}
            <Grid item xs={12} md={6} lg={4}>
              <TextField
                fullWidth
                placeholder="Search by chef name, amount, or status..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ color: '#666', mr: 1 }} />,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E0E0E0',
                    },
                  },
                }}
              />
            </Grid>

            {/* Start Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={3}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E0E0E0',
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* End Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={3}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E0E0E0',
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* Clear Filters Button */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm("");
                  setStartDate(null);
                  setEndDate(null);
                }}
                sx={{
                  height: '48px',
                  borderColor: '#E0E0E0',
                  color: '#666',
                  '&:hover': {
                    borderColor: '#1976D2',
                    backgroundColor: '#F5F5F5',
                  },
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Table Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            overflow: "hidden",
          }}
        >
          {/* Desktop/Tablet Table View */}
          {!isMobile ? (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Date
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Amount
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Chef Name
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Request Status
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Action
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredRequests
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((request) => (
                        <TableRow
                          key={request.id}
                          hover
                          sx={{
                            backgroundColor: "#fff",
                            "&:hover": {
                              backgroundColor: "#F8F9FA",
                            },
                            borderBottom: "1px solid #F0F0F0",
                          }}
                        >
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "400",
                            }}
                          >
                            {formatDateTime(request.date)}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "500",
                            }}
                          >
                            {formatCurrency(request.amount)}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "500",
                            }}
                          >
                            {request.chefName}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={request.requestStatus}
                              size="small"
                              sx={{
                                ...getStatusColor(request.requestStatus),
                                border: "1px solid",
                                fontWeight: "500",
                                fontSize: "11px",
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={0.5}>
                              <IconButton
                                onClick={() => handleEdit(request.id)}
                                sx={{
                                  color: "#3a86ff",
                                  "&:hover": { backgroundColor: "#F5F5F5" },
                                  padding: "4px",
                                }}
                                size="small"
                              >
                                <Edit sx={{ fontSize: 16 }} />
                              </IconButton>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))}
                    {filteredRequests.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} sx={{ textAlign: "center", py: 4 }}>
                          <Typography variant="body1" color="textSecondary">
                            No withdrawal requests found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          ) : (
            /* Mobile Card View */
            <Box sx={{ p: 2 }}>
              {filteredRequests
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((request) => (
                  <WithdrawalCard key={request.id} request={request} />
                ))}
              {filteredRequests.length === 0 && (
                <Box sx={{ textAlign: "center", py: 4 }}>
                  <Typography variant="body1" color="textSecondary">
                    No withdrawal requests found
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {/* Custom Pagination */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              p: 2,
              borderTop: "1px solid #E0E0E0",
              backgroundColor: "#FAFAFA",
              flexDirection: isMobile ? 'column' : 'row',
              gap: isMobile ? 2 : 0,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
              {[1, 2, 3, 4].map((pageNum) => (
                <Button
                  key={pageNum}
                  variant={page + 1 === pageNum ? "contained" : "text"}
                  onClick={() => setPage(pageNum - 1)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: 1,
                    fontSize: "14px",
                    ...(page + 1 === pageNum
                      ? {
                          backgroundColor: "#1976D2",
                          color: "white",
                          "&:hover": {
                            backgroundColor: "#1565C0",
                          },
                        }
                      : {
                          color: "#666",
                          "&:hover": {
                            backgroundColor: "#F5F5F5",
                          },
                        }),
                  }}
                >
                  {pageNum}
                </Button>
              ))}
              <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
                ...
              </Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
              <Typography variant="body2" sx={{ color: "#666" }}>
                Go to page
              </Typography>
              <TextField
                size="small"
                type="number"
                value={page + 1}
                onChange={(e) =>
                  setPage(Math.max(0, Number(e.target.value) - 1))
                }
                sx={{
                  width: 60,
                  "& .MuiOutlinedInput-root": {
                    height: 32,
                    "& fieldset": {
                      borderColor: "#E0E0E0",
                    },
                  },
                }}
                inputProps={{
                  min: 1,
                  max: Math.ceil(filteredRequests.length / rowsPerPage),
                }}
              />
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography variant="body2" color="#666">
                  Show
                </Typography>
                <FormControl size="small" sx={{ minWidth: 80 }}>
                  <Select
                    value={rowsPerPage}
                    onChange={handleChangeRowsPerPage}
                    displayEmpty
                    sx={{
                      backgroundColor: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E0E0E0",
                      },
                    }}
                  >
                    <MenuItem value={5}>5 Row</MenuItem>
                    <MenuItem value={8}>8 Row</MenuItem>
                    <MenuItem value={10}>10 Row</MenuItem>
                    <MenuItem value={25}>25 Row</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Box>
          </Box>
        </Paper>

        {/* Edit Modal */}
        {showEditModal && <EditModal />}
      </Container>
    </LocalizationProvider>
  );
};

export default AllWithdrawalRequestsPage;