import React, {
  useState,
  useCallback,
  useEffect,
  startTransition,
} from "react";
import {
  Box,
  Grid,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Card,
  CardContent,
  RadioGroup,
  Radio,
  Link,
  Container,
  Paper,
  Slide,
  Fade,
  InputAdornment,
  Alert,
  keyframes,
  Snackbar,
} from "@mui/material";
import {
  Person,
  Email,
  Phone,
  Lock,
  Business,
  Restaurant,
  AccountCircle,
} from "@mui/icons-material";

import { getAllSubscriptions } from "../../app/service/subscription.service";
import { saveChef, saveCustomer } from "../../app/service/auth.service";
import { useNavigate } from "react-router-dom";

// Dummy data for subscription plans
// const subscriptionPlans = [
//   { id: 1, name: "1 Month", price: 1000, duration: "1 Month" },
//   { id: 2, name: "6 Months", price: 5000, duration: "6 Months" },
//   { id: 3, name: "1 Year", price: 10000, duration: "1 Year" },
// ];

// Country codes (simplified list)
const countryCodes = [
  { code: "+1", country: "US", name: "United States" },
  { code: "+44", country: "GB", name: "United Kingdom" },
  { code: "+91", country: "IN", name: "India" },
  { code: "+94", country: "LK", name: "Sri Lanka" },
  { code: "+61", country: "AU", name: "Australia" },
  { code: "+86", country: "CN", name: "China" },
  { code: "+81", country: "JP", name: "Japan" },
  { code: "+49", country: "DE", name: "Germany" },
  { code: "+33", country: "FR", name: "France" },
  { code: "+39", country: "IT", name: "Italy" },
];

// Glitter animation
const glitter = keyframes`
  0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.6), 0 0 30px rgba(255, 215, 0, 0.4); }
  100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
`;

const Register = () => {
  const [isChefForm, setIsChefForm] = useState(true);
  const [slideDirection, setSlideDirection] = useState("left");
  const [subscriptionPlans, setSubscriptionPlans] = useState([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success", // success, error, warning, info
  });
  const [submitLoading, setSubmitLoading] = useState(false);
  const fetchSubscriptions = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getAllSubscriptions();

      if (response.responseCode === 1000 && response.data) {
        // Transform API data to match your existing UI structure
        const transformedPlans = response.data.map((plan) => ({
          id: plan.id,
          name: plan.plan_name,
          price: plan.plan_amount,
          duration: `${plan.plan_duration} Month${plan.plan_duration > 1 ? "s" : ""}`,
        }));
        setSubscriptionPlans(transformedPlans);
      } else {
        console.log("No subscription plans available");
        setSubscriptionPlans([]);
      }
    } catch (error) {
      console.error("Error fetching subscriptions:", error);
      setSubscriptionPlans([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // 4. Add this useEffect inside the Register component
  useEffect(() => {
    fetchSubscriptions();
  }, [fetchSubscriptions]);

  // Chef form state
  const [chefForm, setChefForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    countryCode: "+94",
    phoneNumber: "",
    username: "",
    password: "",
    confirmPassword: "",
    agreeTerms: false,
  });

  // Customer form state
  const [customerForm, setCustomerForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    countryCode: "+94",
    phoneNumber: "",
    username: "",
    password: "",
    confirmPassword: "",
    subscriptionPlan: "",
    agreeTerms: false,
  });

  const [errors, setErrors] = useState({});

  const handleToggleForm = (formType) => {
    if (formType !== (isChefForm ? "chef" : "customer")) {
      setSlideDirection(formType === "chef" ? "right" : "left");
      setTimeout(() => {
        setIsChefForm(formType === "chef");
      }, 150);
    }
  };

  const validateForm = (formData, isChef = true) => {
    const newErrors = {};

    if (!formData.firstName.trim())
      newErrors.firstName = "First name is required";
    if (!formData.lastName.trim()) newErrors.lastName = "Last name is required";
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = "Phone number is required";
    } else if (!/^\d{9,10}$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = "Phone number must be 9-10 digits";
    }
    if (!formData.username.trim()) {
      newErrors.username = "Username is required";
    } else if (formData.username.length < 3) {
      newErrors.username = "Username must be at least 3 characters";
    }
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }
    if (!isChef && !formData.subscriptionPlan) {
      newErrors.subscriptionPlan = "Please select a subscription plan";
    }
    if (!formData.agreeTerms) {
      newErrors.agreeTerms = "You must agree to terms and conditions";
    }

    return newErrors;
  };

  const handleCustomerRegistration = useCallback(async (formData) => {
    try {
      setSubmitLoading(true);

      const payload = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        mobileNumber: formData.phoneNumber,
        countryCode: formData.countryCode,
        userName: formData.username,
        password: formData.password,
        userType: 3,
        subscriptionPlanId: parseInt(formData.subscriptionPlan),
      };

      const response = await saveCustomer(payload);

      if (response.success) {
        // Success - you can add success handling here
        console.log("Customer registered successfully:", response);
        setSnackbar({
          open: true,
          message: response.message,
          severity: "success",
        });
        startTransition(() => {
          setTimeout(() => {
            navigate("/contact-us");
          }, 500);
        });

        // Maybe redirect or show success message
      } else {
        // Handle API error response
        console.error("Registration failed:", response.message);
        setSnackbar({
          open: true,
          message: response.message,
          severity: "error",
        });
        // You might want to show an error message to user
      }
    } catch (error) {
      console.error("Error during registration:", error);
      // Handle network or other errors
    } finally {
      setSubmitLoading(false);
    }
  }, []);
  
  const handleChefRegistration = useCallback(async (formData) => {
    try {
      setSubmitLoading(true);

      const payload = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        mobileNumber: formData.phoneNumber,
        countryCode: formData.countryCode,
        userName: formData.username,
        password: formData.password,
        userType: 2,
      };

      const response = await saveChef(payload);

      if (response.success) {
        // Success - you can add success handling here
        console.log("Chef registered successfully:", response);
        setSnackbar({
          open: true,
          message: response.message,
          severity: "success",
        });
        startTransition(() => {
          setTimeout(() => {
            navigate("/system/my-profile");
          }, 500);
        });

        // Maybe redirect or show success message
      } else {
        // Handle API error response
        console.error("Registration failed:", response.message);
        setSnackbar({
          open: true,
          message: response.message,
          severity: "error",
        });
        // You might want to show an error message to user
      }
    } catch (error) {
      console.error("Error during registration:", error);
      // Handle network or other errors
    } finally {
      setSubmitLoading(false);
    }
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = isChefForm ? chefForm : customerForm;
    const formErrors = validateForm(formData, isChefForm);

    if (Object.keys(formErrors).length === 0) {
      if (isChefForm) {
        await handleChefRegistration(formData);
        // Chef API integration can be added later
      } else {
        // Call customer registration API
        await handleCustomerRegistration(formData);
      }
    } else {
      setErrors(formErrors);
    }
  };

  const updateForm = (field, value) => {
    if (isChefForm) {
      setChefForm((prev) => ({ ...prev, [field]: value }));
    } else {
      setCustomerForm((prev) => ({ ...prev, [field]: value }));
    }
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const currentForm = isChefForm ? chefForm : customerForm;

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  return (
    <Container
      maxWidth="lg"
      sx={{ py: 4, minHeight: "100vh", display: "flex", alignItems: "center" }}
    >
      <Paper
        elevation={10}
        sx={{
          width: "100%",
          maxWidth: 1200,
          margin: "0 auto",
          borderRadius: "16px",
          overflow: "hidden",
          animation: `${glitter} 2s ease-in-out infinite`,
          bgcolor: "background.paper",
        }}
      >
        <Grid container sx={{ minHeight: { xs: "auto", md: "700px" } }}>
          {/* Toggle Buttons */}
          <Grid item xs={12}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                p: 2,
                bgcolor: "primary.main",
                position: "relative",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  bgcolor: "white",
                  borderRadius: "25px",
                  p: 0.5,
                  boxShadow: 2,
                }}
              >
                <Button
                  onClick={() => handleToggleForm("chef")}
                  variant={isChefForm ? "contained" : "text"}
                  startIcon={<Restaurant />}
                  sx={{
                    borderRadius: "20px",
                    px: 3,
                    py: 1,
                    color: isChefForm ? "white" : "primary.main",
                    bgcolor: isChefForm ? "primary.main" : "transparent",
                    "&:hover": {
                      bgcolor: isChefForm ? "primary.dark" : "primary.light",
                    },
                  }}
                >
                  I'm a Chef
                </Button>
                <Button
                  onClick={() => handleToggleForm("customer")}
                  variant={!isChefForm ? "contained" : "text"}
                  startIcon={<AccountCircle />}
                  sx={{
                    borderRadius: "20px",
                    px: 3,
                    py: 1,
                    color: !isChefForm ? "white" : "primary.main",
                    bgcolor: !isChefForm ? "primary.main" : "transparent",
                    "&:hover": {
                      bgcolor: !isChefForm ? "primary.dark" : "primary.light",
                    },
                  }}
                >
                  Find a Chef
                </Button>
              </Box>
            </Box>
          </Grid>

          {/* Content */}
          <Slide direction={slideDirection} in={true} timeout={300}>
            <Grid container sx={{ flex: 1 }}>
              {/* Image Section */}
              <Grid
                item
                xs={12}
                md={6}
                sx={{
                  order: { xs: isChefForm ? 1 : 2, md: isChefForm ? 1 : 2 },
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  bgcolor: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  position: "relative",
                }}
              >
                <Box
                  sx={{
                    width: "100%",
                    height: { xs: "300px", md: "100%" },
                    backgroundImage: isChefForm
                      ? "url(https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)"
                      : "url(https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    position: "relative",
                    "&::before": {
                      content: '""',
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      bgcolor: "rgba(0, 0, 0, 0.4)",
                    },
                  }}
                >
                  <Box
                    sx={{
                      position: "absolute",
                      top: "50%",
                      left: "50%",
                      transform: "translate(-50%, -50%)",
                      textAlign: "center",
                      color: "white",
                      zIndex: 1,
                    }}
                  >
                    <Typography
                      variant="h3"
                      component="h1"
                      gutterBottom
                      sx={{ fontWeight: "bold" }}
                    >
                      {isChefForm ? "Join as Chef" : "Find Your Chef"}
                    </Typography>
                    <Typography variant="h6" sx={{ opacity: 0.9 }}>
                      {isChefForm
                        ? "Share your culinary expertise with food lovers"
                        : "Discover amazing chefs for your perfect meal"}
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              {/* Form Section */}
              <Grid
                item
                xs={12}
                md={6}
                sx={{
                  order: { xs: isChefForm ? 2 : 1, md: isChefForm ? 2 : 1 },
                  p: { xs: 3, md: 4 },
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                }}
              >
                <Fade in={true} timeout={500}>
                  <Box>
                    <Typography
                      variant="h4"
                      component="h2"
                      gutterBottom
                      sx={{
                        fontWeight: "bold",
                        color: "primary.main",
                        textAlign: "center",
                        mb: 3,
                      }}
                    >
                      Create Account
                    </Typography>

                    <Box component="form" onSubmit={handleSubmit}>
                      <Grid container spacing={2}>
                        {/* First Name */}
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="First Name"
                            value={currentForm.firstName}
                            onChange={(e) =>
                              updateForm("firstName", e.target.value)
                            }
                            error={!!errors.firstName}
                            helperText={errors.firstName}
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <Person color="action" />
                                </InputAdornment>
                              ),
                            }}
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                borderRadius: "10px",
                              },
                            }}
                            required
                          />
                        </Grid>

                        {/* Last Name */}
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Last Name"
                            value={currentForm.lastName}
                            onChange={(e) =>
                              updateForm("lastName", e.target.value)
                            }
                            error={!!errors.lastName}
                            helperText={errors.lastName}
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <Person color="action" />
                                </InputAdornment>
                              ),
                            }}
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                borderRadius: "10px",
                              },
                            }}
                            required
                          />
                        </Grid>

                        {/* Email */}
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="Email"
                            type="email"
                            value={currentForm.email}
                            onChange={(e) =>
                              updateForm("email", e.target.value)
                            }
                            error={!!errors.email}
                            helperText={errors.email}
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <Email color="action" />
                                </InputAdornment>
                              ),
                            }}
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                borderRadius: "10px",
                              },
                            }}
                            required
                          />
                        </Grid>

                        {/* Phone Number */}
                        <Grid item xs={12} sm={4}>
                          <FormControl fullWidth>
                            <InputLabel>Country Code</InputLabel>
                            <Select
                              value={currentForm.countryCode}
                              onChange={(e) =>
                                updateForm("countryCode", e.target.value)
                              }
                              label="Country Code"
                              sx={{
                                "& .MuiOutlinedInput-root": {
                                  borderRadius: "10px",
                                },
                              }}
                            >
                              {countryCodes.map((country) => (
                                <MenuItem
                                  key={country.code}
                                  value={country.code}
                                >
                                  {country.code} ({country.country})
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        </Grid>

                        <Grid item xs={12} sm={8}>
                          <TextField
                            fullWidth
                            label="Phone Number"
                            value={currentForm.phoneNumber}
                            onChange={(e) =>
                              updateForm("phoneNumber", e.target.value)
                            }
                            error={!!errors.phoneNumber}
                            helperText={errors.phoneNumber}
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <Phone color="action" />
                                </InputAdornment>
                              ),
                            }}
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                borderRadius: "10px",
                              },
                            }}
                            required
                          />
                        </Grid>

                        {/* Username */}
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="Username"
                            value={currentForm.username}
                            onChange={(e) =>
                              updateForm("username", e.target.value)
                            }
                            error={!!errors.username}
                            helperText={errors.username}
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <AccountCircle color="action" />
                                </InputAdornment>
                              ),
                            }}
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                borderRadius: "10px",
                              },
                            }}
                            required
                          />
                        </Grid>

                        {/* Password */}
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Password"
                            type="password"
                            value={currentForm.password}
                            onChange={(e) =>
                              updateForm("password", e.target.value)
                            }
                            error={!!errors.password}
                            helperText={errors.password}
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <Lock color="action" />
                                </InputAdornment>
                              ),
                            }}
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                borderRadius: "10px",
                              },
                            }}
                            required
                          />
                        </Grid>

                        {/* Confirm Password */}
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Confirm Password"
                            type="password"
                            value={currentForm.confirmPassword}
                            onChange={(e) =>
                              updateForm("confirmPassword", e.target.value)
                            }
                            error={!!errors.confirmPassword}
                            helperText={errors.confirmPassword}
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <Lock color="action" />
                                </InputAdornment>
                              ),
                            }}
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                borderRadius: "10px",
                              },
                            }}
                            required
                          />
                        </Grid>

                        {/* Subscription Plans (Customer only) */}
                        {!isChefForm && (
                          <Grid item xs={12}>
                            <Typography
                              variant="subtitle1"
                              gutterBottom
                              sx={{
                                color: "text.primary",
                                fontWeight: 600,
                                fontSize: "1rem",
                                mb: 2,
                              }}
                            >
                              Select Subscription Plan
                            </Typography>
                            <RadioGroup
                              value={customerForm.subscriptionPlan}
                              onChange={(e) =>
                                updateForm("subscriptionPlan", e.target.value)
                              }
                              row
                            >
                              <Grid container spacing={1.5}>
                                {loading ? (
                                  <Grid item xs={12}>
                                    <Typography
                                      variant="body2"
                                      color="text.secondary"
                                      textAlign="center"
                                    >
                                      Loading subscription plans...
                                    </Typography>
                                  </Grid>
                                ) : subscriptionPlans.length > 0 ? (
                                  subscriptionPlans.map((plan) => (
                                    <Grid item xs={12} sm={4} key={plan.id}>
                                      <Card
                                        variant="outlined"
                                        sx={{
                                          cursor: "pointer",
                                          border: 1,
                                          borderColor:
                                            customerForm.subscriptionPlan ===
                                            plan.id.toString()
                                              ? "primary.main"
                                              : "grey.300",
                                          "&:hover": {
                                            borderColor: "primary.main",
                                            boxShadow: 1,
                                          },
                                          borderRadius: "8px",
                                          position: "relative",
                                          minHeight: "80px",
                                          transition: "all 0.2s ease",
                                        }}
                                        onClick={() =>
                                          updateForm(
                                            "subscriptionPlan",
                                            plan.id.toString()
                                          )
                                        }
                                      >
                                        <CardContent
                                          sx={{
                                            p: 2,
                                            "&:last-child": { pb: 2 },
                                            position: "relative",
                                            height: "100%",
                                          }}
                                        >
                                          <Radio
                                            checked={
                                              customerForm.subscriptionPlan ===
                                              plan.id.toString()
                                            }
                                            value={plan.id.toString()}
                                            size="small"
                                            sx={{
                                              position: "absolute",
                                              top: 4,
                                              left: 4,
                                              p: 0.5,
                                            }}
                                          />

                                          <Typography
                                            variant="body1"
                                            sx={{
                                              fontWeight: 600,
                                              color: "text.primary",
                                              fontSize: "0.9rem",
                                              ml: 4,
                                              mt: 0.5,
                                            }}
                                          >
                                            {plan.duration}
                                          </Typography>

                                          <Typography
                                            variant="body2"
                                            sx={{
                                              fontWeight: 500,
                                              color: "primary.main",
                                              fontSize: "0.85rem",
                                              ml: 4,
                                              mt: 0.5,
                                            }}
                                          >
                                            Rs. {plan.price.toLocaleString()}
                                          </Typography>
                                        </CardContent>
                                      </Card>
                                    </Grid>
                                  ))
                                ) : (
                                  <Grid item xs={12}>
                                    <Typography
                                      variant="body2"
                                      color="text.secondary"
                                      textAlign="center"
                                    >
                                      No subscription plans available
                                    </Typography>
                                  </Grid>
                                )}
                                {/* {subscriptionPlans.map((plan) => (
                                  
                                ))} */}
                              </Grid>
                            </RadioGroup>
                            {errors.subscriptionPlan && (
                              <Alert
                                severity="error"
                                sx={{ mt: 1, fontSize: "0.875rem" }}
                              >
                                {errors.subscriptionPlan}
                              </Alert>
                            )}
                          </Grid>
                        )}

                        {/* Terms and Conditions */}
                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={currentForm.agreeTerms}
                                onChange={(e) =>
                                  updateForm("agreeTerms", e.target.checked)
                                }
                                color="primary"
                              />
                            }
                            label={
                              <Typography variant="body2">
                                I agree to the{" "}
                                <Link
                                  href="#"
                                  color="primary"
                                  underline="hover"
                                >
                                  Terms and Conditions
                                </Link>
                              </Typography>
                            }
                          />
                          {errors.agreeTerms && (
                            <Alert severity="error" sx={{ mt: 1 }}>
                              {errors.agreeTerms}
                            </Alert>
                          )}
                        </Grid>

                        {/* Submit Button */}
                        <Grid item xs={12}>
                          <Button
                            type="submit"
                            fullWidth
                            variant="contained"
                            size="large"
                            disabled={submitLoading}
                            sx={{
                              py: 1.5,
                              borderRadius: "10px",
                              fontSize: "1.1rem",
                              fontWeight: "bold",
                              bgcolor: "primary.main",
                              "&:hover": {
                                bgcolor: "primary.dark",
                                transform: "translateY(-2px)",
                                boxShadow: 4,
                              },
                              transition: "all 0.3s ease",
                            }}
                          >
                            {submitLoading
                              ? "Creating Account..."
                              : isChefForm
                                ? "Join as Chef"
                                : "Continue"}
                          </Button>
                        </Grid>

                        {/* Login Link */}
                        <Grid item xs={12}>
                          <Box sx={{ textAlign: "center", mt: 2 }}>
                            <Typography variant="body2" color="text.secondary">
                              Already have an account?{" "}
                              <Link
                                href="/auth/login"
                                color="primary"
                                underline="hover"
                                sx={{ fontWeight: "bold" }}
                              >
                                Log In
                              </Link>
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  </Box>
                </Fade>
              </Grid>
            </Grid>
          </Slide>
        </Grid>
      </Paper>
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Register;
