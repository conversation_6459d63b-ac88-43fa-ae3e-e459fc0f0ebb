import React, { useState, useEffect } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Typography,
  Chip,
  Stack,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
} from "@mui/material";
import { Visibility, Delete, Search } from "@mui/icons-material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { bookingData } from "../../../Dummydata/bookingData";
import BookingDetailsDialog from "../../Admin/Bookings/ViewBooking";

const PendingBookings = () => {
  const [bookings, setBookings] = useState([]);
  const [filteredBookings, setFilteredBookings] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [statusFilter, setStatusFilter] = useState("pending");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  // Load pending booking data on component mount
  useEffect(() => {
    const pendingBookings = bookingData.filter(booking => booking.bookingStatus === 'Pending');
    setBookings(pendingBookings);
    setFilteredBookings(pendingBookings);
  }, []);

  // Filter bookings based on search term, date range, and status
  useEffect(() => {
    let filtered = bookings;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (booking) =>
          booking.customer[0].name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          booking.customer[0].email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          booking.customer[0].phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
          booking.chef[0].name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          booking.bookingStatus.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Date range filter
    if (startDate || endDate) {
      filtered = filtered.filter((booking) => {
        const bookingDate = new Date(booking.bookingDate);
        const start = startDate ? new Date(startDate) : new Date("1900-01-01");
        const end = endDate ? new Date(endDate) : new Date("2100-12-31");
        return bookingDate >= start && bookingDate <= end;
      });
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter((booking) => {
        if (statusFilter === "pending") return booking.bookingStatus.toLowerCase() === "pending";
        if (statusFilter === "confirmed") return booking.bookingStatus.toLowerCase() === "confirmed";
        return true;
      });
    }

    setFilteredBookings(filtered);
    setPage(0); // Reset page when filtering
  }, [searchTerm, startDate, endDate, statusFilter, bookings]);

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleView = (bookingId) => {
    // Find the booking by ID
    const booking = bookings.find(b => b.id === bookingId);
    if (booking) {
      setSelectedBooking(booking);
      setDialogOpen(true);
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedBooking(null);
  };

  const handleStatusChange = (bookingId, newStatus) => {
    const updatedBookings = bookings.map(booking => 
      booking.id === bookingId ? { ...booking, bookingStatus: newStatus } : booking
    );
    setBookings(updatedBookings);
  };

  const handleDelete = (bookingId) => {
    console.log("Delete booking:", bookingId);
    setBookings(bookings.filter(booking => booking.id !== bookingId));
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return {
          backgroundColor: "#E8F5E8",
          color: "#2E7D32",
          borderColor: "#4CAF50",
        };
      case "pending":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFB74D",
        };
      case "completed":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      case "cancelled":
        return {
          backgroundColor: "#FFEBEE",
          color: "#D32F2F",
          borderColor: "#FFCDD2",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      }) +
      "; " +
      date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    );
  };

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':');
    const hour12 = parseInt(hours) % 12 || 12;
    const ampm = parseInt(hours) >= 12 ? 'PM' : 'AM';
    return `${hour12}:${minutes} ${ampm}`;
  };

  // Mobile Card View Component
  const BookingCard = ({ booking }) => (
    <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
            {booking.customer[0].name}
          </Typography>
          <FormControl size="small" sx={{ minWidth: 100 }}>
            <Select
              value={booking.bookingStatus.toLowerCase()}
              onChange={(e) => handleStatusChange(booking.id, e.target.value === 'confirmed' ? 'Confirmed' : 'Pending')}
              sx={{
                height: 28,
                fontSize: "11px",
                fontWeight: "500",
                ...getStatusColor(booking.bookingStatus),
                border: "1px solid",
                "& .MuiOutlinedInput-notchedOutline": {
                  border: "none",
                },
                "& .MuiSelect-select": {
                  padding: "4px 8px !important",
                },
              }}
            >
              <MenuItem value="pending" sx={{ fontSize: "11px" }}>Pending</MenuItem>
              <MenuItem value="confirmed" sx={{ fontSize: "11px" }}>Confirmed</MenuItem>
            </Select>
          </FormControl>
        </Box>
        
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Booking Date:</strong> {formatDateTime(booking.bookingDate)}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Booking Time:</strong> {formatTime(booking.bookingTime)}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Customer Email:</strong> {booking.customer[0].email}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Customer Phone:</strong> {booking.customer[0].phone}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
          <strong>Chef:</strong> {booking.chef[0].name}
        </Typography>

        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <IconButton
            onClick={() => handleView(booking.id)}
            sx={{
              color: "#3a86ff",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Visibility sx={{ fontSize: 18 }} />
          </IconButton>
          <IconButton
            onClick={() => handleDelete(booking.id)}
            sx={{
              color: "#d00000",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Delete sx={{ fontSize: 18 }} />
          </IconButton>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="xl" sx={{ py: 1 }}>
        <Typography
          variant="h4"
          sx={{ mb: 3, color: "#333", fontWeight: "600", fontSize: isMobile ? '24px' : '32px' }}
        >
          Pending Bookings
        </Typography>

        {/* Search and Filters Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            p: 2,
            mb: 2,
          }}
        >
          <Grid container spacing={2}>
            {/* Search Bar */}
            <Grid item xs={12} md={6} lg={4}>
              <TextField
                fullWidth
                placeholder="Search bookings..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ color: '#666', mr: 1 }} />,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E0E0E0',
                    },
                  },
                }}
              />
            </Grid>

            {/* Start Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={3}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E0E0E0',
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* End Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={3}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E0E0E0',
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* Clear Filters Button */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm("");
                  setStartDate(null);
                  setEndDate(null);
                  setStatusFilter("pending");
                }}
                sx={{
                  height: '48px',
                  borderColor: '#E0E0E0',
                  color: '#666',
                  '&:hover': {
                    borderColor: '#1976D2',
                    backgroundColor: '#F5F5F5',
                  },
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Table Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            overflow: "hidden",
          }}
        >
          {/* Desktop/Tablet Table View */}
          {!isMobile ? (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Booking Date
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Booking Time
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Customer Name
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Customer Email
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                          display: isTablet ? 'none' : 'table-cell',
                        }}
                      >
                        Customer Phone
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Chef Name
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Booking Status
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredBookings
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((booking, index) => (
                        <TableRow
                          key={booking.id}
                          hover
                          sx={{
                            backgroundColor: "#fff",
                            "&:hover": {
                              backgroundColor: "#F8F9FA",
                            },
                            borderBottom: "1px solid #F0F0F0",
                          }}
                        >
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "400",
                            }}
                          >
                            {formatDateTime(booking.bookingDate)}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "400",
                            }}
                          >
                            {formatTime(booking.bookingTime)}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "500",
                            }}
                          >
                            {booking.customer[0].name}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                            }}
                          >
                            {booking.customer[0].email}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              display: isTablet ? 'none' : 'table-cell',
                            }}
                          >
                            {booking.customer[0].phone}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "500",
                            }}
                          >
                            {booking.chef[0].name}
                          </TableCell>
                          <TableCell>
                            <FormControl size="small" sx={{ minWidth: 100 }}>
                              <Select
                                value={booking.bookingStatus.toLowerCase()}
                                onChange={(e) => handleStatusChange(booking.id, e.target.value === 'confirmed' ? 'Confirmed' : 'Pending')}
                                sx={{
                                  height: 28,
                                  fontSize: "11px",
                                  fontWeight: "500",
                                  ...getStatusColor(booking.bookingStatus),
                                  border: "1px solid",
                                  "& .MuiOutlinedInput-notchedOutline": {
                                    border: "none",
                                  },
                                  "& .MuiSelect-select": {
                                    padding: "4px 8px !important",
                                  },
                                }}
                              >
                                <MenuItem value="pending" sx={{ fontSize: "11px" }}>Pending</MenuItem>
                                <MenuItem value="confirmed" sx={{ fontSize: "11px" }}>Confirmed</MenuItem>
                              </Select>
                            </FormControl>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={0.5}>
                              <IconButton
                                onClick={() => handleView(booking.id)}
                                sx={{
                                  color: "#3a86ff",
                                  "&:hover": { backgroundColor: "#F5F5F5" },
                                  padding: "4px",
                                }}
                                size="small"
                              >
                                <Visibility sx={{ fontSize: 16 }} />
                              </IconButton>
                              <IconButton
                                onClick={() => handleDelete(booking.id)}
                                sx={{
                                  color: "#d00000",
                                  "&:hover": { backgroundColor: "#F5F5F5" },
                                  padding: "4px",
                                }}
                                size="small"
                              >
                                <Delete sx={{ fontSize: 16 }} />
                              </IconButton>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))}
                    {filteredBookings.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={8} sx={{ textAlign: "center", py: 4 }}>
                          <Typography variant="body1" color="textSecondary">
                            No pending bookings found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          ) : (
            /* Mobile Card View */
            <Box sx={{ p: 2 }}>
              {filteredBookings
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((booking) => (
                  <BookingCard key={booking.id} booking={booking} />
                ))}
              {filteredBookings.length === 0 && (
                <Box sx={{ textAlign: "center", py: 4 }}>
                  <Typography variant="body1" color="textSecondary">
                    No pending bookings found
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {/* Custom Pagination */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              p: 2,
              borderTop: "1px solid #E0E0E0",
              backgroundColor: "#FAFAFA",
              flexDirection: isMobile ? 'column' : 'row',
              gap: isMobile ? 2 : 0,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
              {[1, 2, 3, 4].map((pageNum) => (
                <Button
                  key={pageNum}
                  variant={page + 1 === pageNum ? "contained" : "text"}
                  onClick={() => setPage(pageNum - 1)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: 1,
                    fontSize: "14px",
                    ...(page + 1 === pageNum
                      ? {
                          backgroundColor: "#1976D2",
                          color: "white",
                          "&:hover": {
                            backgroundColor: "#1565C0",
                          },
                        }
                      : {
                          color: "#666",
                          "&:hover": {
                            backgroundColor: "#F5F5F5",
                          },
                        }),
                  }}
                >
                  {pageNum}
                </Button>
              ))}
              <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
                ...
              </Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
              <Typography variant="body2" sx={{ color: "#666" }}>
                Go to page
              </Typography>
              <TextField
                size="small"
                type="number"
                value={page + 1}
                onChange={(e) =>
                  setPage(Math.max(0, Number(e.target.value) - 1))
                }
                sx={{
                  width: 60,
                  "& .MuiOutlinedInput-root": {
                    height: 32,
                    "& fieldset": {
                      borderColor: "#E0E0E0",
                    },
                  },
                }}
                inputProps={{
                  min: 1,
                  max: Math.ceil(filteredBookings.length / rowsPerPage),
                }}
              />
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography variant="body2" color="#666">
                  Show
                </Typography>
                <FormControl size="small" sx={{ minWidth: 80 }}>
                  <Select
                    value={rowsPerPage}
                    onChange={handleChangeRowsPerPage}
                    displayEmpty
                    sx={{
                      backgroundColor: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E0E0E0",
                      },
                    }}
                  >
                    <MenuItem value={5}>5 Row</MenuItem>
                    <MenuItem value={8}>8 Row</MenuItem>
                    <MenuItem value={10}>10 Row</MenuItem>
                    <MenuItem value={25}>25 Row</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Box>
          </Box>
        </Paper>

        {/* Booking Details Dialog */}
        <BookingDetailsDialog 
          open={dialogOpen}
          onClose={handleCloseDialog}
          booking={selectedBooking}
        />
      </Container>
    </LocalizationProvider>
  );
};

export default PendingBookings;