import { useState, useCallback, useEffect } from "react";
import {
  Box,
  Paper,
  Typography,
  Container,
  Avatar,
  Tab,
  Tabs,
  Grid,
  useMediaQuery,
  useTheme,
  Stack,
  Divider,
} from "@mui/material";
import {
  Person,
  Event,
  Payment,
  Email,
  LocationOn,
  AccountCircle,
} from "@mui/icons-material";
import VerifiedIcon from "@mui/icons-material/Verified";
import NewReleasesIcon from "@mui/icons-material/NewReleases";
import PersonalInfoTab from "../../../components/Chefs/MyProfile/PersonalInfoTab";
import SecurityInfoTab from "../../../components/Chefs/MyProfile/SecurityInfoTab";
import ProfessionalInfoTab from "../../../components/Chefs/MyProfile/ProfessionalInfoTab";
import {
  updateChefGeneralInfo,
  getChefGeneralInfoById,
} from "../../../app/service/chef.service";
import { useSelector } from "react-redux";
import { selectUser } from "../../../reducers/auth.reducer";
import { apiPostFormData } from "../../../api/apiManager";
import { ThreeDot } from "react-loading-indicators";

const LoadingFallback = () => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    height="100vh"
  >
    <ThreeDot
      variant="pulsate"
      color="#E16969"
      size="medium"
      text=""
      textColor=""
    />
  </Box>
);
const MyProfile = () => {
  const [chef, setChef] = useState(null);
  const [loading, setLoading] = useState(true);

  // Get chef ID from somewhere (props, params, context, etc.)

  const user = useSelector(selectUser);
  const [currentTab, setCurrentTab] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const isExtraSmall = useMediaQuery(theme.breakpoints.down(480));
  const fetchChefData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getChefGeneralInfoById(user.id);

      if (response.responseCode === 1000) {
        const chefData = response.data.chefGeneralInfo;

        // Transform API data to match component structure
        const transformedChef = {
          id: chefData.id,
          name: chefData.name,
          username: chefData.username,
          title: chefData.title,
          email: chefData.email,
          phoneNumber: chefData.phoneNumber,
          address: chefData.address,
          postalCode: chefData.postalCode,
          country: chefData.country,
          city: chefData.city,
          countryCode: chefData.countryCode,
          hourlyRate: chefData.hourlyRate,
          location: chefData.city || "Not Set",
          joinDate: chefData.joinDate,
          verified: chefData.verified,
          imagePath: chefData.image,
          image: chefData.image
            ? `http://localhost:3005/api/chef/get-chef-profile-image/${chefData.image}`
            : "",
          description: chefData.about,
          balance: chefData.balance,
          rating: chefData.rating?.stars || 0,
          reviews: 0, // Not provided in API
          views: 0, // Not provided in API
          // Add other fields as needed
        };

        setChef(transformedChef);
      }
    } catch (error) {
      console.error("Error fetching chef data:", error);
    } finally {
      setLoading(false);
    }
  }, [user.id]);

  useEffect(() => {
    fetchChefData();
  }, [fetchChefData]);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleChefUpdate = useCallback(
    async (updatedData, tabIndex) => {
      try {
        if (tabIndex === 0) {
          // Personal Info Tab
          // Transform data to API payload format
          const payload = {
            chef_first_name:
              updatedData.firstName || updatedData.name?.split(" ")[0] || "",
            chef_last_name:
              updatedData.lastName ||
              updatedData.name?.split(" ").slice(1).join(" ") ||
              "",
            email: updatedData.email,
            country_code: updatedData.countryCode,
            phone_number: updatedData.phoneNumber,
            country: updatedData.country,
            chef_title: updatedData.chefTitle || updatedData.title,
            description: updatedData.description,
            profile_photo: updatedData.image,
            address: updatedData.address,
            postalCode: updatedData.postalCode,
            city: updatedData.city,
          };
          console.log(payload);
          const response = await updateChefGeneralInfo(user.id, payload);

          if (response.responseCode === 1000) {
            // Refresh chef data after successful update
            await fetchChefData();
            return { success: true, message: "Profile updated successfully!" };
          } else {
            return {
              success: false,
              message: "Failed to update profile. Please try again.",
            };
          }
          // return { success: true, message: "Profile updated successfully!" };
        } else if (tabIndex === 2) {
          
          await fetchChefData(); // Refresh data after update
          return {
            success: true,
            message: "Professional information updated successfully!",
          };
        }

        // Handle other tabs later
        return { success: false, message: "Tab not implemented yet." };
      } catch (error) {
        console.error("Error updating chef:", error);
        return {
          success: false,
          message: "Failed to update profile. Please try again.",
        };
      }
    },
    [user.id, fetchChefData]
  );

  const getInitials = (name) => {
    console.log(name);
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  if (loading) {
    return (
      <Container
        maxWidth="xl"
        sx={{ py: { xs: 1, sm: 1 }, px: { xs: 1, sm: 2, md: 3 } }}
      >
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="400px"
        >
          <LoadingFallback />
        </Box>
      </Container>
    );
  }

  if (!chef) {
    return (
      <Container
        maxWidth="xl"
        sx={{ py: { xs: 1, sm: 1 }, px: { xs: 1, sm: 2, md: 3 } }}
      >
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="400px"
        >
          <Typography>Chef not found</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container
      maxWidth="xl"
      sx={{
        py: { xs: 1, sm: 1 },
        px: { xs: 1, sm: 2, md: 3 },
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          mb: { xs: 2, md: 3 },
          flexDirection: { xs: "column", sm: "row" },
          gap: { xs: 1, sm: 0 },
        }}
      >
        <Typography
          variant={isSmallScreen ? "h5" : "h4"}
          sx={{
            fontWeight: "600",
            color: "#333",
            fontSize: { xs: "1.2rem", sm: "1.5rem", md: "1.6rem" },
            textAlign: { xs: "center", sm: "left" },
            wordBreak: "break-word",
          }}
        >
          My Profile
        </Typography>
      </Box>

      <Grid container spacing={{ xs: 2, md: 3 }}>
        {/* Left Side - Profile Info */}
        <Grid item xs={12} md={4} lg={3}>
          <Paper
            sx={{
              p: { xs: 2, sm: 3 },
              border: "1px solid #E0E0E0",
              borderRadius: 2,
            }}
          >
            {/* Profile Picture and Basic Info */}
            <Box sx={{ textAlign: "center", mb: 3 }}>
              <Avatar
                src={chef.image}
                sx={{
                  width: { xs: 80, sm: 100, md: 120 },
                  height: { xs: 80, sm: 100, md: 120 },
                  mx: "auto",
                  mb: 2,
                  fontSize: { xs: "1.2rem", sm: "1.5rem", md: "2rem" },
                  backgroundColor: "#1e3a8a",
                  color: "white",
                }}
              >
                {getInitials(chef.name)}
              </Avatar>

              <Typography
                variant={isSmallScreen ? "h6" : "h5"}
                sx={{
                  fontWeight: "600",
                  mb: 1,
                  fontSize: { xs: "1.1rem", sm: "1.25rem", md: "1.2rem" },
                  wordBreak: "break-word",
                }}
              >
                {chef.name}
              </Typography>

              <Typography
                variant="body2"
                color="textSecondary"
                sx={{
                  mb: 2,
                  fontSize: { xs: "0.8rem", sm: "0.875rem" },
                  wordBreak: "break-word",
                }}
              >
                {chef.title}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  mb: 2,
                }}
              >
                {chef.verified ? (
                  <VerifiedIcon
                    sx={{ color: "green", fontSize: { xs: 20, sm: 24 } }}
                  />
                ) : (
                  <NewReleasesIcon
                    sx={{ color: "red", fontSize: { xs: 20, sm: 24 } }}
                  />
                )}
              </Box>

              <Typography
                variant="body2"
                color="textSecondary"
                sx={{
                  mb: 1,
                  fontWeight: "600",
                  color: "blue",
                  fontSize: { xs: "0.8rem", sm: "0.875rem" },
                }}
              >
                {chef.hourlyRate ? `$${chef.hourlyRate}/hr` : "N/A"}
              </Typography>
            </Box>

            <Divider sx={{ mb: 3 }} />

            {/* Chef Details Section */}
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: "600",
                  mb: 2,
                  fontSize: { xs: "1rem", sm: "1.1rem" },
                  color: "#333",
                }}
              >
                Profile Details
              </Typography>

              {/* Name */}
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <Person
                  sx={{ color: "#6C5CE7", mr: 1, fontSize: { xs: 18, sm: 20 } }}
                />
                <Box>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: "500",
                      fontSize: { xs: "0.75rem", sm: "0.8rem" },
                      color: "#666",
                    }}
                  >
                    Full Name
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: "400",
                      fontSize: { xs: "0.8rem", sm: "0.875rem" },
                      wordBreak: "break-word",
                    }}
                  >
                    {chef.name}
                  </Typography>
                </Box>
              </Box>

              {/* Username */}
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <AccountCircle
                  sx={{ color: "#6C5CE7", mr: 1, fontSize: { xs: 18, sm: 20 } }}
                />
                <Box>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: "500",
                      fontSize: { xs: "0.75rem", sm: "0.8rem" },
                      color: "#666",
                    }}
                  >
                    Username
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: "400",
                      fontSize: { xs: "0.8rem", sm: "0.875rem" },
                      wordBreak: "break-word",
                    }}
                  >
                    @{chef.username}
                  </Typography>
                </Box>
              </Box>

              {/* Email */}
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <Email
                  sx={{ color: "#6C5CE7", mr: 1, fontSize: { xs: 18, sm: 20 } }}
                />
                <Box>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: "500",
                      fontSize: { xs: "0.75rem", sm: "0.8rem" },
                      color: "#666",
                    }}
                  >
                    Email
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: "400",
                      fontSize: { xs: "0.8rem", sm: "0.875rem" },
                      wordBreak: "break-all",
                    }}
                  >
                    {chef.email}
                  </Typography>
                </Box>
              </Box>

              {/* Location */}
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <LocationOn
                  sx={{ color: "#6C5CE7", mr: 1, fontSize: { xs: 18, sm: 20 } }}
                />
                <Box>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: "500",
                      fontSize: { xs: "0.75rem", sm: "0.8rem" },
                      color: "#666",
                    }}
                  >
                    Location
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: "400",
                      fontSize: { xs: "0.8rem", sm: "0.875rem" },
                      wordBreak: "break-word",
                    }}
                  >
                    {chef.location}
                  </Typography>
                </Box>
              </Box>
            </Box>

            <Divider sx={{ mb: 3 }} />

            {/* Wallet Cards */}
            <Box sx={{ mb: 2 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: "600",
                  mb: 2,
                  fontSize: { xs: "1rem", sm: "1.1rem" },
                  color: "#333",
                }}
              >
                Wallet Balance
              </Typography>
            </Box>

            <Stack spacing={2}>
              <Paper
                sx={{
                  p: { xs: 1.5, sm: 2 },
                  background:
                    "linear-gradient(135deg, #0EA5E9 0%, #0284C7 100%)",
                  color: "white",
                  borderRadius: 2,
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                  <Box
                    sx={{
                      width: { xs: 20, sm: 24 },
                      height: { xs: 14, sm: 16 },
                      backgroundColor: "#FCD34D",
                      borderRadius: 1,
                      mr: 1,
                    }}
                  />
                  <Typography
                    variant="body2"
                    sx={{ fontSize: { xs: "0.75rem", sm: "0.875rem" } }}
                  >
                    Main Wallet
                  </Typography>
                </Box>
                <Typography
                  variant={isSmallScreen ? "h6" : "h5"}
                  sx={{
                    fontWeight: "600",
                    fontSize: { xs: "1.1rem", sm: "1.25rem", md: "1.5rem" },
                  }}
                >
                  Rs {chef?.balance?.toLocaleString() || "0"}
                </Typography>
              </Paper>

              <Paper
                sx={{
                  p: { xs: 1.5, sm: 2 },
                  background:
                    "linear-gradient(135deg, #1E3A8A 0%, #1E40AF 100%)",
                  color: "white",
                  borderRadius: 2,
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                  <Box
                    sx={{
                      width: { xs: 20, sm: 24 },
                      height: { xs: 14, sm: 16 },
                      backgroundColor: "#FCD34D",
                      borderRadius: 1,
                      mr: 1,
                    }}
                  />
                  <Typography
                    variant="body2"
                    sx={{ fontSize: { xs: "0.75rem", sm: "0.875rem" } }}
                  >
                    Profit Wallet
                  </Typography>
                </Box>
                <Typography
                  variant="body2"
                  sx={{
                    opacity: 0.9,
                    fontSize: { xs: "0.75rem", sm: "0.875rem" },
                  }}
                >
                  USD
                </Typography>
                <Typography
                  variant={isSmallScreen ? "h6" : "h5"}
                  sx={{
                    fontWeight: "600",
                    fontSize: { xs: "1.1rem", sm: "1.25rem", md: "1.5rem" },
                  }}
                >
                  $0
                </Typography>
              </Paper>
            </Stack>
          </Paper>
        </Grid>

        {/* Right Side - Tabs Content */}
        <Grid item xs={12} md={8} lg={9}>
          <Paper
            sx={{
              border: "1px solid #E0E0E0",
              borderRadius: 2,
              overflow: "hidden",
            }}
          >
            {/* Tabs Header */}
            <Box sx={{ borderBottom: "1px solid #E0E0E0" }}>
              <Tabs
                value={currentTab}
                onChange={handleTabChange}
                variant={isMobile ? "scrollable" : "fullWidth"}
                scrollButtons="auto"
                allowScrollButtonsMobile
                sx={{
                  "& .MuiTab-root": {
                    textTransform: "none",
                    fontWeight: "500",
                    color: "#666",
                    fontSize: { xs: "0.75rem", sm: "0.875rem" },
                    minWidth: { xs: "auto", sm: 160 },
                    padding: { xs: "8px 12px", sm: "12px 16px" },
                    "&.Mui-selected": {
                      color: "#6C5CE7",
                      backgroundColor: "#F8F7FF",
                    },
                  },
                  "& .MuiTabs-indicator": {
                    backgroundColor: "#6C5CE7",
                  },
                  "& .MuiTab-iconWrapper": {
                    fontSize: { xs: "1rem", sm: "1.2rem" },
                  },
                }}
              >
                <Tab
                  icon={<Person />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Personal Information"
                  sx={{
                    minHeight: { xs: 48, sm: 64 },
                    "& .MuiTab-iconWrapper": {
                      marginBottom: isExtraSmall ? "4px" : 0,
                      marginRight: isExtraSmall ? 0 : "8px",
                    },
                  }}
                />
                <Tab
                  icon={<Event />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Security Information"
                  sx={{
                    minHeight: { xs: 48, sm: 64 },
                    "& .MuiTab-iconWrapper": {
                      marginBottom: isExtraSmall ? "4px" : 0,
                      marginRight: isExtraSmall ? 0 : "8px",
                    },
                  }}
                />
                <Tab
                  icon={<Payment />}
                  iconPosition={isExtraSmall ? "top" : "start"}
                  label="Professional Information"
                  sx={{
                    minHeight: { xs: 48, sm: 64 },
                    "& .MuiTab-iconWrapper": {
                      marginBottom: isExtraSmall ? "4px" : 0,
                      marginRight: isExtraSmall ? 0 : "8px",
                    },
                  }}
                />
              </Tabs>
            </Box>

            {/* Tab Content */}
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                minHeight: { xs: "300px", sm: "400px" },
              }}
            >
              {currentTab === 0 && chef && (
                <PersonalInfoTab
                  chef={chef}
                  onUpdate={(updatedData) => handleChefUpdate(updatedData, 0)}
                />
              )}
              {currentTab === 1 && (
                <SecurityInfoTab chef={chef} onUpdate={handleChefUpdate} />
              )}
              {currentTab === 2 && (
                <ProfessionalInfoTab chef={chef} onUpdate={handleChefUpdate} />
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default MyProfile;
