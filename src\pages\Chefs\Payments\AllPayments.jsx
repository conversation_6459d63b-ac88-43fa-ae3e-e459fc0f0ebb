import React, { useState, useEffect } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Typography,
  Chip,
  Stack,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
} from "@mui/material";
import { Visibility, Delete, Search } from "@mui/icons-material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";

// Sample payment data - replace with your actual data source
const paymentData = [
  {
    id: 1,
    paymentDate: "2024-12-15T10:30:00Z",
    amount: 125.50,
    customerName: "<PERSON>",
    customerPhone: "+****************",
    paymentStatus: "Completed"
  },
  {
    id: 2,
    paymentDate: "2024-12-14T15:45:00Z",
    amount: 89.99,
    customerName: "<PERSON>",
    customerPhone: "+****************",
    paymentStatus: "Pending"
  },
  {
    id: 3,
    paymentDate: "2024-12-13T09:15:00Z",
    amount: 256.75,
    customerName: "Michael Brown",
    customerPhone: "+****************",
    paymentStatus: "Failed"
  },
  {
    id: 4,
    paymentDate: "2024-12-12T14:20:00Z",
    amount: 175.00,
    customerName: "Emily Davis",
    customerPhone: "+****************",
    paymentStatus: "Completed"
  },
  {
    id: 5,
    paymentDate: "2024-12-11T11:00:00Z",
    amount: 99.50,
    customerName: "David Wilson",
    customerPhone: "+****************",
    paymentStatus: "Refunded"
  },
  {
    id: 6,
    paymentDate: "2024-12-10T16:30:00Z",
    amount: 320.25,
    customerName: "Lisa Anderson",
    customerPhone: "+****************",
    paymentStatus: "Completed"
  },
  {
    id: 7,
    paymentDate: "2024-12-09T13:45:00Z",
    amount: 67.80,
    customerName: "Robert Taylor",
    customerPhone: "+****************",
    paymentStatus: "Pending"
  },
  {
    id: 8,
    paymentDate: "2024-12-08T08:15:00Z",
    amount: 198.40,
    customerName: "Jennifer Martinez",
    customerPhone: "+****************",
    paymentStatus: "Completed"
  },
  {
    id: 9,
    paymentDate: "2024-12-07T17:00:00Z",
    amount: 145.60,
    customerName: "Christopher Lee",
    customerPhone: "+****************",
    paymentStatus: "Failed"
  },
  {
    id: 10,
    paymentDate: "2024-12-06T12:30:00Z",
    amount: 78.90,
    customerName: "Amanda White",
    customerPhone: "+****************",
    paymentStatus: "Completed"
  }
];

const AllPayments = () => {
  const [payments, setPayments] = useState([]);
  const [filteredPayments, setFilteredPayments] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [statusFilter, setStatusFilter] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);

  const [selectedPayment, setSelectedPayment] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const isTablet = useMediaQuery(theme.breakpoints.down("lg"));

  // Loading and error states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load payment data on component mount
  useEffect(() => {
    setPayments(paymentData);
    setFilteredPayments(paymentData);
  }, []);

  // Filter payments based on search term, date range, and status
  useEffect(() => {
    let filtered = payments;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (payment) =>
          payment.customerName
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          payment.customerPhone
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          payment.amount.toString().includes(searchTerm) ||
          payment.paymentStatus.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Date range filter
    if (startDate || endDate) {
      filtered = filtered.filter((payment) => {
        const paymentDate = new Date(payment.paymentDate);
        const start = startDate ? new Date(startDate) : new Date("1900-01-01");
        const end = endDate ? new Date(endDate) : new Date("2100-12-31");
        return paymentDate >= start && paymentDate <= end;
      });
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter(
        (payment) => payment.paymentStatus === statusFilter
      );
    }

    setFilteredPayments(filtered);
    setPage(0); // Reset page when filtering
  }, [searchTerm, startDate, endDate, statusFilter, payments]);

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleView = (paymentId) => {
    const payment = payments.find((p) => p.id === paymentId);
    setSelectedPayment(payment);
    console.log("View payment:", payment);
    // Add view functionality here - you can open a dialog or navigate to a detail page
  };

  const handleDelete = (paymentId) => {
    console.log("Delete payment:", paymentId);
    // Add delete functionality here
    // Example: Remove from state or call API
    // setPayments(payments.filter(p => p.id !== paymentId));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "Completed":
        return {
          backgroundColor: "#E8F5E8",
          color: "#2E7D32",
          borderColor: "#A5D6A7",
        };
      case "Pending":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFCC02",
        };
      case "Failed":
        return {
          backgroundColor: "#FFEBEE",
          color: "#D32F2F",
          borderColor: "#EF9A9A",
        };
      case "Refunded":
        return {
          backgroundColor: "#E3F2FD",
          color: "#1976D2",
          borderColor: "#BBDEFB",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      }) +
      "; " +
      date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    );
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getPaymentStatuses = () => {
    const statuses = [
      ...new Set(payments.map((payment) => payment.paymentStatus)),
    ];
    return statuses;
  };

  // Mobile Card View Component
  const PaymentCard = ({ payment }) => (
    <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
      <CardContent sx={{ p: 2 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-start",
            mb: 2,
          }}
        >
          <Typography
            variant="h6"
            sx={{ fontSize: "16px", fontWeight: "500", color: "#333" }}
          >
            {formatCurrency(payment.amount)}
          </Typography>
          <Chip
            label={payment.paymentStatus}
            size="small"
            sx={{
              ...getStatusColor(payment.paymentStatus),
              border: "1px solid",
              fontWeight: "500",
              fontSize: "11px",
            }}
          />
        </Box>

        <Typography variant="body2" sx={{ color: "#666", mb: 1 }}>
          <strong>Customer:</strong> {payment.customerName}
        </Typography>
        <Typography variant="body2" sx={{ color: "#666", mb: 1 }}>
          <strong>Phone:</strong> {payment.customerPhone}
        </Typography>
        <Typography variant="body2" sx={{ color: "#666", mb: 2 }}>
          <strong>Payment Date:</strong> {formatDateTime(payment.paymentDate)}
        </Typography>

        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <IconButton
            onClick={() => handleView(payment.id)}
            sx={{
              color: "#3a86ff",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Visibility sx={{ fontSize: 18 }} />
          </IconButton>
          {/* <IconButton
            onClick={() => handleDelete(payment.id)}
            sx={{
              color: "#d00000",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Delete sx={{ fontSize: 18 }} />
          </IconButton> */}
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="xl" sx={{ py: 1 }}>
        <Typography
          variant="h4"
          sx={{
            mb: 3,
            color: "#333",
            fontWeight: "600",
            fontSize: isMobile ? "24px" : "32px",
          }}
        >
          All Payments
        </Typography>

        {/* Search and Filters Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            p: 2,
            mb: 2,
          }}
        >
          <Grid container spacing={2}>
            {/* Search Bar */}
            <Grid item xs={12} md={6} lg={4}>
              <TextField
                fullWidth
                placeholder="Search payments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ color: "#666", mr: 1 }} />,
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      borderColor: "#E0E0E0",
                    },
                  },
                }}
              />
            </Grid>

            {/* Start Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "& fieldset": {
                          borderColor: "#E0E0E0",
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* End Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        "& fieldset": {
                          borderColor: "#E0E0E0",
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* Payment Status Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <FormControl fullWidth>
                <InputLabel>Payment Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Payment Status"
                  onChange={(e) => setStatusFilter(e.target.value)}
                  sx={{
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#E0E0E0",
                    },
                  }}
                >
                  <MenuItem value="">All Status</MenuItem>
                  {getPaymentStatuses().map((status) => (
                    <MenuItem key={status} value={status}>
                      {status}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Clear Filters Button */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm("");
                  setStartDate(null);
                  setEndDate(null);
                  setStatusFilter("");
                }}
                sx={{
                  height: "48px",
                  borderColor: "#E0E0E0",
                  color: "#666",
                  "&:hover": {
                    borderColor: "#1976D2",
                    backgroundColor: "#F5F5F5",
                  },
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Table Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            overflow: "hidden",
          }}
        >
          {/* Desktop/Tablet Table View */}
          {!isMobile ? (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Payment Date
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Amount
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Customer Name
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                          display: isTablet ? "none" : "table-cell",
                        }}
                      >
                        Customer Phone
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Payment Status
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredPayments
                      .slice(
                        page * rowsPerPage,
                        page * rowsPerPage + rowsPerPage
                      )
                      .map((payment, index) => (
                        <TableRow
                          key={payment.id}
                          hover
                          sx={{
                            backgroundColor: "#fff",
                            "&:hover": {
                              backgroundColor: "#F8F9FA",
                            },
                            borderBottom: "1px solid #F0F0F0",
                          }}
                        >
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "400",
                            }}
                          >
                            {formatDateTime(payment.paymentDate)}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "600",
                            }}
                          >
                            {formatCurrency(payment.amount)}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "500",
                            }}
                          >
                            {payment.customerName}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              display: isTablet ? "none" : "table-cell",
                            }}
                          >
                            {payment.customerPhone}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={payment.paymentStatus}
                              size="small"
                              sx={{
                                ...getStatusColor(payment.paymentStatus),
                                border: "1px solid",
                                fontWeight: "500",
                                fontSize: "11px",
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={0.5}>
                              <IconButton
                                onClick={() => handleView(payment.id)}
                                sx={{
                                  color: "#3a86ff",
                                  "&:hover": { backgroundColor: "#F5F5F5" },
                                  padding: "4px",
                                }}
                                size="small"
                              >
                                <Visibility sx={{ fontSize: 16 }} />
                              </IconButton>
                              {/* <IconButton
                                onClick={() => handleDelete(payment.id)}
                                sx={{
                                  color: "#d00000",
                                  "&:hover": { backgroundColor: "#F5F5F5" },
                                  padding: "4px",
                                }}
                                size="small"
                              >
                                <Delete sx={{ fontSize: 16 }} />
                              </IconButton> */}
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))}
                    {filteredPayments.length === 0 && (
                      <TableRow>
                        <TableCell
                          colSpan={6}
                          sx={{ textAlign: "center", py: 4 }}
                        >
                          <Typography variant="body1" color="textSecondary">
                            No payments found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          ) : (
            /* Mobile Card View */
            <Box sx={{ p: 2 }}>
              {filteredPayments
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((payment) => (
                  <PaymentCard key={payment.id} payment={payment} />
                ))}
              {filteredPayments.length === 0 && (
                <Box sx={{ textAlign: "center", py: 4 }}>
                  <Typography variant="body1" color="textSecondary">
                    No payments found
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {/* Custom Pagination */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              p: 2,
              borderTop: "1px solid #E0E0E0",
              backgroundColor: "#FAFAFA",
              flexDirection: isMobile ? "column" : "row",
              gap: isMobile ? 2 : 0,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                flexWrap: "wrap",
              }}
            >
              {[1, 2, 3, 4].map((pageNum) => (
                <Button
                  key={pageNum}
                  variant={page + 1 === pageNum ? "contained" : "text"}
                  onClick={() => setPage(pageNum - 1)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: 1,
                    fontSize: "14px",
                    ...(page + 1 === pageNum
                      ? {
                          backgroundColor: "#1976D2",
                          color: "white",
                          "&:hover": {
                            backgroundColor: "#1565C0",
                          },
                        }
                      : {
                          color: "#666",
                          "&:hover": {
                            backgroundColor: "#F5F5F5",
                          },
                        }),
                  }}
                >
                  {pageNum}
                </Button>
              ))}
              <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
                ...
              </Typography>
            </Box>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                flexWrap: "wrap",
              }}
            >
              <Typography variant="body2" sx={{ color: "#666" }}>
                Go to page
              </Typography>
              <TextField
                size="small"
                type="number"
                value={page + 1}
                onChange={(e) =>
                  setPage(Math.max(0, Number(e.target.value) - 1))
                }
                sx={{
                  width: 60,
                  "& .MuiOutlinedInput-root": {
                    height: 32,
                    "& fieldset": {
                      borderColor: "#E0E0E0",
                    },
                  },
                }}
                inputProps={{
                  min: 1,
                  max: Math.ceil(filteredPayments.length / rowsPerPage),
                }}
              />
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography variant="body2" color="#666">
                  Show
                </Typography>
                <FormControl size="small" sx={{ minWidth: 80 }}>
                  <Select
                    value={rowsPerPage}
                    onChange={handleChangeRowsPerPage}
                    displayEmpty
                    sx={{
                      backgroundColor: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E0E0E0",
                      },
                    }}
                  >
                    <MenuItem value={5}>5 Row</MenuItem>
                    <MenuItem value={8}>8 Row</MenuItem>
                    <MenuItem value={10}>10 Row</MenuItem>
                    <MenuItem value={25}>25 Row</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Box>
          </Box>
        </Paper>
      </Container>
    </LocalizationProvider>
  );
};

export default AllPayments;