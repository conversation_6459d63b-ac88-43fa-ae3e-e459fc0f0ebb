import React, { useState, useEffect } from "react";
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Typography,
  Chip,
  Stack,
  Container,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
  Collapse,
  Alert,
} from "@mui/material";
import { 
  Visibility, 
  Check, 
  Close, 
  Search, 
  Add, 
  ExpandMore, 
  ExpandLess 
} from "@mui/icons-material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { adminWithdrawalData } from "../../../Dummydata/adminWithdrawalData";

const AllWithdrawals = () => {
  const [withdrawals, setWithdrawals] = useState([]);
  const [filteredWithdrawals, setFilteredWithdrawals] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [statusFilter, setStatusFilter] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8);

  const [successMessage, setSuccessMessage] = useState("");

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  // Load withdrawal data on component mount
  useEffect(() => {
    setWithdrawals(adminWithdrawalData);
    setFilteredWithdrawals(adminWithdrawalData);
  }, []);

  // Filter withdrawals based on search term, date range, and status
  useEffect(() => {
    let filtered = withdrawals;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (withdrawal) =>
          withdrawal.amount.toString().includes(searchTerm)
      );
    }

    // Date range filter
    if (startDate || endDate) {
      filtered = filtered.filter((withdrawal) => {
        const withdrawalDate = new Date(withdrawal.withdrawalDate);
        const start = startDate ? new Date(startDate) : new Date("1900-01-01");
        const end = endDate ? new Date(endDate) : new Date("2100-12-31");
        return withdrawalDate >= start && withdrawalDate <= end;
      });
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter((withdrawal) => 
        withdrawal.status === statusFilter
      );
    }

    setFilteredWithdrawals(filtered);
    setPage(0); // Reset page when filtering
  }, [searchTerm, startDate, endDate, statusFilter, withdrawals]);



  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleView = (withdrawalId) => {
    console.log("View withdrawal:", withdrawalId);
    // Add view functionality here
  };

  const handleApprove = (withdrawalId) => {
    console.log("Approve withdrawal:", withdrawalId);
    // Add approval functionality here
    setWithdrawals(prev => 
      prev.map(withdrawal => 
        withdrawal.id === withdrawalId 
          ? { ...withdrawal, status: "Completed" }
          : withdrawal
      )
    );
  };

  const handleReject = (withdrawalId) => {
    console.log("Reject withdrawal:", withdrawalId);
    // Add rejection functionality here
    setWithdrawals(prev => 
      prev.map(withdrawal => 
        withdrawal.id === withdrawalId 
          ? { ...withdrawal, status: "Rejected" }
          : withdrawal
      )
    );
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "Completed":
        return {
          backgroundColor: "#E8F5E8",
          color: "#2E7D32",
          borderColor: "#A5D6A7",
        };
      case "Pending":
        return {
          backgroundColor: "#FFF3E0",
          color: "#F57C00",
          borderColor: "#FFCC02",
        };
      case "Rejected":
        return {
          backgroundColor: "#FFEBEE",
          color: "#C62828",
          borderColor: "#EF9A9A",
        };
      default:
        return {
          backgroundColor: "#F5F5F5",
          color: "#666",
          borderColor: "#E0E0E0",
        };
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      }) +
      "; " +
      date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    );
  };

  const formatAmount = (amount) => {
    return `$${amount.toFixed(2)}`;
  };

  const getStatuses = () => {
    const statuses = [...new Set(withdrawals.map(withdrawal => withdrawal.status))];
    return statuses;
  };

  // Mobile Card View Component
  const WithdrawalCard = ({ withdrawal }) => (
    <Card sx={{ mb: 2, border: "1px solid #E0E0E0" }}>
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Chip
            label={withdrawal.status}
            size="small"
            sx={{
              ...getStatusColor(withdrawal.status),
              border: "1px solid",
              fontWeight: "500",
              fontSize: "11px",
            }}
          />
        </Box>
        <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
          <strong>Amount:</strong> {formatAmount(withdrawal.amount)}
        </Typography>
        <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
          <strong>Withdrawal Date:</strong> {formatDateTime(withdrawal.withdrawalDate)}
        </Typography>

        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <IconButton
            onClick={() => handleView(withdrawal.id)}
            sx={{
              color: "#3a86ff",
              "&:hover": { backgroundColor: "#F5F5F5" },
              padding: "8px",
            }}
            size="small"
          >
            <Visibility sx={{ fontSize: 18 }} />
          </IconButton>
          {withdrawal.status === "Pending" && (
            <>
              <IconButton
                onClick={() => handleApprove(withdrawal.id)}
                sx={{
                  color: "#2E7D32",
                  "&:hover": { backgroundColor: "#F5F5F5" },
                  padding: "8px",
                }}
                size="small"
              >
                <Check sx={{ fontSize: 18 }} />
              </IconButton>
              <IconButton
                onClick={() => handleReject(withdrawal.id)}
                sx={{
                  color: "#d00000",
                  "&:hover": { backgroundColor: "#F5F5F5" },
                  padding: "8px",
                }}
                size="small"
              >
                <Close sx={{ fontSize: 18 }} />
              </IconButton>
            </>
          )}
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="xl" sx={{ py: 1 }}>
        <Typography
          variant="h4"
          sx={{ mb: 3, color: "#333", fontWeight: "600", fontSize: isMobile ? '24px' : '32px' }}
        >
          All Withdrawals
        </Typography>

        {/* Success Message */}
        {successMessage && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {successMessage}
          </Alert>
        )}

        {/* Search and Filters Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            p: 2,
            mb: 2,
          }}
        >
          <Grid container spacing={2}>
            {/* Search Bar */}
            <Grid item xs={12} md={6} lg={4}>
              <TextField
                fullWidth
                placeholder="Search withdrawals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ color: '#666', mr: 1 }} />,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E0E0E0',
                    },
                  },
                }}
              />
            </Grid>

            {/* Start Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E0E0E0',
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* End Date Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E0E0E0',
                        },
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* Status Filter */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => setStatusFilter(e.target.value)}
                  sx={{
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#E0E0E0',
                    },
                  }}
                >
                  <MenuItem value="">All Status</MenuItem>
                  {getStatuses().map((status) => (
                    <MenuItem key={status} value={status}>
                      {status}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Clear Filters Button */}
            <Grid item xs={12} sm={6} md={3} lg={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm("");
                  setStartDate(null);
                  setEndDate(null);
                  setStatusFilter("");
                }}
                sx={{
                  height: '48px',
                  borderColor: '#E0E0E0',
                  color: '#666',
                  '&:hover': {
                    borderColor: '#1976D2',
                    backgroundColor: '#F5F5F5',
                  },
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Table Section */}
        <Paper
          elevation={1}
          sx={{
            border: "1px solid #E0E0E0",
            borderRadius: 2,
            overflow: "hidden",
          }}
        >
          {/* Desktop/Tablet Table View */}
          {!isMobile ? (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: "#F8F9FA" }}>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Withdrawal Date
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Amount
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Status
                      </TableCell>
                      <TableCell
                        sx={{
                          color: "#666",
                          fontWeight: "600",
                          fontSize: "12px",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                          borderBottom: "1px solid #E0E0E0",
                        }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredWithdrawals
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((withdrawal, index) => (
                        <TableRow
                          key={withdrawal.id}
                          hover
                          sx={{
                            backgroundColor: "#fff",
                            "&:hover": {
                              backgroundColor: "#F8F9FA",
                            },
                            borderBottom: "1px solid #F0F0F0",
                          }}
                        >
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "400",
                            }}
                          >
                            {formatDateTime(withdrawal.withdrawalDate)}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "#333",
                              fontSize: "13px",
                              fontWeight: "600",
                            }}
                          >
                            {formatAmount(withdrawal.amount)}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={withdrawal.status}
                              size="small"
                              sx={{
                                ...getStatusColor(withdrawal.status),
                                border: "1px solid",
                                fontWeight: "500",
                                fontSize: "11px",
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={0.5}>
                              <IconButton
                                onClick={() => handleView(withdrawal.id)}
                                sx={{
                                  color: "#3a86ff",
                                  "&:hover": { backgroundColor: "#F5F5F5" },
                                  padding: "4px",
                                }}
                                size="small"
                              >
                                <Visibility sx={{ fontSize: 16 }} />
                              </IconButton>
                              {withdrawal.status === "Pending" && (
                                <>
                                  <IconButton
                                    onClick={() => handleApprove(withdrawal.id)}
                                    sx={{
                                      color: "#2E7D32",
                                      "&:hover": { backgroundColor: "#F5F5F5" },
                                      padding: "4px",
                                    }}
                                    size="small"
                                  >
                                    <Check sx={{ fontSize: 16 }} />
                                  </IconButton>
                                  <IconButton
                                    onClick={() => handleReject(withdrawal.id)}
                                    sx={{
                                      color: "#d00000",
                                      "&:hover": { backgroundColor: "#F5F5F5" },
                                      padding: "4px",
                                    }}
                                    size="small"
                                  >
                                    <Close sx={{ fontSize: 16 }} />
                                  </IconButton>
                                </>
                              )}
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))}
                    {filteredWithdrawals.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={6} sx={{ textAlign: "center", py: 4 }}>
                          <Typography variant="body1" color="textSecondary">
                            No withdrawals found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          ) : (
            /* Mobile Card View */
            <Box sx={{ p: 2 }}>
              {filteredWithdrawals
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((withdrawal) => (
                  <WithdrawalCard key={withdrawal.id} withdrawal={withdrawal} />
                ))}
              {filteredWithdrawals.length === 0 && (
                <Box sx={{ textAlign: "center", py: 4 }}>
                  <Typography variant="body1" color="textSecondary">
                    No withdrawals found
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {/* Custom Pagination */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              p: 2,
              borderTop: "1px solid #E0E0E0",
              backgroundColor: "#FAFAFA",
              flexDirection: isMobile ? 'column' : 'row',
              gap: isMobile ? 2 : 0,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
              {[1, 2, 3, 4].map((pageNum) => (
                <Button
                  key={pageNum}
                  variant={page + 1 === pageNum ? "contained" : "text"}
                  onClick={() => setPage(pageNum - 1)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: 1,
                    fontSize: "14px",
                    ...(page + 1 === pageNum
                      ? {
                          backgroundColor: "#1976D2",
                          color: "white",
                          "&:hover": {
                            backgroundColor: "#1565C0",
                          },
                        }
                      : {
                          color: "#666",
                          "&:hover": {
                            backgroundColor: "#F5F5F5",
                          },
                        }),
                  }}
                >
                  {pageNum}
                </Button>
              ))}
              <Typography variant="body2" sx={{ color: "#666", mx: 1 }}>
                ...
              </Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: 'wrap' }}>
              <Typography variant="body2" sx={{ color: "#666" }}>
                Go to page
              </Typography>
              <TextField
                size="small"
                type="number"
                value={page + 1}
                onChange={(e) =>
                  setPage(Math.max(0, Number(e.target.value) - 1))
                }
                sx={{
                  width: 60,
                  "& .MuiOutlinedInput-root": {
                    height: 32,
                    "& fieldset": {
                      borderColor: "#E0E0E0",
                    },
                  },
                }}
                inputProps={{
                  min: 1,
                  max: Math.ceil(filteredWithdrawals.length / rowsPerPage),
                }}
              />
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography variant="body2" color="#666">
                  Show
                </Typography>
                <FormControl size="small" sx={{ minWidth: 80 }}>
                  <Select
                    value={rowsPerPage}
                    onChange={handleChangeRowsPerPage}
                    displayEmpty
                    sx={{
                      backgroundColor: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E0E0E0",
                      },
                    }}
                  >
                    <MenuItem value={5}>5 Row</MenuItem>
                    <MenuItem value={8}>8 Row</MenuItem>
                    <MenuItem value={10}>10 Row</MenuItem>
                    <MenuItem value={25}>25 Row</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Box>
          </Box>
        </Paper>
      </Container>
    </LocalizationProvider>
  );
};

export default AllWithdrawals;