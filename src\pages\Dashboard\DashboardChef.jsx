import React from "react";
import {
  Box,
  Paper,
  Typography,
  Container,
  Grid,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
  IconButton,
} from "@mui/material";
import {
  People,
  Person,
  Subscriptions,
  CalendarToday,
  AccountBalanceWallet,
  Payment,
  Receipt,
  TrendingUp,
  MoreVert,
} from "@mui/icons-material";

const DashboardChef = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  // Sample data - replace with your actual data
  const dashboardData = [
    {
      title: "All Bookings",
      count: "4,512",
      icon: CalendarToday,
      color: "#F57C00",
      bgColor: "#FFF3E0",
      change: "+22%",
      changeType: "increase",
    },
    {
      title: "All Withdrawals",
      count: "342",
      icon: AccountBalanceWallet,
      color: "#D32F2F",
      bgColor: "#FFEBEE",
      change: "-5%",
      changeType: "decrease",
    },
    {
      title: "All Payments",
      count: "$89,247",
      icon: Payment,
      color: "#388E3C",
      bgColor: "#E8F5E8",
      change: "+18%",
      changeType: "increase",
    },
  ];

  const StatCard = ({ data }) => {
    const IconComponent = data.icon;
    
    return (
      <Card
        sx={{
          border: "1px solid #E0E0E0",
          borderRadius: 2,
          overflow: "hidden",
          transition: "all 0.3s ease",
          "&:hover": {
            boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
            transform: "translateY(-2px)",
          },
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Box 
              sx={{
                width: 45,
                height: 45,
                borderRadius: 2,
                backgroundColor: data.bgColor,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <IconComponent sx={{ fontSize: 24, color: data.color }} />
            </Box>
            <IconButton
              size="small"
              sx={{
                color: "#666",
                "&:hover": { backgroundColor: "#F5F5F5" },
              }}
            >
              <MoreVert sx={{ fontSize: 18 }} />
            </IconButton>
          </Box>
          
          <Typography
            variant="h4"
            sx={{
              fontSize: isMobile ? '24px' : '28px',
              fontWeight: '700',
              color: '#333',
              mb: 1,
            }}
          >
            {data.count}
          </Typography>
          
          <Typography
            variant="body1"
            sx={{
              color: '#666',
              fontSize: '14px',
              fontWeight: '500',
              mb: 2,
            }}
          >
            {data.title}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          </Box>
        </CardContent>
      </Card>
    );
  };

  return (
    <Container maxWidth="xl" sx={{ py: 1 }}>
      <Typography
        variant="h4"
        sx={{
          mb: 3,
          color: "#333",
          fontWeight: "600",
          fontSize: isMobile ? '24px' : '32px'
        }}
      >
        Dashboard
      </Typography>


      {/* Statistics Cards Grid */}
      <Grid container spacing={2}>
        {dashboardData.map((data, index) => (
          <Grid
            item
            xs={12}
            sm={4}
            md={4}
            lg={4}
            key={index}
          >
            <StatCard data={data} />
          </Grid>
        ))}
      </Grid>

      {/* Recent Activity Section */}
      <Paper
        elevation={1}
        sx={{
          border: "1px solid #E0E0E0",
          borderRadius: 2,
          p: 3,
          mt: 3,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            color: "#333",
            fontWeight: "600",
            mb: 2,
            fontSize: isMobile ? '18px' : '20px'
          }}
        >
          Quick Stats Overview
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={4}>
            <Box sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h4" sx={{ color: '#1976D2', fontWeight: '700', mb: 1 }}>
                98.5%
              </Typography>
              <Typography variant="body2" sx={{ color: '#666' }}>
                Customer Satisfaction
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h4" sx={{ color: '#388E3C', fontWeight: '700', mb: 1 }}>
                $2.4M
              </Typography>
              <Typography variant="body2" sx={{ color: '#666' }}>
                Total Revenue
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h4" sx={{ color: '#7B1FA2', fontWeight: '700', mb: 1 }}>
                89%
              </Typography>
              <Typography variant="body2" sx={{ color: '#666' }}>
                Booking Success Rate
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default DashboardChef;