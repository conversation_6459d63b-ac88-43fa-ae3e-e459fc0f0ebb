// import React, { useState } from "react";
// import {
//   Box,
//   Container,
//   Paper,
//   TextField,
//   Typography,
//   Checkbox,
//   Button,
//   FormControlLabel,
//   InputAdornment,
//   IconButton,
//   S<PERSON>kbar,
//   <PERSON><PERSON>,
// } from "@mui/material";
// import { Visibility, VisibilityOff } from "@mui/icons-material";
// import { useNavigate } from "react-router-dom";
// import logo from "../../assets/Logo.png";
// import backgroundImage from "../../assets/Dashboard/background.png";
// import { useDispatch, useSelector } from "react-redux";
// import { selectIsLoggedIn, selectUserType } from "../../reducers/auth.reducer";
// import { loginUser } from "../../app/service/auth.service";

// // Replace with correct paths
// // const backgroundImage = "/assets/Dashboard/background.png";
// // const logo = "/assets/logo.png";

// export default function Login() {
//   const [email, setEmail] = useState("");
//   const [username, setUsername] = useState("");
//   const [password, setPassword] = useState("");
//   const [rememberMe, setRememberMe] = useState(false);
//   const [showPassword, setShowPassword] = useState(false);
//   const isLoggedIn = false;
//   // const isLoggedIn = useSelector(selectIsLoggedIn);
//   const [error, setError] = useState("");
//   const userType = "admin";
//   // const userType = useSelector(selectUserType);
//   const [snackbar, setSnackbar] = useState({
//     open: false,
//     message: "",
//     severity: "success",
//   });
//   const showSnackbar = (message, severity) => {
//     setSnackbar({ open: true, message, severity });
//   };

//   const handleCloseSnackbar = () => {
//     setSnackbar({ ...snackbar, open: false });
//   };
//   const navigate = useNavigate();

//   const handleLogin = async (e) => {
//     e.preventDefault();
//     // const result = await loginUser(username, password);
//     // console.log(result);
//     // if (result.success) {
//       navigate("/system/dashboard");
//       showSnackbar("Login Success", "success");

//       // Login successful
//     // } else {
//     //   showSnackbar(result.error, "error");
//     //   // Login failed
//     //   setError(result.error);
//     // }
//   };

//   return (
//     <>
//       <Box
//         sx={{
//           position: "relative",
//           height: "100vh",
//           display: "flex",
//           alignItems: "center",
//           justifyContent: "center",
//           backgroundImage: `url(${backgroundImage})`,
//           backgroundSize: "cover",
//           backgroundPosition: "center",
//           backgroundRepeat: "no-repeat",
//           "&::before": {
//             content: '""',
//             position: "absolute",
//             top: 0,
//             left: 0,
//             width: "100%",
//             height: "100%",
//             // backgroundColor: 'rgba(0, 0, 0, 0.6)', // Dark overlay
//             zIndex: 1,
//           },
//         }}
//       >
//         <Container
//           maxWidth="xs"
//           sx={{ position: "relative", zIndex: 2, marginLeft: "0" }}
//         >
//           <Paper
//             elevation={10}
//             sx={{
//               display: "flex",
//               flexDirection: "column",
//               alignItems: "center",
//               padding: 4,
//               backgroundColor: "rgba(85, 79, 79, 0.9)",
//               borderRadius: 2,
//               width: "100%",
//               boxShadow: "none",
//               // marginLeft: '0',
//             }}
//           >
//             {/* Logo */}
//             <Box
//               component="img"
//               src={logo}
//               alt="Muscle Lab Logo"
//               sx={{
//                 height: 100,
//                 marginBottom: 2,
//               }}
//             />

//             {/* Login Title */}
//             <Typography
//               component="h1"
//               variant="h5"
//               sx={{
//                 color: "white",
//                 fontWeight: "bold",
//                 marginBottom: 2,
//               }}
//             >
//               LOGIN
//             </Typography>

//             {/* Login Form */}
//             <Box component="form" noValidate sx={{ width: "100%" }}>
//               {/* Email Input */}
//               <TextField
//                 margin="normal"
//                 required
//                 fullWidth
//                 id="username"
//                 label="Username"
//                 name="username"
//                 autoComplete="username"
//                 autoFocus
//                 variant="outlined"
//                 value={username}
//                 onChange={(e) => setUsername(e.target.value)}
//                 InputProps={{
//                   sx: {
//                     backgroundColor: "white",
//                     borderRadius: "8px",
//                   },
//                 }}
//               />

//               {/* Password Input */}
//               <TextField
//                 margin="normal"
//                 required
//                 fullWidth
//                 name="password"
//                 label="Password"
//                 type={showPassword ? "text" : "password"}
//                 id="password"
//                 autoComplete="current-password"
//                 variant="outlined"
//                 value={password}
//                 onChange={(e) => setPassword(e.target.value)}
//                 InputProps={{
//                   sx: {
//                     backgroundColor: "white",
//                     borderRadius: "8px",
//                   },
//                   endAdornment: (
//                     <InputAdornment position="end">
//                       <IconButton
//                         aria-label="toggle password visibility"
//                         onClick={() => setShowPassword(!showPassword)}
//                         edge="end"
//                       >
//                         {showPassword ? <VisibilityOff /> : <Visibility />}
//                       </IconButton>
//                     </InputAdornment>
//                   ),
//                 }}
//               />

//               {/* Remember Me & Forgot Password */}
//               <Box
//                 sx={{
//                   display: "flex",
//                   justifyContent: "space-between",
//                   alignItems: "center",
//                   mt: 1,
//                 }}
//               >
//                 <FormControlLabel
//                   control={
//                     <Checkbox
//                       checked={rememberMe}
//                       onChange={(e) => setRememberMe(e.target.checked)}
//                       sx={{
//                         color: "white",
//                         "&.Mui-checked": {
//                           color: "white",
//                         },
//                       }}
//                     />
//                   }
//                   label={
//                     <Typography sx={{ color: "white" }}>Remember Me</Typography>
//                   }
//                 />
//                 <Button
//                   href="#"
//                   sx={{
//                     color: "white",
//                     textTransform: "none",
//                   }}
//                 >
//                   Forgot Password?
//                 </Button>
//               </Box>

//               {/* Login Button */}
//               <Button
//                 fullWidth
//                 variant="contained"
//                 onClick={handleLogin}
//                 sx={{
//                   mt: 3,
//                   mb: 2,
//                   py: 1.5,
//                   backgroundColor: "black",
//                   fontSize: 14,
//                   fontFamily: "Poppins, sans-serif",
//                   color: "#FFF",
//                   "&:hover": {
//                     backgroundColor: "rgba(0,0,0,0.8)",
//                   },
//                 }}
//               >
//                 Login
//               </Button>
//             </Box>
//           </Paper>
//         </Container>
//         <Snackbar
//           open={snackbar.open}
//           autoHideDuration={6000}
//           onClose={handleCloseSnackbar}
//           anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
//         >
//           <Alert
//             onClose={handleCloseSnackbar}
//             severity={snackbar.severity}
//             sx={{ width: "100%" }}
//           >
//             {snackbar.message}
//           </Alert>
//         </Snackbar>
//       </Box>
//     </>
//   );
// }

import React, { startTransition, useState } from "react";
import {
  Box,
  Container,
  Paper,
  TextField,
  Typography,
  Checkbox,
  Button,
  FormControlLabel,
  InputAdornment,
  IconButton,
  Snackbar,
  Alert,
  Link,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import { Visibility, VisibilityOff, RestaurantMenu } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { loginUser } from "../../app/service/auth.service";

export default function WorkreapLogin() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const navigate = useNavigate();

  const testUsers = [
    { username: "admin", password: "admin123", userType: "Admin" },
    { username: "chef", password: "chef123", userType: "Chef" },
    { username: "customer", password: "customer123", userType: "Customer" },
  ];

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    // const user = testUsers.find(
    //   (u) => u.username === username && u.password === password
    // );

    // if (!user) {
    //   showSnackbar("Invalid username or password!", "error");
    //   return;
    // }
    const result = await loginUser(username, password);
    if (result.success) {
      showSnackbar(`Login successful as ${result.userType}!`, "success");

      // Use startTransition to wrap the navigation
      startTransition(() => {
        // Add a small delay to let the user see the success message
        setTimeout(() => {
          if (result.userType === "Admin" || result.userType === "Chef") {
            navigate(`/system/dashboard?userType=${result.userType}`);
          } else if (result.userType === "Customer") {
            navigate(`/account?userType=${result.userType}`);
          }
        }, 500);
      });
    } else {
      showSnackbar(result.error, "error");
      // Login failed
      setError(result.error);
    }
  };

  const handleSignUp = () => {
    // Add navigation to sign up page
    console.log("Navigate to sign up");
    navigate("/auth/register");
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f8f9fa",
        padding: 2,
      }}
    >
      <Container maxWidth="sm">
        <Paper
          elevation={0}
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            padding: { xs: 3, sm: 4, md: 5 },
            backgroundColor: "white",
            borderRadius: 3,
            boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
            border: "1px solid #e9ecef",
            maxWidth: 400,
            margin: "0 auto",
          }}
        >
          {/* Logo */}
          {/* <Box
            sx={{
              display: "flex",
              alignItems: "center",
              marginBottom: 4,
            }}
          >
            <Box
              sx={{
                width: 32,
                height: 32,
                backgroundColor: "#ff6b35",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginRight: 1.5,
                position: "relative",
                "&::after": {
                  content: '""',
                  position: "absolute",
                  width: 16,
                  height: 16,
                  backgroundColor: "white",
                  borderRadius: "50%",
                  left: "50%",
                  top: "50%",
                  transform: "translate(-50%, -50%)",
                },
                "&::before": {
                  content: '""',
                  position: "absolute",
                  width: 8,
                  height: 8,
                  backgroundColor: "#ff6b35",
                  borderRadius: "50%",
                  left: "50%",
                  top: "50%",
                  transform: "translate(-50%, -50%)",
                  zIndex: 1,
                },
              }}
            />
            <Typography
              variant="h5"
              sx={{
                color: "#2c3e50",
                fontWeight: 600,
                fontSize: "1.5rem",
              }}
            >
              Hire a Chef
            </Typography>
          </Box> */}

          <Box
            sx={{ display: "flex", alignItems: "center", gap: 1, py: 2, pb: 3 }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                width: 32,
                height: 32,
                borderRadius: "50%",
                backgroundColor: theme.palette.primary.main,
              }}
            >
              <RestaurantMenu sx={{ color: "white", fontSize: 20 }} />
            </Box>
            <Typography
              variant="h6"
              sx={{ fontWeight: 700, color: "text.primary" }}
            >
              Hire a Chef
            </Typography>
          </Box>

          {/* Subtitle */}
          <Typography
            variant="body1"
            sx={{
              color: "#6c757d",
              textAlign: "center",
              marginBottom: 4,
              fontSize: "0.95rem",
              lineHeight: 1.5,
            }}
          >
            Please enter your username & password
            <br />
            to access your account
          </Typography>

          {/* Login Form */}
          <Box
            component="form"
            onSubmit={handleLogin}
            noValidate
            sx={{ width: "100%" }}
          >
            {/* Username Input */}
            <Box sx={{ marginBottom: 3 }}>
              <Typography
                variant="body2"
                sx={{
                  color: "#495057",
                  marginBottom: 1,
                  fontSize: "0.9rem",
                  fontWeight: 500,
                }}
              >
                Username
              </Typography>
              <TextField
                required
                fullWidth
                id="username"
                name="username"
                autoComplete="username"
                autoFocus
                variant="outlined"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your username"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    backgroundColor: "#f8f9fa",
                    borderRadius: "8px",
                    border: "none",
                    "& fieldset": {
                      border: "1px solid #e9ecef",
                    },
                    "&:hover fieldset": {
                      borderColor: "#ff6b35",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#ff6b35",
                      borderWidth: "2px",
                    },
                  },
                  "& .MuiOutlinedInput-input": {
                    padding: "14px 16px",
                    fontSize: "0.95rem",
                  },
                }}
              />
            </Box>

            {/* Password Input */}
            <Box sx={{ marginBottom: 3 }}>
              <Typography
                variant="body2"
                sx={{
                  color: "#495057",
                  marginBottom: 1,
                  fontSize: "0.9rem",
                  fontWeight: 500,
                }}
              >
                Password
              </Typography>
              <TextField
                required
                fullWidth
                name="password"
                type={showPassword ? "text" : "password"}
                id="password"
                autoComplete="current-password"
                variant="outlined"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    backgroundColor: "#f8f9fa",
                    borderRadius: "8px",
                    border: "none",
                    "& fieldset": {
                      border: "1px solid #e9ecef",
                    },
                    "&:hover fieldset": {
                      borderColor: "#ff6b35",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#ff6b35",
                      borderWidth: "2px",
                    },
                  },
                  "& .MuiOutlinedInput-input": {
                    padding: "14px 16px",
                    fontSize: "0.95rem",
                  },
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                        sx={{ color: "#6c757d" }}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            {/* Remember Me & Forgot Password */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: 4,
              }}
            >
              <FormControlLabel
                control={
                  <Checkbox
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    sx={{
                      color: "#6c757d",
                      "&.Mui-checked": {
                        color: "#ff6b35",
                      },
                      padding: "4px 8px 4px 0",
                    }}
                  />
                }
                label={
                  <Typography
                    sx={{
                      color: "#495057",
                      fontSize: "0.9rem",
                      fontWeight: 500,
                    }}
                  >
                    Remember me
                  </Typography>
                }
              />
              <Link
                href="#"
                underline="none"
                sx={{
                  color: theme.palette.primary.main,
                  fontSize: "0.9rem",
                  fontWeight: 500,
                  "&:hover": {
                    color: theme.palette.primary.dark,
                  },
                }}
              >
                Forget password?
              </Link>
            </Box>

            {/* Sign In Button */}
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{
                marginBottom: 3,
                padding: "12px 0",
                backgroundColor: theme.palette.primary.main,
                color: "white",
                fontSize: "1rem",
                fontWeight: 600,
                borderRadius: "8px",
                textTransform: "none",
                boxShadow: "0 4px 12px rgba(255, 107, 53, 0.3)",
                "&:hover": {
                  backgroundColor: theme.palette.primary.dark,
                  boxShadow: "0 6px 16px rgba(255, 107, 53, 0.4)",
                },
                "&:active": {
                  transform: "translateY(1px)",
                },
                transition: "all 0.2s ease-in-out",
              }}
            >
              Sign In
            </Button>

            {/* Sign Up Link */}
            <Box sx={{ textAlign: "center" }}>
              <Typography
                variant="body2"
                sx={{
                  color: "#6c757d",
                  fontSize: "0.9rem",
                }}
              >
                Don't have an account?{" "}
                <Link
                  component="button"
                  type="button"
                  onClick={handleSignUp}
                  underline="none"
                  sx={{
                    color: theme.palette.primary.main,
                    fontWeight: 600,
                    "&:hover": {
                      color: theme.palette.primary.dark,
                    },
                  }}
                >
                  Sign up
                </Link>
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{
              width: "100%",
              borderRadius: "8px",
            }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
}
