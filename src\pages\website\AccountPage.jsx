import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  Button,
  Card,
  CardContent,
  Divider,
  useTheme,
  useMediaQuery,
  Drawer,
  IconButton,
  AppBar,
  Toolbar,
  CssBaseline
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  ShoppingBag as OrdersIcon,
  Payment as PaymentIcon,
  Subscriptions as SubscriptionIcon,
  Person as PersonIcon,
  Logout as LogoutIcon,
  Menu as MenuIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { logOut } from '../../reducers/auth.reducer';

export default function AccountPage() {
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [mobileOpen, setMobileOpen] = useState(false);
  const theme = useTheme();
  const dispatch = useDispatch();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmall = useMediaQuery(theme.breakpoints.down('sm'));

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };
  const handleLogout = () => {
      dispatch(logOut()); // Dispatch the logOut action to clear the auth state
      navigate("/"); // Redirect the user to the login page
    };

  // Sample data
  const orders = [
    { id: '#ORD-001', date: '2024-06-15', status: 'Delivered', total: '$129.99', items: 3 },
    { id: '#ORD-002', date: '2024-06-10', status: 'Processing', total: '$89.50', items: 2 },
    { id: '#ORD-003', date: '2024-06-05', status: 'Shipped', total: '$199.99', items: 5 },
    { id: '#ORD-004', date: '2024-05-28', status: 'Delivered', total: '$75.25', items: 1 }
  ];

  const payments = [
    { id: 'PAY-001', date: '2024-06-15', method: 'Credit Card', amount: '$129.99', status: 'Completed' },
    { id: 'PAY-002', date: '2024-06-10', method: 'PayPal', amount: '$89.50', status: 'Pending' },
    { id: 'PAY-003', date: '2024-06-05', method: 'Credit Card', amount: '$199.99', status: 'Completed' },
    { id: 'PAY-004', date: '2024-05-28', method: 'Bank Transfer', amount: '$75.25', status: 'Completed' }
  ];

  const subscriptions = [
    { id: 'SUB-001', service: 'Premium Plan', status: 'Active', nextBilling: '2024-07-15', amount: '$29.99' },
    { id: 'SUB-002', service: 'Pro Features', status: 'Active', nextBilling: '2024-07-20', amount: '$19.99' },
    { id: 'SUB-003', service: 'Basic Plan', status: 'Cancelled', nextBilling: '-', amount: '$9.99' }
  ];

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: <DashboardIcon /> },
    { id: 'orders', label: 'My Bookings', icon: <OrdersIcon /> },
    { id: 'payments', label: 'Payments', icon: <PaymentIcon /> },
    { id: 'subscriptions', label: 'Subscriptions', icon: <SubscriptionIcon /> },
    { id: 'profile', label: 'Account Details', icon: <PersonIcon /> },
  ];

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'delivered':
      case 'completed':
      case 'active':
        return 'success';
      case 'processing':
      case 'pending':
        return 'warning';
      case 'shipped':
        return 'info';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const renderDashboard = () => (
    <Box sx={{ width: '100%' }}>
      <Typography 
        variant={isSmall ? "h5" : "h4"} 
        gutterBottom 
        sx={{ 
          fontWeight: 'bold', 
          color: theme.palette.primary.main,
          textAlign: { xs: 'center', sm: 'left' },
          mb: 2
        }}
      >
        Welcome back, John Doe!
      </Typography>
      <Typography 
        variant="body1" 
        color="text.secondary" 
        sx={{ 
          mb: 4,
          textAlign: { xs: 'center', sm: 'left' },
          fontSize: { xs: '0.875rem', sm: '1rem' }
        }}
      >
        From your account dashboard you can view your recent orders, manage your shipping and billing addresses, and edit your password and account details.
      </Typography>
      
      <Grid container spacing={{ xs: 2, sm: 3 }} justifyContent="center">
        <Grid item xs={12} sm={6} lg={4}>
          <Card sx={{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
            color: 'white',
            height: '100%',
            minHeight: { xs: 120, sm: 140 }
          }}>
            <CardContent sx={{ 
              display: 'flex', 
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              p: { xs: 2, sm: 3 }
            }}>
              <OrdersIcon sx={{ fontSize: { xs: 32, sm: 40 }, mr: 2 }} />
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant={isSmall ? "h5" : "h4"} fontWeight="bold">12</Typography>
                <Typography variant="body2">Total Bookings</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} lg={4}>
          <Card sx={{ 
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', 
            color: 'white',
            height: '100%',
            minHeight: { xs: 120, sm: 140 }
          }}>
            <CardContent sx={{ 
              display: 'flex', 
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              p: { xs: 2, sm: 3 }
            }}>
              <PaymentIcon sx={{ fontSize: { xs: 32, sm: 40 }, mr: 2 }} />
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant={isSmall ? "h5" : "h4"} fontWeight="bold">$1,234</Typography>
                <Typography variant="body2">Total Spent</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} lg={4}>
          <Card sx={{ 
            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', 
            color: 'white',
            height: '100%',
            minHeight: { xs: 120, sm: 140 }
          }}>
            <CardContent sx={{ 
              display: 'flex', 
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              p: { xs: 2, sm: 3 }
            }}>
              <SubscriptionIcon sx={{ fontSize: { xs: 32, sm: 40 }, mr: 2 }} />
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant={isSmall ? "h5" : "h4"} fontWeight="bold">2</Typography>
                <Typography variant="body2">Active Subscriptions</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderOrders = () => (
    <Box sx={{ width: '100%' }}>
      <Typography 
        variant={isSmall ? "h5" : "h4"} 
        gutterBottom 
        sx={{ 
          fontWeight: 'bold', 
          color: '#2e7d32',
          textAlign: { xs: 'center', sm: 'left' },
          mb: 3
        }}
      >
        My Orders
      </Typography>
      <TableContainer 
        component={Paper} 
        sx={{ 
          mt: 3, 
          boxShadow: 3,
          overflowX: 'auto',
          '& .MuiTable-root': {
            minWidth: { xs: 600, sm: 'auto' }
          }
        }}
      >
        <Table size={isSmall ? "small" : "medium"}>
          <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Order ID</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Date</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Status</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Total</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Items</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {orders.map((order) => (
              <TableRow key={order.id} hover>
                <TableCell sx={{ fontWeight: 'medium', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{order.id}</TableCell>
                <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{order.date}</TableCell>
                <TableCell>
                  <Chip 
                    label={order.status} 
                    color={getStatusColor(order.status)} 
                    size="small"
                    sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                  />
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: '#2e7d32', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{order.total}</TableCell>
                <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{order.items}</TableCell>
                <TableCell>
                  <Button 
                    variant="outlined" 
                    size="small" 
                    startIcon={<ViewIcon />}
                    sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                  >
                    View
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderPayments = () => (
    <Box sx={{ width: '100%' }}>
      <Typography 
        variant={isSmall ? "h5" : "h4"} 
        gutterBottom 
        sx={{ 
          fontWeight: 'bold', 
          color: '#2e7d32',
          textAlign: { xs: 'center', sm: 'left' },
          mb: 3
        }}
      >
        Payments
      </Typography>
      <TableContainer 
        component={Paper} 
        sx={{ 
          mt: 3, 
          boxShadow: 3,
          overflowX: 'auto',
          '& .MuiTable-root': {
            minWidth: { xs: 650, sm: 'auto' }
          }
        }}
      >
        <Table size={isSmall ? "small" : "medium"}>
          <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Payment ID</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Date</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Method</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Amount</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Status</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {payments.map((payment) => (
              <TableRow key={payment.id} hover>
                <TableCell sx={{ fontWeight: 'medium', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{payment.id}</TableCell>
                <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{payment.date}</TableCell>
                <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{payment.method}</TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: '#2e7d32', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{payment.amount}</TableCell>
                <TableCell>
                  <Chip 
                    label={payment.status} 
                    color={getStatusColor(payment.status)} 
                    size="small"
                    sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                  />
                </TableCell>
                <TableCell>
                  <Button 
                    variant="outlined" 
                    size="small" 
                    startIcon={<ViewIcon />}
                    sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                  >
                    View
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderSubscriptions = () => (
    <Box sx={{ width: '100%' }}>
      <Typography 
        variant={isSmall ? "h5" : "h4"} 
        gutterBottom 
        sx={{ 
          fontWeight: 'bold', 
          color: '#2e7d32',
          textAlign: { xs: 'center', sm: 'left' },
          mb: 3
        }}
      >
        Subscriptions
      </Typography>
      <TableContainer 
        component={Paper} 
        sx={{ 
          mt: 3, 
          boxShadow: 3,
          overflowX: 'auto',
          '& .MuiTable-root': {
            minWidth: { xs: 700, sm: 'auto' }
          }
        }}
      >
        <Table size={isSmall ? "small" : "medium"}>
          <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Subscription ID</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Service</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Status</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Next Billing</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Amount</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {subscriptions.map((subscription) => (
              <TableRow key={subscription.id} hover>
                <TableCell sx={{ fontWeight: 'medium', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{subscription.id}</TableCell>
                <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{subscription.service}</TableCell>
                <TableCell>
                  <Chip 
                    label={subscription.status} 
                    color={getStatusColor(subscription.status)} 
                    size="small"
                    sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                  />
                </TableCell>
                <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{subscription.nextBilling}</TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: '#2e7d32', fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>{subscription.amount}</TableCell>
                <TableCell>
                  <Button 
                    variant="outlined" 
                    size="small" 
                    startIcon={<ViewIcon />}
                    sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                  >
                    Manage
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderProfile = () => (
    <Box sx={{ width: '100%' }}>
      <Typography 
        variant={isSmall ? "h5" : "h4"} 
        gutterBottom 
        sx={{ 
          fontWeight: 'bold', 
          color: theme.palette.primary.main,
          textAlign: { xs: 'center', sm: 'left' },
          mb: 3
        }}
      >
        Account Details
      </Typography>
      <Card sx={{ mt: 3, boxShadow: 3 }}>
        <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
          <Box 
            display="flex" 
            alignItems="center" 
            mb={3}
            sx={{ 
              flexDirection: { xs: 'column', sm: 'row' },
              textAlign: { xs: 'center', sm: 'left' }
            }}
          >
            <Avatar sx={{ 
              width: { xs: 60, sm: 80 }, 
              height: { xs: 60, sm: 80 }, 
              mr: { xs: 0, sm: 3 },
              mb: { xs: 2, sm: 0 },
              bgcolor: theme.palette.primary.main 
            }}>
              JD
            </Avatar>
            <Box>
              <Typography variant={isSmall ? "h6" : "h5"} fontWeight="bold">John Doe</Typography>
              <Typography color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}><EMAIL></Typography>
              <Typography color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>Member since: January 2024</Typography>
            </Box>
          </Box>
          <Divider sx={{ my: 2 }} />
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">Phone</Typography>
              <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>+****************</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">Address</Typography>
              <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>123 Main St, City, State 12345</Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'orders':
        return renderOrders();
      case 'payments':
        return renderPayments();
      case 'subscriptions':
        return renderSubscriptions();
      case 'profile':
        return renderProfile();
      default:
        return renderDashboard();
    }
  };

  const sidebarContent = (
    <Box sx={{ 
      width: { xs: 280, sm: 300 }, 
      height: '100%', 
      bgcolor: '#fff',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <Box sx={{ 
        p: { xs: 2, sm: 3 }, 
        borderBottom: '1px solid #e0e0e0',
        flexShrink: 0
      }}>
        <Box display="flex" alignItems="center">
          <Avatar sx={{ 
            width: { xs: 40, sm: 50 }, 
            height: { xs: 40, sm: 50 }, 
            mr: 2, 
            bgcolor: theme.palette.primary.main 
          }}>
            JD
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>John Doe</Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}><EMAIL></Typography>
          </Box>
        </Box>
      </Box>
      
      <List sx={{ p: 0, flexGrow: 1 }}>
        {menuItems.map((item) => (
          <ListItem
            key={item.id}
            button
            onClick={() => {
              setActiveSection(item.id);
              if (isMobile) setMobileOpen(false);
            }}
            sx={{
              py: { xs: 1, sm: 1.5 },
              px: { xs: 2, sm: 3 },
              backgroundColor: activeSection === item.id ? '#e8f5e8' : 'transparent',
              borderRight: activeSection === item.id ? `3px solid ${theme.palette.primary.main}` : 'none',
              '&:hover': {
                backgroundColor: '#f5f5f5',
              },
            }}
          >
            <ListItemIcon sx={{ 
              color: activeSection === item.id ? theme.palette.primary.main : 'inherit',
              minWidth: { xs: 40, sm: 56 }
            }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText 
              primary={item.label} 
              sx={{ 
                '& .MuiListItemText-primary': { 
                  fontWeight: activeSection === item.id ? 'bold' : 'normal',
                  color: activeSection === item.id ? theme.palette.primary.main : 'inherit',
                  fontSize: { xs: '0.875rem', sm: '1rem' }
                } 
              }} 
            />
          </ListItem>
        ))}
        
        <Divider sx={{ my: 1 }} />
        
        <ListItem 
          button 
          sx={{ 
            py: { xs: 1, sm: 1.5 }, 
            px: { xs: 2, sm: 3 }, 
            '&:hover': { backgroundColor: '#f5f5f5' } 
          }}
        >
          <ListItemIcon sx={{ minWidth: { xs: 40, sm: 56 } }}>
            <LogoutIcon />
          </ListItemIcon>
          <ListItemText 
            primary="Log out" 
            sx={{
              '& .MuiListItemText-primary': {
                fontSize: { xs: '0.875rem', sm: '1rem' }
              }
            }}
            onClick={handleLogout}
          />
        </ListItem>
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', bgcolor: '#f8f9fa' }}>
      <CssBaseline />
      
      {isMobile && (
        <AppBar 
        className='Hello'
          position="fixed" 
          sx={{ 
            bgcolor: 'white', 
            color: 'black', 
            boxShadow: 1,
            // zIndex: theme.zIndex.drawer + 1,
            top:'100px'
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div">
              My Account
            </Typography>
          </Toolbar>
        </AppBar>
      )}

      {/* Sidebar */}
      <Box
        component="nav"
        sx={{ 
          width: { md: 300 }, 
          flexShrink: { md: 0 }
        }}
      >
        {isMobile ? (
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{ keepMounted: true }}
            sx={{
              '& .MuiDrawer-paper': { 
                boxSizing: 'border-box', 
                width: { xs: 280, sm: 300 },
                mt:4,
                zIndex:100
              },
            }}
          >
            {sidebarContent}
          </Drawer>
        ) : (
          <Drawer
            variant="permanent"
            sx={{
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: 300,
                position: 'relative',
                boxShadow: 2,
                height: '100vh'
              },
            }}
            open
          >
            {sidebarContent}
          </Drawer>
        )}
      </Box>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          p: { xs: 2, sm: 1 },
          mt: { xs: 8, md: 0 },
          width: { md: `calc(100% - 300px)` }
        }}
      >
        <Container 
          maxWidth="xl" 
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-start',
            minHeight: { xs: 'calc(100vh - 120px)', md: '100vh' },
            py: { xs: 2, sm: 4 }
          }}
        >
          <Box sx={{ 
            width: '100%', 
            maxWidth: '1200px',
            display: 'flex',
            // justifyContent: 'center',
            // alignItems: 'center'
          }}>
            {renderContent()}
          </Box>
        </Container>
      </Box>
    </Box>
  );
}