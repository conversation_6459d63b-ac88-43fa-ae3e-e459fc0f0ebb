import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Alert,
  Stack,
  Divider,
  useTheme,
  useMediaQuery,
  Paper,
  IconButton,
  CircularProgress,
  Snackbar,
} from "@mui/material";
import {
  DatePicker,
  TimePicker,
  LocalizationProvider,
  PickersDay,
} from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import {
  ArrowBack,
  ExpandMore,
  Schedule,
  Restaurant,
  AttachMoney,
  LocationOn,
  CreditCard,
  Person,
  CalendarToday,
} from "@mui/icons-material";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";

// EXTEND DAYJS WITH PLUGINS
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);

import {
  getChefAvailableDays,
  bookChef,
} from "../../../app/service/chef.service";
import { useSelector } from "react-redux";
import { selectUser } from "../../../reducers/auth.reducer";

const Booking = () => {
  const customer = useSelector(selectUser); //for customer ID = customer.id
  const { id } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  // States
  const [loading, setLoading] = useState(true);
  const [bookingLoading, setBookingLoading] = useState(false);
  const [chefData, setChefData] = useState(null);
  const [bookingType, setBookingType] = useState("hourly");
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);
  const [selectedMenus, setSelectedMenus] = useState([]);
  const [address, setAddress] = useState("");
  const [totalPrice, setTotalPrice] = useState(0);
  const [errors, setErrors] = useState({});
  const [bookingNote, setBookingNote] = useState(""); // ADD THIS

  // Fetch chef availability data
  const fetchChefData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getChefAvailableDays(id);
      setChefData(response.data);
    } catch (error) {
      console.error("Error fetching chef data:", error);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchChefData();
  }, [fetchChefData]);

  // Date validation functions
  const isDateDisabled = useCallback(
    (date) => {
      if (!chefData) return true;

      const dateStr = dayjs(date).format("YYYY-MM-DD");
      const dayName = dayjs(date).format("dddd");

      // Check if it's a holiday
      const isHoliday = chefData.holidays?.some(
        (holiday) => holiday.date === dateStr
      );
      if (isHoliday) return true;

      // Check if chef is available on this day
      const isAvailableDay = chefData.availableDays?.some(
        (day) => day.day.toLowerCase() === dayName.toLowerCase()
      );
      if (!isAvailableDay) return true;

      // UPDATED LOGIC FOR DIFFERENT BOOKING TYPES
      if (bookingType === "hourly") {
        // For hourly bookings, check if entire day is fully booked
        const dayBookings =
          chefData.existingBookings?.filter(
            (booking) =>
              dayjs(booking.from).isSame(dayjs(date), "day") &&
              booking.type === "hourly"
          ) || [];

        // If there are daily/fixed bookings on this day, disable it
        const isDayFullyBooked = chefData.existingBookings?.some((booking) => {
          if (booking.type === "daily" || booking.type === "fixed") {
            const bookingStart = dayjs(booking.from);
            const bookingEnd = dayjs(booking.to);
            const currentDate = dayjs(date);

            return (
              currentDate.isSameOrAfter(bookingStart, "day") &&
              currentDate.isSameOrBefore(bookingEnd, "day")
            );
          }
          return false;
        });

        if (isDayFullyBooked) return true;

        // Check if all hourly slots are taken
        const availableDay = chefData.availableDays?.find(
          (day) => day.day.toLowerCase() === dayName.toLowerCase()
        );

        if (availableDay && dayBookings.length > 0) {
          const startTime = dayjs(`2000-01-01 ${availableDay.startTime}`);
          const endTime = dayjs(`2000-01-01 ${availableDay.endTime}`);
          const totalAvailableMinutes = endTime.diff(startTime, "minute");

          const totalBookedMinutes = dayBookings.reduce((total, booking) => {
            if (booking.startTime && booking.endTime) {
              const bookingStart = dayjs(`2000-01-01 ${booking.startTime}`);
              const bookingEnd = dayjs(`2000-01-01 ${booking.endTime}`);
              return total + bookingEnd.diff(bookingStart, "minute");
            }
            return total;
          }, 0);

          if (totalBookedMinutes >= totalAvailableMinutes) return true;
        }
      }

      // For daily/fixed bookings, disable dates that fall within ANY existing booking
      if (bookingType === "daily" || bookingType === "fixed") {
        const isDayBooked = chefData.existingBookings?.some((booking) => {
          const bookingStart = dayjs(booking.from);
          const bookingEnd = dayjs(booking.to);
          const currentDate = dayjs(date);

          // Check if the date falls within ANY booking range (hourly, daily, or fixed)
          return (
            currentDate.isSameOrAfter(bookingStart, "day") &&
            currentDate.isSameOrBefore(bookingEnd, "day")
          );
        });

        if (isDayBooked) return true;
      }

      return false;
    },
    [chefData, bookingType]
  );

  // Get available time slots for a specific date
  const getAvailableTimeSlots = useCallback(
    (date) => {
      if (!chefData || !date) return { startTime: null, endTime: null };

      const dayName = dayjs(date).format("dddd");
      const availableDay = chefData.availableDays?.find(
        (day) => day.day.toLowerCase() === dayName.toLowerCase()
      );

      if (!availableDay) return { startTime: null, endTime: null };

      return {
        startTime: availableDay.startTime,
        endTime: availableDay.endTime,
      };
    },
    [chefData]
  );

  // Time validation
  const isTimeDisabled = useCallback(
    (time, type) => {
      if (!chefData || !fromDate) return false;

      const timeStr = dayjs(time).format("HH:mm");

      // Get available time slots for the date
      const { startTime: availableStartTime, endTime: availableEndTime } =
        getAvailableTimeSlots(fromDate);

      // Check if time is within available hours
      if (!availableStartTime || !availableEndTime) return true;
      if (timeStr < availableStartTime || timeStr > availableEndTime)
        return true;

      // Check against existing HOURLY bookings only
      const dayBookings =
        chefData.existingBookings?.filter(
          (booking) =>
            dayjs(booking.from).isSame(dayjs(fromDate), "day") &&
            booking.type === "hourly" &&
            booking.startTime &&
            booking.endTime
        ) || [];

      // Check if time conflicts with existing hourly bookings
      const isTimeBooked = dayBookings.some((booking) => {
        const bookingStart = booking.startTime;
        const bookingEnd = booking.endTime;

        if (type === "start") {
          // For start time, check if it falls within any existing booking
          return timeStr >= bookingStart && timeStr < bookingEnd;
        } else if (type === "end") {
          // For end time, check if it overlaps with any existing booking
          return timeStr > bookingStart && timeStr <= bookingEnd;
        }

        return false;
      });

      if (isTimeBooked) return true;

      // For end time, ensure it's after start time
      if (type === "end" && startTime) {
        const startTimeStr = dayjs(startTime).format("HH:mm");
        if (timeStr <= startTimeStr) return true;
      }

      return false;
    },
    [chefData, fromDate, startTime, getAvailableTimeSlots]
  );

  const isDateRangeValid = useCallback(
    (startDate, endDate) => {
      if (!chefData || !startDate || !endDate) return false;

      let currentDate = dayjs(startDate);
      const end = dayjs(endDate);

      while (currentDate.isSameOrBefore(end, "day")) {
        // Check if ANY date in the range conflicts with existing bookings
        const hasConflict = chefData.existingBookings?.some((booking) => {
          const bookingStart = dayjs(booking.from);
          const bookingEnd = dayjs(booking.to);

          return (
            currentDate.isSameOrAfter(bookingStart, "day") &&
            currentDate.isSameOrBefore(bookingEnd, "day")
          );
        });

        if (hasConflict) return false;

        currentDate = currentDate.add(1, "day");
      }

      return true;
    },
    [chefData]
  );

  const isDateUnavailable = useCallback(
    (date) => {
      if (!chefData) return true;

      const dateStr = dayjs(date).format("YYYY-MM-DD");
      const dayName = dayjs(date).format("dddd");

      // Check if it's a holiday
      const isHoliday = chefData.holidays?.some(
        (holiday) => holiday.date === dateStr
      );
      if (isHoliday) return true;

      // Check if chef is available on this day
      const isAvailableDay = chefData.availableDays?.some(
        (day) => day.day.toLowerCase() === dayName.toLowerCase()
      );
      if (!isAvailableDay) return true;

      // FOR PRICE CALCULATION: Only check daily/fixed bookings
      // (Don't count hourly bookings as unavailable for daily/fixed pricing)
      const isDayBooked = chefData.existingBookings?.some((booking) => {
        if (booking.type === "daily" || booking.type === "fixed") {
          const bookingStart = dayjs(booking.from);
          const bookingEnd = dayjs(booking.to);
          const currentDate = dayjs(date);

          return (
            currentDate.isSameOrAfter(bookingStart, "day") &&
            currentDate.isSameOrBefore(bookingEnd, "day")
          );
        }
        return false;
      });

      return isDayBooked;
    },
    [chefData]
  );

  // ADD THIS NEW HELPER FUNCTION
  const getAvailableDaysInRange = useCallback(
    (startDate, endDate) => {
      if (!chefData || !startDate || !endDate) return 0;

      let availableDaysCount = 0;
      let currentDate = dayjs(startDate);
      const end = dayjs(endDate);

      while (currentDate.isSameOrBefore(end, "day")) {
        // Check if this date is available (not disabled)
        if (!isDateUnavailable(currentDate.toDate())) {
          availableDaysCount++;
        }
        currentDate = currentDate.add(1, "day");
      }

      return availableDaysCount;
    },
    [chefData, isDateUnavailable]
  );

  // ADD CUSTOM DAY RENDERING (OPTIONAL)
  const renderDay = (day, selectedDays, pickersDayProps) => {
  if (!chefData) return <PickersDay {...pickersDayProps} />;
  
  const dateStr = dayjs(day).format("YYYY-MM-DD");
  const dayName = dayjs(day).format("dddd");
  
  // Check different states
  const isHoliday = chefData.holidays?.some(holiday => holiday.date === dateStr);
  const isAvailableDay = chefData.availableDays?.some(
    availableDay => availableDay.day.toLowerCase() === dayName.toLowerCase()
  );
  
  // Check booking types for this date
  const hourlyBookings = chefData.existingBookings?.filter(booking => 
    dayjs(booking.from).isSame(dayjs(day), 'day') && booking.type === 'hourly'
  ) || [];
  
  const dailyFixedBookings = chefData.existingBookings?.filter(booking => {
    if (booking.type === "daily" || booking.type === "fixed") {
      const bookingStart = dayjs(booking.from);
      const bookingEnd = dayjs(booking.to);
      return dayjs(day).isSameOrAfter(bookingStart, 'day') && 
             dayjs(day).isSameOrBefore(bookingEnd, 'day');
    }
    return false;
  }) || [];

  let backgroundColor = 'transparent';
  let color = 'inherit';
  let tooltip = '';
  
  if (isHoliday) {
    backgroundColor = '#ffebee'; // Light red for holidays
    color = '#d32f2f';
    tooltip = 'Holiday';
  } else if (!isAvailableDay) {
    backgroundColor = '#f5f5f5'; // Light gray for unavailable days
    color = '#9e9e9e';
    tooltip = 'Chef not available';
  } else if (dailyFixedBookings.length > 0) {
    backgroundColor = '#ffcdd2'; // Red for fully booked
    color = '#d32f2f';
    tooltip = 'Fully booked';
  } else if (hourlyBookings.length > 0) {
    backgroundColor = '#fff3e0'; // Light orange for partially booked
    color = '#f57c00';
    tooltip = 'Partially booked';
  }

  return (
    <PickersDay
      {...pickersDayProps}
      title={tooltip}
      sx={{
        backgroundColor,
        color,
        '&:hover': {
          backgroundColor: backgroundColor !== 'transparent' ? backgroundColor : undefined,
        }
      }}
    />
  );
};

  // Price calculation
  const calculatePrice = useCallback(() => {
    if (!chefData) return 0;

    let price = 0;

    if (bookingType === "hourly" && startTime && endTime) {
      const minutes = dayjs(endTime).diff(dayjs(startTime), "minute");
      const hourlyRate = chefData.rates?.hourly || 0;
      price = (minutes / 60) * hourlyRate;
    } else if (bookingType === "daily" && fromDate && toDate) {
      const availableDays = getAvailableDaysInRange(fromDate, toDate);
      const dailyRate = chefData.rates?.daily?.[0]?.rate || 0;
      price = availableDays * dailyRate;
    } else if (
      bookingType === "fixed" &&
      selectedMenus.length > 0 &&
      fromDate
    ) {
      const availableDays = toDate
        ? getAvailableDaysInRange(fromDate, toDate)
        : 1;
      const menuPrice = selectedMenus.reduce(
        (total, menu) => total + menu.price,
        0
      );
      price = menuPrice * availableDays;
    }

    return Math.round(price * 100) / 100; // Round to 2 decimal places
  }, [
    chefData,
    bookingType,
    fromDate,
    toDate,
    startTime,
    endTime,
    selectedMenus,
    getAvailableDaysInRange,
  ]);

  useEffect(() => {
    setTotalPrice(calculatePrice());
  }, [calculatePrice]);

  // Handle date changes
  const handleFromDateChange = (date) => {
    setFromDate(date);
    if (bookingType === "hourly") {
      setToDate(date);
    } else if (toDate && dayjs(date).isAfter(dayjs(toDate))) {
      setToDate(null);
    }
    setStartTime(null);
    setEndTime(null);
  };

  const handleToDateChange = (date) => {
    // For daily/fixed bookings, validate the entire range
    if ((bookingType === "daily" || bookingType === "fixed") && fromDate) {
      if (!isDateRangeValid(fromDate, date)) {
        setErrors({
          ...errors,
          toDate: "Selected date range conflicts with existing bookings",
        });
        return;
      } else {
        // Clear the error if range is valid
        const newErrors = { ...errors };
        delete newErrors.toDate;
        setErrors(newErrors);
      }
    }

    setToDate(date);
  };

  // Handle booking type change
  const handleBookingTypeChange = (event) => {
    setBookingType(event.target.value);
    setFromDate(null);
    setToDate(null);
    setStartTime(null);
    setEndTime(null);
    setSelectedMenus([]);
    setErrors({});
  };

  // Handle menu selection
  const handleMenuSelection = (menu, persons) => {
    const menuPrice = menu.prices?.find((p) => p.persons === persons);
    if (menuPrice) {
      setSelectedMenus((prev) => {
        const existing = prev.find((m) => m.id === menu.id);
        if (existing) {
          return prev.map((m) =>
            m.id === menu.id ? { ...m, persons, price: menuPrice.price } : m
          );
        } else {
          return [
            ...prev,
            {
              id: menu.id,
              name: menu.name,
              persons,
              price: menuPrice.price,
            },
          ];
        }
      });
    }
  };

  // Handle form submission
  const handleBookNow = useCallback(async () => {
    // Validation
    const newErrors = {};
    if (!customer?.id) {
      newErrors.customer = "Please login to book a chef";
    }

    if (!fromDate) newErrors.fromDate = "Start date is required";
    if (bookingType !== "hourly" && !toDate)
      newErrors.toDate = "End date is required";
    if (bookingType === "hourly" && !startTime)
      newErrors.startTime = "Start time is required";
    if (bookingType === "hourly" && !endTime)
      newErrors.endTime = "End time is required";
    if (bookingType === "fixed" && selectedMenus.length === 0) {
      newErrors.menus = "Please select at least one menu";
    }
    if (!address) newErrors.address = "Address is required";

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) return;
    const availableDaysCount =
      (bookingType === "daily" || bookingType === "fixed") && toDate
        ? getAvailableDaysInRange(fromDate, toDate)
        : 1;

    // Prepare booking payload
    const bookingPayload = {
      customerId: customer?.id,
      chefId: parseInt(id),
      bookingType,
      fromDate: dayjs(fromDate).format("YYYY-MM-DD"),
      toDate: toDate
        ? dayjs(toDate).format("YYYY-MM-DD")
        : dayjs(fromDate).format("YYYY-MM-DD"),
      startTime: startTime ? dayjs(startTime).format("HH:mm") : null,
      endTime: endTime ? dayjs(endTime).format("HH:mm") : null,
      address,
      totalPrice,
      availableDaysCount,
      totalDaysSelected: toDate
        ? dayjs(toDate).diff(dayjs(fromDate), "day") + 1
        : 1,
      selectedMenus: bookingType === "fixed" ? selectedMenus : [],
      isHourlyBooking: bookingType === "hourly",
      isDayBooking: bookingType === "daily",
      isFixedBooking: bookingType === "fixed",
      bookingNote,
    };

    console.log("Booking Payload:", bookingPayload);

    // API Call
    try {
      setBookingLoading(true);
      const response = await bookChef(bookingPayload);

      if (response.responseCode === 1000) {
        // Success - show success message and redirect
        setSnackbar({
          open: true,
          message: response.message || "Booking created successfully!",
          severity: "success",
        });
        navigate(`/chef-profile/${id}`);
      } else {
        setSnackbar({
          open: true,
          message: response.message || "Failed to create booking.",
          severity: "error",
        });

        // Error from API
        setErrors({ api: response.message });
      }
    } catch (error) {
      console.error("Booking error:", error);
      setSnackbar({
        open: true,
        message: "Failed to create booking. Please try again.",
        severity: "error",
      });

      setErrors({ api: "Failed to create booking. Please try again." });
    } finally {
      setBookingLoading(false);
    }
  }, [
    customer?.id,
    id,
    bookingType,
    fromDate,
    toDate,
    startTime,
    endTime,
    address,
    totalPrice,
    selectedMenus,
    bookingNote,
    getAvailableDaysInRange,
    navigate,
  ]);

  const handleCancel = () => {
    navigate(`/chef-profile/${id}`);
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh",
        }}
      >
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box
        sx={{ bgcolor: "#f8f9fa", minHeight: "100vh", py: { xs: 2, md: 4 } }}
      >
        <Container maxWidth="md">
          {/* Header */}
          <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
            <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
              <ArrowBack />
            </IconButton>
            <Typography variant="h4" fontWeight="bold">
              Book Chef
            </Typography>
          </Box>

          {errors.api && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {errors.api}
            </Alert>
          )}

          <Card sx={{ borderRadius: 2, mb: 3 }}>
            <CardContent sx={{ p: { xs: 2, md: 4 } }}>
              {/* Booking Type Selection */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                  Select Booking Type
                </Typography>
                <FormControl component="fieldset">
                  <RadioGroup
                    row={!isMobile}
                    value={bookingType}
                    onChange={handleBookingTypeChange}
                  >
                    <FormControlLabel
                      value="hourly"
                      control={<Radio />}
                      label={
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Schedule sx={{ mr: 1 }} />
                          <Box>
                            <Typography variant="body2" fontWeight="600">
                              Hourly Booking
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              LKR {chefData?.rates?.hourly || 0}/hour
                            </Typography>
                          </Box>
                        </Box>
                      }
                    />
                    <FormControlLabel
                      value="daily"
                      control={<Radio />}
                      label={
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <CalendarToday sx={{ mr: 1 }} />
                          <Box>
                            <Typography variant="body2" fontWeight="600">
                              Daily Booking
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              LKR {chefData?.rates?.daily?.[0]?.rate || 0}/day
                            </Typography>
                          </Box>
                        </Box>
                      }
                    />
                    <FormControlLabel
                      value="fixed"
                      control={<Radio />}
                      label={
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Restaurant sx={{ mr: 1 }} />
                          <Box>
                            <Typography variant="body2" fontWeight="600">
                              Fixed Menu
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              Pre-defined menus
                            </Typography>
                          </Box>
                        </Box>
                      }
                    />
                  </RadioGroup>
                </FormControl>
              </Box>

              {/* Booking Hints */}
              <Alert severity="info" sx={{ mb: 3 }}>
                {bookingType === "hourly" &&
                  "For hourly booking, select the same date in both date pickers to enable time selection."}
                {bookingType === "daily" &&
                  "For daily booking, select different dates to book multiple days."}
                {bookingType === "fixed" &&
                  "Select fixed menus and dates. You can book one or multiple days."}
              </Alert>

              {/* Date Selection */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="From Date"
                    value={fromDate}
                    onChange={handleFromDateChange}
                    shouldDisableDate={isDateDisabled}
                    renderDay={renderDay}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        error={!!errors.fromDate}
                        helperText={errors.fromDate}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="To Date"
                    value={toDate}
                    onChange={handleToDateChange}
                    disabled={!fromDate || bookingType === "hourly"}
                    renderDay={renderDay}
                    shouldDisableDate={isDateDisabled}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        error={!!errors.toDate}
                        helperText={errors.toDate}
                      />
                    )}
                  />
                </Grid>
              </Grid>

              {/* Time Selection for Hourly Booking */}
              {bookingType === "hourly" &&
                fromDate &&
                dayjs(fromDate).isSame(dayjs(toDate || fromDate), "day") && (
                  <Grid container spacing={3} sx={{ mb: 3 }}>
                    <Grid item xs={12} md={6}>
                      <TimePicker
                        label="Start Time"
                        value={startTime}
                        onChange={setStartTime}
                        shouldDisableTime={(value) =>
                          isTimeDisabled(value, "start")
                        }
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            fullWidth
                            error={!!errors.startTime}
                            helperText={errors.startTime}
                          />
                        )}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TimePicker
                        label="End Time"
                        value={endTime}
                        onChange={setEndTime}
                        disabled={!startTime}
                        shouldDisableTime={(value) =>
                          isTimeDisabled(value, "end")
                        }
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            fullWidth
                            error={!!errors.endTime}
                            helperText={errors.endTime}
                          />
                        )}
                      />
                    </Grid>
                  </Grid>
                )}

              {/* Fixed Menu Selection */}
              {bookingType === "fixed" &&
                chefData?.menus &&
                Object.keys(chefData.menus).length > 0 && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
                      Select Menus
                    </Typography>
                    {errors.menus && (
                      <Alert severity="error" sx={{ mb: 2 }}>
                        {errors.menus}
                      </Alert>
                    )}
                    <Stack spacing={2}>
                      {Object.entries(chefData.menus).map(
                        ([category, menus]) => (
                          <Box key={category}>
                            <Typography
                              variant="subtitle1"
                              fontWeight="600"
                              sx={{ mb: 1 }}
                            >
                              {category}
                            </Typography>
                            {menus.map((menu) => (
                              <Accordion key={menu.id}>
                                <AccordionSummary expandIcon={<ExpandMore />}>
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      width: "100%",
                                    }}
                                  >
                                    <Restaurant
                                      sx={{ mr: 1, color: "#FF9800" }}
                                    />
                                    <Typography
                                      variant="body1"
                                      fontWeight="600"
                                    >
                                      {menu.name}
                                    </Typography>
                                    {selectedMenus.find(
                                      (m) => m.id === menu.id
                                    ) && (
                                      <Chip
                                        label="Selected"
                                        size="small"
                                        color="primary"
                                        sx={{ ml: "auto", mr: 2 }}
                                      />
                                    )}
                                  </Box>
                                </AccordionSummary>
                                <AccordionDetails>
                                  <Grid container spacing={2}>
                                    <Grid item xs={12} md={6}>
                                      <Typography
                                        variant="subtitle2"
                                        sx={{ mb: 1 }}
                                      >
                                        Menu Items:
                                      </Typography>
                                      {menu.items?.map((item) => (
                                        <Box key={item.id} sx={{ mb: 1 }}>
                                          <Typography
                                            variant="body2"
                                            fontWeight="500"
                                          >
                                            {item.name}
                                          </Typography>
                                          {item.description && (
                                            <Typography
                                              variant="caption"
                                              color="text.secondary"
                                            >
                                              {item.description}
                                            </Typography>
                                          )}
                                        </Box>
                                      ))}
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                      <Typography
                                        variant="subtitle2"
                                        sx={{ mb: 1 }}
                                      >
                                        Select Persons & Price:
                                      </Typography>
                                      <Stack spacing={1}>
                                        {menu.prices?.map((price) => (
                                          <Button
                                            key={price.id}
                                            variant={
                                              selectedMenus.find(
                                                (m) =>
                                                  m.id === menu.id &&
                                                  m.persons === price.persons
                                              )
                                                ? "contained"
                                                : "outlined"
                                            }
                                            onClick={() =>
                                              handleMenuSelection(
                                                menu,
                                                price.persons
                                              )
                                            }
                                            sx={{
                                              justifyContent: "space-between",
                                              textTransform: "none",
                                            }}
                                          >
                                            <Box
                                              sx={{
                                                display: "flex",
                                                alignItems: "center",
                                              }}
                                            >
                                              <Person
                                                sx={{ mr: 1, fontSize: 18 }}
                                              />
                                              <Typography variant="body2">
                                                {price.persons} person
                                                {price.persons > 1 ? "s" : ""}
                                              </Typography>
                                            </Box>
                                            <Typography
                                              variant="body2"
                                              fontWeight="600"
                                            >
                                              LKR {price.price}
                                            </Typography>
                                          </Button>
                                        ))}
                                      </Stack>
                                    </Grid>
                                  </Grid>
                                </AccordionDetails>
                              </Accordion>
                            ))}
                          </Box>
                        )
                      )}
                    </Stack>
                  </Box>
                )}
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  label="Booking Note (Optional)"
                  multiline
                  rows={2}
                  value={bookingNote}
                  onChange={(e) => setBookingNote(e.target.value)}
                  placeholder="Add any special requests or notes for the chef..."
                />
              </Box>

              {/* Address Input */}
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  label="Address"
                  multiline
                  rows={3}
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  error={!!errors.address}
                  helperText={errors.address}
                  placeholder="Enter your complete address where the chef should provide service..."
                  InputProps={{
                    startAdornment: (
                      <LocationOn sx={{ mr: 1, color: "text.secondary" }} />
                    ),
                  }}
                />
              </Box>

              {/* Price Summary */}
              {totalPrice > 0 && (
                <Paper
                  sx={{
                    p: 3,
                    mb: 3,
                    bgcolor: "#f8f9fa",
                    border: "1px solid #e0e0e0",
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                    <AttachMoney sx={{ mr: 1, color: "#FF9800" }} />
                    <Typography variant="h6" fontWeight="600">
                      Price Summary
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />

                  {bookingType === "hourly" && startTime && endTime && (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        mb: 1,
                      }}
                    >
                      <Typography variant="body2">
                        Duration:{" "}
                        {Math.floor(
                          dayjs(endTime).diff(dayjs(startTime), "minute") / 60
                        )}
                        h {dayjs(endTime).diff(dayjs(startTime), "minute") % 60}
                        m
                      </Typography>
                      <Typography variant="body2">
                        @ LKR {chefData?.rates?.hourly || 0}/hour
                      </Typography>
                    </Box>
                  )}

                  {bookingType === "daily" && fromDate && toDate && (
                    <>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          mb: 1,
                        }}
                      >
                        <Typography variant="body2">
                          Total Days Selected:{" "}
                          {dayjs(toDate).diff(dayjs(fromDate), "day") + 1}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          mb: 1,
                        }}
                      >
                        <Typography variant="body2" color="success.main">
                          Available Days:{" "}
                          {getAvailableDaysInRange(fromDate, toDate)}
                        </Typography>
                        <Typography variant="body2">
                          @ LKR {chefData?.rates?.daily?.[0]?.rate || 0}/day
                        </Typography>
                      </Box>
                      {dayjs(toDate).diff(dayjs(fromDate), "day") + 1 !==
                        getAvailableDaysInRange(fromDate, toDate) && (
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            mb: 1,
                          }}
                        >
                          <Typography variant="body2" color="warning.main">
                            Unavailable Days:{" "}
                            {dayjs(toDate).diff(dayjs(fromDate), "day") +
                              1 -
                              getAvailableDaysInRange(fromDate, toDate)}{" "}
                            (Not charged)
                          </Typography>
                        </Box>
                      )}
                    </>
                  )}

                  {bookingType === "fixed" && selectedMenus.length > 0 && (
                    <Box>
                      {selectedMenus.map((menu) => (
                        <Box
                          key={menu.id}
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            mb: 1,
                          }}
                        >
                          <Typography variant="body2">
                            {menu.name} ({menu.persons} persons)
                          </Typography>
                          <Typography variant="body2">
                            LKR {menu.price}
                          </Typography>
                        </Box>
                      ))}
                      {fromDate && toDate && (
                        <>
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              mb: 1,
                            }}
                          >
                            <Typography variant="body2">
                              Total Days Selected:{" "}
                              {dayjs(toDate).diff(dayjs(fromDate), "day") + 1}
                            </Typography>
                          </Box>
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              mb: 1,
                            }}
                          >
                            <Typography variant="body2" color="success.main">
                              Available Days:{" "}
                              {getAvailableDaysInRange(fromDate, toDate)}
                            </Typography>
                          </Box>
                          {dayjs(toDate).diff(dayjs(fromDate), "day") + 1 !==
                            getAvailableDaysInRange(fromDate, toDate) && (
                            <Box
                              sx={{
                                display: "flex",
                                justifyContent: "space-between",
                                mb: 1,
                              }}
                            >
                              <Typography variant="body2" color="warning.main">
                                Unavailable Days:{" "}
                                {dayjs(toDate).diff(dayjs(fromDate), "day") +
                                  1 -
                                  getAvailableDaysInRange(
                                    fromDate,
                                    toDate
                                  )}{" "}
                                (Not charged)
                              </Typography>
                            </Box>
                          )}
                        </>
                      )}
                    </Box>
                  )}

                  <Divider sx={{ my: 2 }} />
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <Typography variant="h6" fontWeight="bold">
                      Total Amount:
                    </Typography>
                    <Typography variant="h5" fontWeight="bold" color="primary">
                      LKR {totalPrice}
                    </Typography>
                  </Box>
                </Paper>
              )}

              {/* Payment Methods */}
              <Box sx={{ mb: 4 }}>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mb: 2 }}
                >
                  We accept:
                </Typography>
                <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
                  <Chip
                    icon={<CreditCard />}
                    label="Visa"
                    variant="outlined"
                    sx={{ bgcolor: "#f8f9fa" }}
                  />
                  <Chip
                    icon={<CreditCard />}
                    label="Mastercard"
                    variant="outlined"
                    sx={{ bgcolor: "#f8f9fa" }}
                  />
                  <Chip
                    icon={<CreditCard />}
                    label="American Express"
                    variant="outlined"
                    sx={{ bgcolor: "#f8f9fa" }}
                  />
                  <Chip
                    icon={<CreditCard />}
                    label="Discover"
                    variant="outlined"
                    sx={{ bgcolor: "#f8f9fa" }}
                  />
                </Box>
              </Box>

              {/* Action Buttons */}
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    size="large"
                    onClick={handleCancel}
                    disabled={bookingLoading}
                    sx={{ py: 1.5 }}
                  >
                    Cancel
                  </Button>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    size="large"
                    onClick={handleBookNow}
                    disabled={totalPrice === 0 || bookingLoading}
                    sx={{
                      py: 1.5,
                      bgcolor: "#FF9800",
                      "&:hover": { bgcolor: "#F57C00" },
                    }}
                  >
                    {bookingLoading ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      `Book Now - LKR ${totalPrice}`
                    )}
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Container>
      </Box>
      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </LocalizationProvider>
  );
};

export default Booking;
