import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Paper,
  IconButton,
  Divider,
  Alert,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Email,
  Phone,
  LocationOn,
  AccessTime,
  Send,
  Facebook,
  Twitter,
  Instagram,
  LinkedIn,
  RestaurantMenu
} from '@mui/icons-material';

export default function ContactUsPage() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  
  const [showSuccess, setShowSuccess] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission here
    setShowSuccess(true);
    setTimeout(() => setShowSuccess(false), 5000);
    // Reset form
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });
  };

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: '#f8f9fa' }}>
      {/* Header */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #FF4D00 0%, #ff7043 100%)',
          color: 'white',
          py: 8,
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
            <RestaurantMenu sx={{ fontSize: 60, mb: 2, opacity: 0.9 }} />
            <Typography variant="h2" component="h1" sx={{ 
              fontWeight: '600', 
              mb: 2,
              fontSize: { xs: '2rem', md: '3rem' }
            }}>
              Contact Us
            </Typography>
            <Typography variant="h5" sx={{ 
              opacity: 0.9,
              fontWeight: 500,
              maxWidth: '600px',
              mx: 'auto',
              fontSize: { xs: '1.1rem', md: '1.25rem' }
            }}>
              Get in touch with us to find the perfect chef for your culinary needs
            </Typography>
          </Box>
          
          {/* Background decoration */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              width: '200px',
              height: '200px',
              background: 'rgba(255,255,255,0.1)',
              borderRadius: '50%',
              transform: 'translate(50%, -50%)'
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              width: '150px',
              height: '150px',
              background: 'rgba(255,255,255,0.1)',
              borderRadius: '50%',
              transform: 'translate(-50%, 50%)'
            }}
          />
        </Container>
      </Box>

      <Container maxWidth="lg" sx={{ py: 6 }}>
        <Grid container spacing={4}>
          {/* Contact Information */}
          <Grid item xs={12} md={4}>
            <Box sx={{ mb: 4 }}>
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 3, color: '#333' }}>
                Get In Touch
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, color: '#666', lineHeight: 1.6 }}>
                We're here to help you find the perfect chef for any occasion. 
                Whether you need a personal chef, catering services, or culinary consultation, 
                our team is ready to assist you.
              </Typography>
            </Box>

            {/* Contact Cards */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              <Card sx={{ 
                borderLeft: '4px solid #FF4D00',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s',
                '&:hover': { transform: 'translateY(-2px)' }
              }}>
                <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{ 
                    backgroundColor: theme.palette.primary.main, 
                    color: 'white', 
                    p: 1.5, 
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Email />
                  </Box>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>
                      Email Us
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <EMAIL>
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <EMAIL>
                    </Typography>
                  </Box>
                </CardContent>
              </Card>

              <Card sx={{ 
                borderLeft: '4px solid #FF4D00',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s',
                '&:hover': { transform: 'translateY(-2px)' }
              }}>
                <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{ 
                    backgroundColor: theme.palette.primary.main, 
                    color: 'white', 
                    p: 1.5, 
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Phone />
                  </Box>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>
                      Call Us
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      +****************
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      +****************
                    </Typography>
                  </Box>
                </CardContent>
              </Card>

              <Card sx={{ 
                borderLeft: '4px solid #FF4D00',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s',
                '&:hover': { transform: 'translateY(-2px)' }
              }}>
                <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{ 
                    backgroundColor: theme.palette.primary.main, 
                    color: 'white', 
                    p: 1.5, 
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <LocationOn />
                  </Box>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>
                      Visit Us
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      123 Culinary Street
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Food District, NY 10001
                    </Typography>
                  </Box>
                </CardContent>
              </Card>

              <Card sx={{ 
                borderLeft: '4px solid #FF4D00',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s',
                '&:hover': { transform: 'translateY(-2px)' }
              }}>
                <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{ 
                    backgroundColor: '#FF4D00', 
                    color: 'white', 
                    p: 1.5, 
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <AccessTime />
                  </Box>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>
                      Business Hours
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Mon - Fri: 9:00 AM - 6:00 PM
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Sat - Sun: 10:00 AM - 4:00 PM
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Box>

            {/* Social Media */}
            <Box sx={{ mt: 4 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: '#333' }}>
                Follow Us
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {[
                  { icon: Facebook, color: '#1877F2' },
                  { icon: Twitter, color: '#1DA1F2' },
                  { icon: Instagram, color: '#E4405F' },
                  { icon: LinkedIn, color: '#0A66C2' }
                ].map((social, index) => (
                  <IconButton
                    key={index}
                    sx={{
                      backgroundColor: social.color,
                      color: 'white',
                      '&:hover': {
                        backgroundColor: social.color,
                        opacity: 0.8,
                        transform: 'scale(1.1)'
                      },
                      transition: 'all 0.2s'
                    }}
                  >
                    <social.icon />
                  </IconButton>
                ))}
              </Box>
            </Box>
          </Grid>

          {/* Contact Form */}
          <Grid item xs={12} md={8}>
            <Paper
              elevation={0}
              sx={{
                p: 4,
                backgroundColor: 'white',
                borderRadius: 3,
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                border: '1px solid #e0e0e0'
              }}
            >
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 3, color: '#333' }}>
                Send us a Message
              </Typography>
              
              {showSuccess && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  Thank you for your message! We'll get back to you within 24 hours.
                </Alert>
              )}

              <form onSubmit={handleSubmit}>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          '&:hover fieldset': {
                            borderColor: '#FF4D00',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#FF4D00',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#FF4D00',
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          '&:hover fieldset': {
                            borderColor: '#FF4D00',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#FF4D00',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#FF4D00',
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          '&:hover fieldset': {
                            borderColor: '#FF4D00',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#FF4D00',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#FF4D00',
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          '&:hover fieldset': {
                            borderColor: '#FF4D00',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#FF4D00',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#FF4D00',
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      multiline
                      rows={6}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          '&:hover fieldset': {
                            borderColor: '#FF4D00',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#FF4D00',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#FF4D00',
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      type="submit"
                      variant="contained"
                      size="large"
                      endIcon={<Send />}
                      sx={{
                        backgroundColor: theme.palette.primary.main,
                        color: 'white',
                        py: 1.5,
                        px: 4,
                        fontSize: '14px',
                        letterSpacing: 0.5,
                        fontWeight: 500,
                        borderRadius: 1,
                        textTransform: 'none',
                        boxShadow: '0 4px 16px rgba(255, 87, 34, 0.3)',
                        '&:hover': {
                          backgroundColor: theme.palette.primary.dark,
                          boxShadow: '0 6px 20px rgba(255, 87, 34, 0.4)',
                          transform: 'translateY(-2px)'
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      Send Message
                    </Button>
                  </Grid>
                </Grid>
              </form>
            </Paper>
          </Grid>
        </Grid>

        {/* FAQ Section */}
        <Box sx={{ mt: 8 }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 4, textAlign: 'center', color: '#333' }}>
            Frequently Asked Questions
          </Typography>
          <Grid container spacing={3}>
            {[
              {
                question: "How do I book a chef?",
                answer: "Simply browse our chef profiles, select your preferred chef, and book directly through our platform. You can also contact us for personalized recommendations."
              },
              {
                question: "What types of cuisine do your chefs specialize in?",
                answer: "Our chefs specialize in a wide variety of cuisines including French, Italian, Asian, Mediterranean, American, and many more. Each chef's profile lists their specialties."
              },
              {
                question: "How far in advance should I book?",
                answer: "We recommend booking at least 48 hours in advance, though many of our chefs can accommodate same-day requests depending on availability."
              },
              {
                question: "Do you provide ingredients and equipment?",
                answer: "This varies by chef and service type. Many chefs can provide ingredients and basic equipment, while others may require you to provide them. Details are specified in each chef's profile."
              }
            ].map((faq, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Card sx={{ 
                  height: '100%',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  transition: 'transform 0.2s',
                  '&:hover': { transform: 'translateY(-2px)' }
                }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: '#ff5722' }}>
                      {faq.question}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                      {faq.answer}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Container>
    </Box>
  );
}