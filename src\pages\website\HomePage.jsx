// src/pages/website/HomePage.jsx
import React, { useCallback, useEffect, useState } from "react";
import { Box } from "@mui/material";
import FilterSection from "../../components/website/sections/FilterSection";
import ChefCardsSection from "../../components/website/sections/ChefCardsSection";
import { searchChefs } from "../../app/service/chef.service";
const HomePage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    hourlyRate: [1, 5000],
    location: '',
    category: '',
  });
  const [chefData, setChefData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({});

  // API call with useCallback
  const fetchChefs = useCallback(async (searchParams = {}) => {
    try {
      setLoading(true);
      const response = await searchChefs(searchParams);
      if (response.responseCode === 1000) {
        setChefData(response.data.data);
        setPagination(response.data.pagination);
      }
    } catch (error) {
      console.error('Error fetching chefs:', error);
      setChefData([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load initial data
  useEffect(() => {
    fetchChefs({ page: 1, limit: 9 });
  }, [fetchChefs]);

  const handleSearch = useCallback((query, searchFilters) => {
    setSearchQuery(query);
    setFilters(searchFilters);
    
    const searchParams = {
      query,
      page: 1,
      limit: 9,
      minRate: searchFilters.hourlyRate[0],
      maxRate: searchFilters.hourlyRate[1],
      location: searchFilters.location,
      category: searchFilters.category,
      sortBy: 'recent'
    };
    
    fetchChefs(searchParams);
  }, [fetchChefs]);

  const handleFilterChange = useCallback((newFilters) => {
    setFilters(newFilters);
    
    const searchParams = {
      query: searchQuery,
      page: 1,
      limit: 9,
      minRate: newFilters.hourlyRate[0],
      maxRate: newFilters.hourlyRate[1],
      location: newFilters.location,
      category: newFilters.category,
      sortBy: 'recent'
    };
    
    fetchChefs(searchParams);
  }, [searchQuery, fetchChefs]);

  return (
    <Box>
      <FilterSection 
        onSearch={handleSearch}
        onFilterChange={handleFilterChange}
      />
      <ChefCardsSection 
        searchQuery={searchQuery}
        filters={filters}
        chefData={chefData}
        loading={loading}
        pagination={pagination}
        onPageChange={(page) => {
          const searchParams = {
            query: searchQuery,
            page,
            limit: 9,
            minRate: filters.hourlyRate[0],
            maxRate: filters.hourlyRate[1],
            location: filters.location,
            category: filters.category,
            sortBy: 'recent'
          };
          fetchChefs(searchParams);
        }}
        onSortChange={(sortBy) => {
          const searchParams = {
            query: searchQuery,
            page: 1,
            limit: 9,
            minRate: filters.hourlyRate[0],
            maxRate: filters.hourlyRate[1],
            location: filters.location,
            category: filters.category,
            sortBy
          };
          fetchChefs(searchParams);
        }}
      />
    </Box>
  );
};

export default HomePage;