import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Paper,
  Chip,
  useTheme,
  useMediaQuery,
  CardActions
} from '@mui/material';
import {
  RestaurantMenu,
  Check,
  Star,
  AccessTime,
  Restaurant,
  LocalOffer
} from '@mui/icons-material';

// Dummy data from your file
const membershipPlanData = [
  {
    id: 1,
    planName: "1 Month Plan",
    duration: "1 Month",
    price: 19.99,
  },
  {
    id: 2,
    planName: "3 Month Plan",
    duration: "3 Months",
    price: 49.99,
  },  
  {
    id: 3,
    planName: "6 Month Plan",
    duration: "6 Months",
    price: 89.99,
  },
];

export default function SubscriptionPage() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [selectedPlan, setSelectedPlan] = useState(null);

  const handlePlanSelect = (planId) => {
    setSelectedPlan(planId);
  };


  const getMostPopular = (planId) => {
    return planId === 2; // 3 Month Plan is most popular
  };

  const getSavings = (planId) => {
    if (planId === 2) return "Save $10";
    if (planId === 3) return "Save $30";
    return null;
  };

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: '#f8f9fa' }}>
      {/* Header */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #FF4D00 0%, #ff7043 100%)',
          color: 'white',
          py: 8,
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
            <RestaurantMenu sx={{ fontSize: 60, mb: 2, opacity: 0.9 }} />
            <Typography variant="h2" component="h1" sx={{ 
              fontWeight: '600', 
              mb: 2,
              fontSize: { xs: '2rem', md: '3rem' }
            }}>
              Choose Your Plan
            </Typography>
            <Typography variant="h5" sx={{ 
              opacity: 0.9,
              fontWeight: 500,
              maxWidth: '600px',
              mx: 'auto',
              fontSize: { xs: '1.1rem', md: '1.25rem' }
            }}>
              Unlock premium features and get the best chef hiring experience
            </Typography>
          </Box>
          
          {/* Background decoration */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              width: '200px',
              height: '200px',
              background: 'rgba(255,255,255,0.1)',
              borderRadius: '50%',
              transform: 'translate(50%, -50%)'
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              width: '150px',
              height: '150px',
              background: 'rgba(255,255,255,0.1)',
              borderRadius: '50%',
              transform: 'translate(-50%, 50%)'
            }}
          />
        </Container>
      </Box>

      <Container maxWidth="lg" sx={{ py: 6 }}>
        {/* Subscription Plans */}
        <Box sx={{ mb: 6 }}>
          <Typography variant="h4" sx={{ 
            fontWeight: 'bold', 
            mb: 2, 
            textAlign: 'center', 
            color: '#333' 
          }}>
            Membership Plans
          </Typography>
          <Typography variant="body1" sx={{ 
            textAlign: 'center', 
            color: '#666', 
            mb: 6,
            maxWidth: '600px',
            mx: 'auto',
            lineHeight: 1.6
          }}>
            Choose the perfect plan that suits your culinary needs. All plans include access to our premium chef network.
          </Typography>

          <Grid container spacing={4} justifyContent="center">
            {membershipPlanData.map((plan) => (
              <Grid item xs={12} sm={6} md={4} key={plan.id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    borderRadius: 3,
                    boxShadow: selectedPlan === plan.id 
                      ? '0 12px 40px rgba(255, 77, 0, 0.3)' 
                      : '0 8px 32px rgba(0,0,0,0.1)',
                    border: selectedPlan === plan.id ? '2px solid #FF4D00' : '1px solid #e0e0e0',
                    transition: 'all 0.3s ease',
                    transform: selectedPlan === plan.id ? 'scale(1.02)' : 'scale(1)',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 40px rgba(0,0,0,0.15)'
                    }
                  }}
                >
                  {/* Most Popular Badge */}
                  {getMostPopular(plan.id) && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: -10,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        zIndex: 1
                      }}
                    >
                      <Chip
                        icon={<Star />}
                        label="Most Popular"
                        sx={{
                          backgroundColor: '#FF4D00',
                          color: 'white',
                          fontWeight: 'bold',
                          '& .MuiChip-icon': {
                            color: 'white'
                          }
                        }}
                      />
                    </Box>
                  )}

                  {/* Savings Badge */}
                  {getSavings(plan.id) && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 16,
                        right: 16,
                        zIndex: 1
                      }}
                    >
                      <Chip
                        icon={<LocalOffer />}
                        label={getSavings(plan.id)}
                        size="small"
                        sx={{
                          backgroundColor: '#4caf50',
                          color: 'white',
                          fontWeight: 'bold',
                          '& .MuiChip-icon': {
                            color: 'white'
                          }
                        }}
                      />
                    </Box>
                  )}

                  <CardContent sx={{ flexGrow: 1, textAlign: 'center', pt: 4 }}>
                    {/* Plan Icon */}
                    <Box
                      sx={{
                        width: 80,
                        height: 80,
                        borderRadius: '50%',
                        backgroundColor: selectedPlan === plan.id ? '#FF4D00' : '#f5f5f5',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 3,
                        transition: 'all 0.3s ease'
                      }}
                    >
                      <Restaurant sx={{ 
                        fontSize: 40, 
                        color: selectedPlan === plan.id ? 'white' : '#FF4D00' 
                      }} />
                    </Box>

                    {/* Plan Name */}
                    <Typography variant="h5" sx={{ 
                      fontWeight: 'bold', 
                      mb: 1, 
                      color: '#333' 
                    }}>
                      {plan.planName}
                    </Typography>

                    {/* Duration */}
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
                      <AccessTime sx={{ fontSize: 20, color: '#666', mr: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        {plan.duration}
                      </Typography>
                    </Box>

                    {/* Price */}
                    <Box sx={{ mb: 4 }}>
                      <Typography variant="h3" sx={{ 
                        fontWeight: 'bold', 
                        color: '#FF4D00',
                        mb: 1
                      }}>
                        ${plan.price}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        per {plan.duration.toLowerCase()}
                      </Typography>
                    </Box>
                  </CardContent>

                  <CardActions sx={{ p: 3, pt: 0 }}>
                    {/* <Button
                      fullWidth
                      variant={selectedPlan === plan.id ? "contained" : "outlined"}
                      size="large"
                      onClick={() => handlePlanSelect(plan.id)}
                      sx={{
                        py: 1.5,
                        borderRadius: 2,
                        textTransform: 'none',
                        fontWeight: 'bold',
                        fontSize: '16px',
                        ...(selectedPlan === plan.id ? {
                          backgroundColor: '#FF4D00',
                          color: 'white',
                          boxShadow: '0 4px 16px rgba(255, 77, 0, 0.3)',
                          '&:hover': {
                            backgroundColor: '#e64100',
                            boxShadow: '0 6px 20px rgba(255, 77, 0, 0.4)'
                          }
                        } : {
                          borderColor: '#FF4D00',
                          color: '#FF4D00',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 77, 0, 0.04)',
                            borderColor: '#FF4D00'
                          }
                        })
                      }}
                    >
                      {selectedPlan === plan.id ? 'Selected' : 'Choose Plan'}
                    </Button> */}
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Features Overview */}
        <Paper
          elevation={0}
          sx={{
            p: 6,
            backgroundColor: 'white',
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
            border: '1px solid #e0e0e0',
            textAlign: 'center'
          }}
        >
          <Typography variant="h4" sx={{ 
            fontWeight: 'bold', 
            mb: 3, 
            color: '#333' 
          }}>
            Why Choose Our Platform?
          </Typography>
          <Typography variant="body1" sx={{ 
            color: '#666', 
            mb: 4,
            maxWidth: '800px',
            mx: 'auto',
            lineHeight: 1.7
          }}>
            Join thousands of satisfied customers who trust us to connect them with professional chefs. 
            Our platform offers the most comprehensive chef hiring experience with verified professionals, 
            secure payments, and exceptional customer service.
          </Typography>

          <Grid container spacing={4} sx={{ mt: 2 }}>
            {[
              {
                icon: <Check />,
                title: "Verified Chefs",
                description: "All our chefs are professionally verified and background checked"
              },
              {
                icon: <Star />,
                title: "Quality Guarantee",
                description: "100% satisfaction guarantee or your money back"
              },
              {
                icon: <AccessTime />,
                title: "24/7 Support",
                description: "Round-the-clock customer support for all your needs"
              }
            ].map((feature, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: '50%',
                      backgroundColor: '#FF4D00',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 2
                    }}
                  >
                    {React.cloneElement(feature.icon, { sx: { color: 'white', fontSize: 30 } })}
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1, color: '#333' }}>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                    {feature.description}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Paper>

        {/* Call to Action */}
        {selectedPlan && (
          <Box sx={{ textAlign: 'center', mt: 6 }}>
            <Paper
              elevation={0}
              sx={{
                p: 4,
                backgroundColor: '#FF4D00',
                borderRadius: 3,
                color: 'white'
              }}
            >
              <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 2 }}>
                Ready to get started?
              </Typography>
              <Typography variant="body1" sx={{ mb: 3, opacity: 0.9 }}>
                You've selected the {membershipPlanData.find(p => p.id === selectedPlan)?.planName}. 
                Complete your subscription to unlock all features.
              </Typography>
              <Button
                variant="contained"
                size="large"
                sx={{
                  backgroundColor: 'white',
                  color: '#FF4D00',
                  px: 4,
                  py: 1.5,
                  fontWeight: 'bold',
                  '&:hover': {
                    backgroundColor: '#f5f5f5'
                  }
                }}
              >
                Subscribe Now
              </Button>
            </Paper>
          </Box>
        )}
      </Container>
    </Box>
  );
}