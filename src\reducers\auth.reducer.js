import { createSlice } from '@reduxjs/toolkit';
import { update } from 'lodash';

const authSlice = createSlice({
    name: 'auth',
    initialState: {
        isLoggedIn: false,
        token: null,
        refreshToken: null,
        user: null,
        userType: null,
        admin: null,
        customer: null,
        chef: null,
    },
    reducers: {
        setCredentials: (state, action) => {
            const { user, token, refreshToken, admin, customer, chef, userType } = action.payload;
            state.user = user;
            state.token = token;
            state.refreshToken = refreshToken;
            state.isLoggedIn = true;
            state.userType = userType;
            state.admin = admin;
            state.customer = customer;
            state.chef = chef;
        },
        loginSuccess: (state, action) => {
            state.isLoggedIn = true;
            state.user = action.payload;
        },
        logOut: (state) => {
            state.user = null;
            state.token = null;
            state.refreshToken = null;
            state.isLoggedIn = false;
            state.userType = null;
            state.admin = null;
            state.customer = null;
            state.chef = null;
        },
        updateProfileImage: (state, action) => {
            const { profileImage, userType } = action.payload;
            console.log(userType)
            switch (userType) {
                case 'Admin':
                    if (state.admin) {

                        state.admin.profile_photo = profileImage;
                    }
                    break;
                case 'Gym User':
                    if (state.customer) {
                        state.customer.profile_photo = profileImage;

                    }
                    break;
                case 'Chef':
                    if (state.chef) {
                        console.log("It's here")
                        state.chef.profile_photo = profileImage;
                    }
                    break;
                default:
                    break;
            }
        },
        updateName: (state, action) => {
            const { firstName, lastName, userType } = action.payload
            switch (userType) {
                case 'Admin':
                    if (state.admin) {
                        state.admin.admin_first_name = firstName;
                        state.admin.admin_last_name = lastName;
                    }
                    break;
                case 'Customer':
                    if (state.customer) {
                        state.customer.customer_first_name = firstName;
                        state.customer.customer_last_name = lastName;
                    }
                    break;
                case 'Chef':
                    if (state.chef) {
                        state.chef.chef_first_name = firstName;
                        state.chef.chef_last_name = lastName;
                    }
                    break;
                default:
                    break;
            }
        },
        updateUsername: (state, action) => {
            const { username } = action.payload

            if (state.user) {
                state.user.user_name = username;
            }

        }
    },
});

export const { setCredentials, loginSuccess, logOut, updateProfileImage, updateName, updateUsername } = authSlice.actions;

export default authSlice.reducer;

export const selectCurrentUser = (state) => state.auth.user;
export const selectCurrentToken = (state) => state.auth.token;
export const selectIsLoggedIn = (state) => state.auth.isLoggedIn;
export const selectUserType = (state) => state.auth.userType;
export const selectUser = (state) => {
    const { userType } = state.auth;
    switch (userType) {
        case "Admin":
            return state.auth.admin;
        case "Customer":
            return state.auth.customer;
        case "Chef":
            return state.auth.chef;
        default:
            return null;
    }
};