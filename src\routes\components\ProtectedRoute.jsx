import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import PropTypes from "prop-types";
import { selectUserType } from "../../reducers/auth.reducer";

function ProtectedRoute({ children }) {
  // const user = useSelector((state) => state.auth.user);
  // const userType =useSelector(selectUserType)

  // const location = useLocation();

  // if (!user) {
  //   // User is not logged in
  //   return <Navigate to="/login" replace state={{ from: location }} />;
  // }

  // if (!allowedUserTypes.includes(userType)) {
  //   // User doesn't have the correct user type
  //   return <Navigate to="/unauthorized" replace />;
  // }

  return children;
}

ProtectedRoute.propTypes = {
  children: PropTypes.node.isRequired,
  // allowedUserTypes: PropTypes.arrayOf(PropTypes.string).isRequired,
};

export default ProtectedRoute;
