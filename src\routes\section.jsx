import React, { lazy, Suspense } from "react";
import { Navigate, useRoutes, Outlet } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { CircularProgress, Box } from "@mui/material";
import {
  loginSuccess,
  selectIsLoggedIn,
  selectUserType,
} from "../reducers/auth.reducer";
import { ThreeDot } from "react-loading-indicators";
import { useEffect } from "react";
import { useState } from "react";

// Layouts
const DashboardLayout = lazy(() => import("../layout/dashboard/index"));

// Components
const ProtectedRoute = lazy(() => import("./components/ProtectedRoute"));

// Pages

//Auth Pages
const Login = lazy(() => import("../pages/Login/Login"));
const Register = lazy(() => import("../pages/Auth/Register"));

const DashboardComponent = lazy(() => import("../pages/Dashboard/Dashboard"));
const WebsiteLayout = lazy(() => import("../layout/dashboard/WebsiteLayout"));
const ViewChefProfile = lazy(
  () => import("../components/website/sections/viewProfile")
);
const ContactUsPage = lazy(() => import("../pages/website/ContactUsPage"));
const Customers = lazy(() => import("../pages/Admin/Customer/Customers"));
const AllChefs = lazy(() => import("../pages/Admin/Chefs/AllChefs"));
const VerifiedChefs = lazy(() => import("../pages/Admin/Chefs/VerifiedChefs"));
const UnverifiedChefs = lazy(
  () => import("../pages/Admin/Chefs/UnverifiedChefs")
);
const AllMembershipPlans = lazy(
  () => import("../pages/Admin/Plans/AllMembershipPlan")
);
const NewMembershipPlan = lazy(
  () => import("../pages/Admin/Plans/NewMembershipPlan")
);
const EditMembershipPlan = lazy(
  () => import("../pages/Admin/Plans/EditMembershipPlan")
);
const AllUsers = lazy(() => import("../pages/Admin/Users/<USER>"));
const NewUser = lazy(() => import("../pages/Admin/Users/<USER>"));
const SubscriptionPayment = lazy(
  () => import("../pages/Admin/Payment/SubscriptionPayment")
);
const BookingPayment = lazy(
  () => import("../pages/Admin/Payment/BookingPayment")
);
const AllBookings = lazy(() => import("../pages/Admin/Bookings/AllBookings"));
const ViewUser = lazy(() => import("../pages/Admin/Users/<USER>"));
const WithdrawalRequests = lazy(
  () => import("../pages/Admin/Withdrawals/WithdrawalRequests")
);
const ChefWithdrawals = lazy(
  () => import("../pages/Admin/Withdrawals/ChefWithdrawals")
);
const AdminWithdrawals = lazy(
  () => import("../pages/Admin/Withdrawals/AdminWithdrawals")
);
const ViewChef = lazy(() => import("../pages/Admin/Chefs/ViewChef"));
const ViewCustomer = lazy(() => import("../pages/Admin/Customer/ViewCustomer"));
const MyProfile = lazy(() => import("../pages/Admin/Settings-Page/MyAccount"));
const PendingBookings = lazy(
  () => import("../pages/Chefs/Bookings/PendingBookings")
);
const ChefAllBookings = lazy(
  () => import("../pages/Chefs/Bookings/AllBookings")
);
const MyAccount = lazy(() => import("../pages/Chefs/MyProfile/MyProfile"));
const AllPayments = lazy(() => import("../pages/Chefs/Payments/AllPayments"));
const AllChefWithdrawRequests = lazy(() => import("../pages/Chefs/Withdrawals/WithdrawRequests"));
const AllWithdrawals = lazy(() => import("../pages/Chefs/Withdrawals/AllWithdrawals"));
const DashboardChef = lazy(() => import("../pages/Dashboard/DashboardChef/"));

// Website Pages
const HomePage = lazy(() => import("../pages/website/HomePage"));
const SubscriptionPage = lazy(
  () => import("../pages/website/SubscriptionPage")
);
const AboutPage = lazy(() => import("../pages/website/AboutPage"));
const AccountPage = lazy(() => import("../pages/website/AccountPage"));
const BookingPage = lazy(() => import("../pages/website/Bookings/Booking"));
const LoadingFallback = () => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    height="100vh"
  >
    <ThreeDot
      variant="pulsate"
      color="#000"
      size="medium"
      text=""
      textColor=""
    />
  </Box>
);

export default function Router() {
  const dispatch = useDispatch();
  const isLoggedIn = useSelector(selectIsLoggedIn);
  const userType = useSelector(selectUserType);

  console.log("userType", userType);
  const [isRehydrated, setIsRehydrated] = useState(true);
  useEffect(() => {
    // console.log("Is login : ", isLoggedIn);
    const rehydrateState = async () => {
      const persistedAuth = JSON.parse(
        localStorage.getItem("persist:root")
      )?.auth;
      if (persistedAuth) {
        const parsedAuth = JSON.parse(persistedAuth);
        if (parsedAuth.isLoggedIn) {
          await dispatch(loginSuccess(parsedAuth.user));
        }
      }
      setIsRehydrated(true);
    };
  
    rehydrateState();
  }, [dispatch]);

  const routes = useRoutes([
    {
      path: "/",
      element: (
        <Suspense fallback={<LoadingFallback />}>
          <WebsiteLayout />
        </Suspense>
      ),
      children: [
        { path: "", element: <HomePage /> },
        { path: "contact-us", element: <ContactUsPage /> },
        { path: "chef-profile/:id", element: <ViewChefProfile /> },
        { path: "chef-booking/:id", element: <BookingPage /> },
        { path: "subscription", element: <SubscriptionPage /> },
        { path: "about-us", element: <AboutPage /> },
        { path: "account", element: <AccountPage /> },
      ],
    },
    {
      path: "/auth",
      element: (
        <Suspense fallback={<LoadingFallback />}>
          <Outlet />
        </Suspense>
      ),
      children: [
        { path: "", element: <Navigate to="/auth/login" replace /> },
        {
          path: "login",
          element: (
            <Suspense fallback={<LoadingFallback />}>
              <Login />
            </Suspense>
          ),
        },
        {
          path: "register",
          element: (
            <Suspense fallback={<LoadingFallback />}>
              <Register />
            </Suspense>
          ),
        },
      ],
    },
    {
      path: "/system",
      element: (
        <ProtectedRoute>
          <Suspense fallback={<LoadingFallback />}>
            <DashboardLayout />
          </Suspense>
        </ProtectedRoute>
      ),
      children: [
        { path: "", element: <Navigate to="/system/dashboard" replace /> },
        {
          path: "dashboard",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                {userType === "Chef" ? <DashboardChef /> : <DashboardComponent />}
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Dashboard",
        },
        {
          path: "customers",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <Customers />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Customers",
        },
        {
          path: "customers/view/:customerId",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <ViewCustomer />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "View Customer",
        },
        {
          path: "chefs/all",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <AllChefs />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "All Chefs",
        },
        {
          path: "chefs/verified",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <VerifiedChefs />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Verified Chefs",
        },
        {
          path: "chefs/unverified",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <UnverifiedChefs />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Unverified Chefs",
        },
        {
          path: "chefs/view/:chefId",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <ViewChef />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "View Chefs",
        },
        {
          path: "plans/new-membership-plan",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <NewMembershipPlan />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "New Membership Plan",
        },
        {
          path: "plans/all-membership-plan",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <AllMembershipPlans />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "All Membership Plan",
        },
        {
          path: "plans/view/:planId",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <EditMembershipPlan />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "All Membership Plan",
        },
        {
          path: "users/all-users",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <AllUsers />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "All Users",
        },
        {
          path: "users/new-user",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <NewUser />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "New User",
        },
        {
          path: "users/view/:userId",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <ViewUser />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "New User",
        },
        {
          path: "payments/subscription-payment",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <SubscriptionPayment />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Payments",
        },
        {
          path: "payments/booking-payment",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <BookingPayment />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Booking Payments",
        },
        {
          path: "bookings",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <AllBookings />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Bookings",
        },
        {
          path: "withdrawals/requests",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <WithdrawalRequests />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Withdrawal Requests",
        },
        {
          path: "withdrawals/admin",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <AdminWithdrawals />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Admin Withdrawals",
        },
        {
          path: "withdrawals/chef",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <ChefWithdrawals />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Chef Withdrawals",
        },
        {
          path: "settings/my-profile",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <MyProfile />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "My Profile",
        },
        {
          path: "bookings/pending",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <PendingBookings />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "Pending Bookings",
        },
        {
          path: "bookings/all",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <ChefAllBookings />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "All Bookings",
        },
        {
          path: "my-profile",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <MyAccount />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "My Profile",
        },
        {
          path: "all-payments",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <AllPayments />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "All Payments",
        },
        {
          path: "withdrawals/requests-all",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <AllChefWithdrawRequests />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "All Chef Withdrawal Requests",
        },
        {
          path: "withdrawals/all",
          element: (
            <ProtectedRoute>
              <Suspense fallback={<LoadingFallback />}>
                <AllWithdrawals />
              </Suspense>
            </ProtectedRoute>
          ),
          index: true,
          title: "All Chef Withdrawals",
        },
      ],
    },
  ]);

  if (!isRehydrated) {
    return <LoadingFallback />;
  }

  return routes;
}
